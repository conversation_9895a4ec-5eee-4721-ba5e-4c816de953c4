import antfu from '@antfu/eslint-config';
import tailwind from 'eslint-plugin-tailwindcss';

export default antfu(
  {
    ignores: [
      'build',
      '**/build/**',
      'config/*.js',
      'config/*.js/**',
      'static/dist',
      'static/dist/**',
      'static/js',
      'static/js/**',
      'config',
      '**/config/**',
      'dist',
      '**/dist/**',
      '*.env*',
      '**/*.env*/**',
      'src/interface/generate.js',
      'src/interface/generate.js/**',
      'src/interface/swaggerApi.json',
      'src/interface/swaggerApi.json/**',
      '.eslintrc-auto-import.json',
      '**/.eslintrc-auto-import.json/**',
      '.prettierrc.js',
      '**/.prettierrc.js/**',
      '.tailwind.config.js',
      '**/.tailwind.config.js/**',
      'NIM_Web_SDK_v9.1.1.js',
      '**/NIM_Web_SDK_v9.1.1.js/**',
    ],
    formatters: true,
    vue: true,
    stylistic: {
      semi: true,
    },
  },
  ...tailwind.configs['flat/recommended']
);
