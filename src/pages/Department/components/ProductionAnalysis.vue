<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { useRouteQuery } from '@vueuse/router';
import dayjs from 'dayjs';
import { computed, ref } from 'vue';
import { fetchDeptInvestmentOverview, fetchDeptInvestmentTrend, fetchDeptOutputTrend } from '@/api';
import {
  BarLineChart,
  CardTitle,
  DoughnutChart,
  RadioGroup,
  StatisticText,
  Times,
} from '@/components';
import { MERGE_METHOD } from '@/constant';
import { formatDecimal } from '@/lib';

const mergeMethod = ref(MERGE_METHOD[0].id);

const deptId = useRouteQuery('id', '0', { transform: Number });
const deptType = useRouteQuery('type', '', { transform: String });
const startTime = ref<number | undefined>(undefined);
const endTime = ref<number | undefined>(undefined);

const queryEnabled = computed(() => {
  return !!deptId.value && !!startTime.value && !!endTime.value;
});

// 获取部门投产总览
const { data: investmentOverview } = useQuery({
  queryKey: ['investmentOverview', deptId, startTime, endTime, mergeMethod],
  queryFn: () =>
    fetchDeptInvestmentOverview({
      deptId: deptId.value,
      startTime: startTime.value,
      endTime: endTime.value,
      deptType: deptType.value,
    }),
  enabled: queryEnabled,
});

// 获取部门投入趋势
const { data: investmentTrend } = useQuery({
  queryKey: ['investmentTrend', deptId, startTime, endTime, mergeMethod],
  queryFn: () =>
    fetchDeptInvestmentTrend({
      deptId: deptId.value,
      startTime: startTime.value,
      endTime: endTime.value,
      mergeMethod: mergeMethod.value,
      deptType: deptType.value,
    }),
  enabled: queryEnabled,
});

// 获取部门产出趋势
const { data: outputTrend } = useQuery({
  queryKey: ['outputTrend', deptId, startTime, endTime, mergeMethod],
  queryFn: () =>
    fetchDeptOutputTrend({
      deptId: deptId.value,
      startTime: startTime.value,
      endTime: endTime.value,
      mergeMethod: mergeMethod.value,
      deptType: deptType.value,
    }),
  enabled: queryEnabled,
});

// 计算产出趋势图表数据
const outputTrendChartData = computed(() => {
  if (!outputTrend.value)
    return { xData: [], yData: [], orderData: [] };

  return {
    xData: outputTrend.value.map(item => item.key),
    yData: outputTrend.value.map(item => item.dataList[0].orderAmount),
    orderData: outputTrend.value.map(item => item.dataList[0].orderNum),
  };
});

/** 投入总金额 */
const investmentTotal = computed(() => {
  return (investmentOverview?.value?.totalFeeReimburse || 0) + (investmentOverview?.value?.totalCopyAmount || 0);
});

function handleDateChange([start, end]: [string, string]) {
  const currentStartTime = dayjs(start).startOf('day').valueOf();
  const currentEndTime = dayjs(end).endOf('day').valueOf();
  startTime.value = currentStartTime;
  endTime.value = currentEndTime;
}
</script>

<template>
  <div class="space-y-24">
    <div
      class="sticky top-0 z-20 flex items-center justify-between bg-white text-sm"
    >
      <Times @date-change="handleDateChange" />
      <div class="flex items-center gap-16">
        <RadioGroup
          v-model="mergeMethod"
          label="汇总方式"
          :data="MERGE_METHOD"
          size="mini"
        />
      </div>
    </div>
    <div class="space-y-16">
      <CardTitle title="投产总览" sub-title="单位：元" class="text-lg" />
      <div class="grid grid-cols-3 gap-16">
        <div class="col-span-2 grid grid-cols-3 rounded bg-[#F7F8FA] p-16">
          <StatisticText
            title="投入金额"
            :value="investmentTotal"
            size="large"
          />
          <div class="col-span-2">
            <p class="mb-16">
              投入占比
            </p>
            <DoughnutChart
              :radius="['70%', '100%']"
              :size="72"
              :data="investmentOverview?.investList?.map(item => ({
                name: item.investType,
                value: item.investAmount || 0,
              })) ?? []"
              :inner-label="false"
            />
          </div>
        </div>
        <div v-if="deptType === 'SALES'" class="flex justify-between rounded bg-[#F7F8FA] p-16">
          <StatisticText
            title="产出订单量（单）/产出金额"
            :value="`${investmentOverview?.totalOrderNum || 0}/${investmentOverview?.totalOutputAmount || 0}`"
            size="large"
          />
          <StatisticText
            title="部门投产比"
            :value="formatDecimal((investmentOverview?.totalOutputAmount ?? 0) / ((investmentOverview?.totalFeeReimburse || 0) + (investmentOverview?.totalCopyAmount || 0) || 1), 2)"
            size="large"
          />
        </div>
        <div v-else class="flex justify-between rounded bg-[#F7F8FA] p-16">
          <StatisticText
            title="产出PCI手术量"
            :value="`${investmentOverview?.pciSurgicalVolume || 0}`"
            size="large"
          />
          <StatisticText
            title="千台手术成本"
            :value="formatDecimal((investmentTotal ?? 0) / (investmentOverview?.pciSurgicalVolume || 1) * 1000, 2)"
            size="large"
          />
        </div>
      </div>
    </div>
    <div class="space-y-16">
      <CardTitle title="投入详情" class="text-lg" />
      <BarLineChart
        title="投入趋势"
        sub-title="单位：元"
        :show-legend="false"
        :colors="['#2FB324', '#2FB324', '#E37221']"
        :x-axis="{
          data: investmentTrend?.map(item => item.key) ?? [],
        }"
        :series="[
          {
            type: 'line',
            data: investmentTrend?.map(item => item.dataList?.map(item => item.investAmount).reduce((a, b) => a + b, 0) ?? 0) ?? [],
            name: '总金额',
          },
        ]"
        :grid="{
          left: 0,
          bottom: 0,
        }"
        :tooltip="{
          formatter: (params) => {
            const index = params[0].dataIndex;
            return `<strong>${params[0].name}</strong><br/>
              ${investmentTrend?.[index].dataList?.map(item => `${item.investType}: ${item.investAmount}<br>`).join('') || ''}
              总计：${investmentTrend?.[index]?.dataList?.map(item => item.investAmount).reduce((a, b) => a + b, 0) ?? 0}<br/>
            `;
          },
        }"
        :height="340"
      />
    </div>
    <div class="space-y-16">
      <CardTitle title="产出详情" class="text-lg" />
      <BarLineChart
        title="产出趋势"
        sub-title="单位：元"
        :show-legend="false"
        :colors="['#2FB324', '#2FB324', '#E37221']"
        :x-axis="{
          data: outputTrendChartData.xData,
        }"
        :series="[
          {
            type: 'line',
            data: outputTrendChartData.yData,
            name: '订单金额',
          },
        ]"
        :grid="{
          left: 0,
          bottom: 0,
        }"
        :height="340"
        :tooltip="{
          formatter: (params) => {
            const index = params[0].dataIndex;
            return `<strong>${params[0].name}</strong><br/>
              订单量：${outputTrendChartData.orderData[index]}<br/>
              订单金额：${outputTrendChartData.yData[index]}<br/>
            `;
          },
        }"
      />
    </div>
  </div>
</template>
