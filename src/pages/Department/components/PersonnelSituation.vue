<script setup lang="ts">
import type { DeptType } from '@/constant';
import type { DeptEmployeeItem } from '@/schema';
import { useQuery } from '@tanstack/vue-query';
import { useRouteQuery } from '@vueuse/router';
import { ElCheckbox, ElTableColumn } from 'element-plus';
import { ref, useTemplateRef, watch } from 'vue';
import { fetchEmployeeDist, fetchEmployeeList, fetchPositionLevel } from '@/api';
import {
  <PERSON><PERSON>itle,
  DoughnutChart,
  InfiniteTable,
  MatrixChart,
  PeopleDrawer,
  StatisticText,
} from '@/components';

const onlyWork = ref(true);
const osUserId = ref<number>(0);
const userId = ref<number>(0);
const show = ref<boolean>(false);
/** 部门🆔 */
const departmentId = useRouteQuery<number>('id', 0, { transform: Number });
/** 部门类型 */
const departmentType = useRouteQuery<DeptType>('type');
const tableRef = useTemplateRef('table');

function queryFn(params: { pageNumber: number; pageSize: number }) {
  return fetchEmployeeList({
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
    onlyWork: onlyWork.value,
    deptId: departmentId.value,
    deptType: departmentType.value,
  });
}

const { data: employeeDist } = useQuery({
  queryKey: ['employeeDist', departmentId],
  queryFn: () => fetchEmployeeDist(departmentId.value, departmentType.value),
});
const { data: positionLevel } = useQuery({
  queryKey: ['positionLevel', departmentId],
  queryFn: () => fetchPositionLevel(departmentId.value, departmentType.value),
});

watch([onlyWork, departmentId], () => {
  tableRef.value?.clear();
});

function handleOpenDrawer(row: DeptEmployeeItem) {
  osUserId.value = row.osUserId;
  userId.value = row.userId;
  show.value = true;
}
</script>

<template>
  <div class="space-y-16">
    <CardTitle title="员工分布" class="text-lg" />
    <div class="grid grid-cols-3 gap-16">
      <div class="grid grid-cols-2 bg-[#F7F8FA] p-16">
        <StatisticText title="在职员工" :value="employeeDist?.workEmployeeNum ?? 0" size="large" />
        <StatisticText title="正式员工" :value="employeeDist?.formalEmployeeNum ?? 0" size="large">
          <template #suffix>
            ({{ employeeDist?.formalEmployeeRate ?? 0 }}%)
          </template>
        </StatisticText>
      </div>
      <div class="col-span-2 grid grid-cols-4 gap-16 bg-[#F7F8FA] p-16">
        <StatisticText title="员工转正率" :value="employeeDist?.employeeCorrectionRate ?? 0" size="large">
          <template #suffix>
            <span>%</span>
          </template>
        </StatisticText>
        <StatisticText title="员工离职率" :value="employeeDist?.employeeLeaveRate ?? 0" size="large">
          <template #suffix>
            <span>%</span>
          </template>
        </StatisticText>
        <StatisticText
          title="PIP考核员工数"
          :value="employeeDist?.pipEmployeeNum ?? 0"
          size="large"
        />
        <StatisticText title="在招岗位数" :value="employeeDist?.recruitJobNum ?? 0" size="large" />
      </div>
    </div>
    <div class="grid grid-cols-2 gap-24">
      <div>
        <h3 class="mb-16 text-base font-medium text-[#15233F]">
          能力分布
        </h3>
        <MatrixChart :department-id="departmentId" :dept-type="departmentType" />
      </div>
      <div>
        <h3 class="mb-16 text-base font-medium text-[#15233F]">
          职级占比
        </h3>
        <div class="mt-84">
          <DoughnutChart
            :radius="['60%', '90%']"
            :size="144"
            :cols="1"
            :data="positionLevel?.map(item => ({
              value: item.num,
              name: item.positionLevel,
            })) ?? []"
            :inner-label="false"
          />
        </div>
      </div>
    </div>
    <CardTitle title="员工清单" class="text-lg" />
    <div class="space-y-8">
      <div class="flex items-center justify-end gap-8 text-sm">
        <ElCheckbox v-model="onlyWork" />
        <span class="text-[#323233]">仅看在职</span>
      </div>
      <InfiniteTable ref="table" :query-fn="queryFn">
        <ElTableColumn label="姓名" prop="employeeName" width="100" />
        <ElTableColumn label="岗位" prop="position" />
        <ElTableColumn label="职级" prop="positionLevel" width="100" />
        <ElTableColumn label="在岗时长" prop="workTime" width="100">
          <template #default="{ row }">
            <span>{{ row.workTime }}年</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="所在部门" prop="deptNameList" />
        <ElTableColumn label="状态" prop="employeeStatus" width="80" />
        <ElTableColumn label="操作" prop="name" fixed="right" width="80" align="center">
          <template #default="{ row }">
            <span
              class="cursor-pointer text-[#2E6BE6]"
              @click="handleOpenDrawer(row)"
            >
              详情
            </span>
          </template>
        </ElTableColumn>
      </InfiniteTable>
    </div>
    <PeopleDrawer
      v-model="show"
      :os-user-id="osUserId"
      :dept-type="departmentType"
      :user-id="userId"
      :dept-id="departmentId"
    />
  </div>
</template>
