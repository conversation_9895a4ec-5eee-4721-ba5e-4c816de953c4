<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { useRouteQuery } from '@vueuse/router';
import dayjs from 'dayjs';
import { computed, ref } from 'vue';
import { fetchDeptExamScoreTrend, fetchDeptPerformanceTrend, fetchDeptPerfScoreTrend, fetchDeptSubjScoreTrend } from '@/api';
import { BarLineChart, CardTitle, DatePicker } from '@/components';
import { DEPT_TYPE } from '@/constant';
import { formatDecimal, minShow } from '@/lib';

const deptId = useRouteQuery('id', '0', { transform: Number });
const deptType = useRouteQuery<string>('type');
const startTime = ref<number | undefined>(undefined);
const endTime = ref<number | undefined>(undefined);

const queryEnabled = computed(() => {
  return !!deptId.value && !!deptType.value && !!startTime.value && !!endTime.value;
});

const performanceTrendQueryEnabled = computed(() => {
  return queryEnabled.value && (deptType.value === DEPT_TYPE.SALES || deptType.value === DEPT_TYPE.MARKET);
});

const { data: performanceTrend } = useQuery({
  queryKey: ['performanceTrend', deptId, startTime, endTime],
  queryFn: () =>
    fetchDeptPerformanceTrend({
      deptId: deptId.value,
      startTime: dayjs(startTime.value).valueOf(),
      endTime: dayjs(endTime.value).valueOf(),
      deptType: deptType.value,
    }),
  enabled: performanceTrendQueryEnabled,
});

const { data: examScoreTrend } = useQuery({
  queryKey: ['考试成绩走势', deptId, startTime, endTime],
  queryFn: () =>
    fetchDeptExamScoreTrend({
      deptId: deptId.value,
      startTime: dayjs(startTime.value).valueOf(),
      endTime: dayjs(endTime.value).valueOf(),
      deptType: deptType.value,
    }),
  enabled: queryEnabled,
});

const { data: subjScoreTrend } = useQuery({
  queryKey: ['主观能动性', deptId, startTime, endTime],
  queryFn: () =>
    fetchDeptSubjScoreTrend({
      deptId: deptId.value,
      startTime: dayjs(startTime.value).valueOf(),
      endTime: dayjs(endTime.value).valueOf(),
      deptType: deptType.value,
    }),
  enabled: queryEnabled,
});

const { data: perfScoreTrend } = useQuery({
  queryKey: ['绩效评分', deptId, startTime, endTime],
  queryFn: () =>
    fetchDeptPerfScoreTrend({
      deptId: deptId.value,
      startTime: dayjs(startTime.value).valueOf(),
      endTime: dayjs(endTime.value).valueOf(),
      deptType: deptType.value,
    }),
  enabled: queryEnabled,
});
</script>

<template>
  <div class="space-y-24">
    <div
      class="sticky top-0 z-20 flex items-center justify-between bg-white text-sm"
    >
      <DatePicker
        v-model:start-time="startTime"
        v-model:end-time="endTime"
        :enable-keys="['latestYear', 'year']"
      />
    </div>
    <div v-if="deptType === DEPT_TYPE.SALES || deptType === DEPT_TYPE.MARKET" class="space-y-16">
      <CardTitle title="业绩走势" class="text-lg" />
      <BarLineChart
        :title="deptType === DEPT_TYPE.SALES ? '新购订单走势' : '市场开发走势'"
        :x-axis="{
          data: performanceTrend?.map(item => item.statDate) ?? [],
        }"
        :series="[
          {
            type: 'bar',
            data: performanceTrend?.map(item => item.quotaNum || 0) ?? [],
            name: '指标量',
          },
          {
            type: 'bar',
            data: performanceTrend?.map(item => (deptType === DEPT_TYPE.SALES ? item.orderPaidNum : item.surgeryNum) || 0) ?? [],
            name: deptType === DEPT_TYPE.SALES ? '有效订单' : '开发手术量',
          },
          {
            type: 'line',
            data: performanceTrend?.map(item => item.achieveRate) ?? [],
            name: '达成率',
            yAxisIndex: 1,
          },
        ]"
        :grid="{
          left: 0,
          bottom: 0,
        }"
        :tooltip="{
          formatter: (params) => {
            const { dataIndex } = params[0]
            const salesList = [
              {
                name: '开单工作室',
                value: performanceTrend?.[dataIndex].paidGroupNum,
              },
              {
                name: '指标量',
                value: performanceTrend?.[dataIndex].quotaNum,
              },
              {
                name: '有效订单',
                value: performanceTrend?.[dataIndex].orderPaidNum,
              },
              {
                name: '达成率',
                value: `${performanceTrend?.[dataIndex].achieveRate}%`,
              },
            ]
            const marketList = [
              {
                name: '指标量',
                value: performanceTrend?.[dataIndex].quotaNum,
              },
              {
                name: '开发手术量',
                value: performanceTrend?.[dataIndex].surgeryNum,
              },
              {
                name: '达成率',
                value: `${performanceTrend?.[dataIndex].achieveRate}%`,
              },
            ]
            return `<strong>${performanceTrend?.[dataIndex].statDate}</strong><br>
              ${(deptType === DEPT_TYPE.SALES ? salesList : marketList).map(item => `<div>${item.name}: ${item.value}</div>`).join('')}
            `;
          },
        }"
        :height="340"
        :y-axis="[
          {},
          {
            splitLine: {
              show: false,
            },
            axisLabel: {
              formatter(value) {
                return `${value}%`;
              },
            },
          },
        ]"
      />
    </div>
    <div class="space-y-16">
      <CardTitle title="人员专业性" class="text-lg" />
      <BarLineChart
        title="考试成绩走势"
        :colors="['#5285EB', '#2FB324', '#E37221']"
        :x-axis="{
          data: examScoreTrend?.toSorted((a, b) => a.month - b.month)?.map(item => item.month) ?? [],
        }"
        :series="[
          {
            type: 'line',
            data: examScoreTrend?.toSorted((a, b) => a.month - b.month)?.map(item => item.avgScore) ?? [],
            name: '常规培训',
          },
          {
            type: 'line',
            data: examScoreTrend?.toSorted((a, b) => a.month - b.month)?.map(item => item.avgScoreLarge) ?? [],
            name: '大练兵',
          },
          {
            type: 'line',
            data: examScoreTrend?.toSorted((a, b) => a.month - b.month)?.map(item => item.duration) ?? [],
            name: '培训时长',
          },
        ]"
        :grid="{
          left: 0,
          bottom: 0,
        }"
        :height="340"
        :tooltip="{
          formatter: (params) => {
            const { dataIndex } = params[0]
            const sortedExamScoreTrend = examScoreTrend?.toSorted((a, b) => a.month - b.month);
            const salesList = [
              {
                name: '培训人数',
                value: `${sortedExamScoreTrend?.[dataIndex].staffNum}人`,
              },
              {
                name: '日常成绩',
                value: `${formatDecimal(sortedExamScoreTrend?.[dataIndex].avgScoreOther ?? 0, 2)}分`,
              },
              {
                name: '大练兵成绩',
                value: `${sortedExamScoreTrend?.[dataIndex].avgScoreLarge}分`,
              },
              {
                name: '培训时长',
                value: `${minShow(sortedExamScoreTrend?.[dataIndex].duration ?? 0)}`,
              },
            ]

            return `<strong>${dayjs(`${sortedExamScoreTrend?.[dataIndex].month}`).format('YYYY年MM月')}</strong><br>
              ${salesList.map(item => `<div>${item.name}: ${item.value}</div>`).join('')}
            `;
          },
        }"
      />
    </div>
    <div class="space-y-16">
      <CardTitle title="主观能动性" class="text-lg" />
      <BarLineChart
        title="主观能动性得分走势"
        :colors="['#5285EB', '#2FB324', '#E37221']"
        :x-axis="{
          data: subjScoreTrend?.toSorted((a, b) => a.month - b.month)?.map(item => item.month) ?? [],
        }"
        :series="[
          {
            type: 'line',
            data: subjScoreTrend?.toSorted((a, b) => a.month - b.month)?.map(item => item.avgScore) ?? [],
            name: '分数',
          },
        ]"
        :grid="{
          left: 0,
          bottom: 0,
        }"
        :height="340"
        :tooltip="{
          formatter: (params) => {
            const { dataIndex } = params[0]
            const data = subjScoreTrend?.toSorted((a, b) => a.month - b.month)?.[dataIndex]
            const salesList = [
              {
                name: '分数',
                value: `${data?.avgScore}`,
              },
            ]

            return `<strong>${dayjs(`${data?.month}`).format('YYYY年MM月')}</strong><br>
              ${salesList.map(item => `<div>${item.name}: ${item.value}</div>`).join('')}
            `;
          },
        }"
      />
    </div>
    <div class="space-y-16">
      <CardTitle title="绩效评价" />
      <BarLineChart
        title="绩效评分走势"
        :colors="['#5285EB', '#2FB324', '#E37221']"
        :show-legend="false"
        :x-axis="{
          data: perfScoreTrend?.toSorted((a, b) => a.month - b.month)?.map(item => item.month) ?? [],
        }"
        :series="[
          {
            type: 'line',
            data: perfScoreTrend?.toSorted((a, b) => a.month - b.month)?.map(item => item.avgScore) ?? [],
            name: '绩效评分',
          },
        ]"
        :grid="{
          left: 0,
          bottom: 0,
        }"
        :height="340"
        :tooltip="{
          formatter: (params) => {
            const { dataIndex } = params[0]
            const data = perfScoreTrend?.toSorted((a, b) => a.month - b.month)?.[dataIndex]
            const salesList = [
              {
                name: '绩效人数',
                value: `${data?.staffNum}人`,
              },
              {
                name: '平均绩效分值',
                value: `${data?.avgScore}`,
              },
              {
                name: '平均系数',
                value: `${data?.avgCoefficient}`,
              },
            ]

            return `<strong>${dayjs(`${data?.month}`).format('YYYY年MM月')}</strong><br>
              ${salesList.map(item => `<div>${item.name}: ${item.value}</div>`).join('')}
            `;
          },
        }"
      />
    </div>
  </div>
</template>
