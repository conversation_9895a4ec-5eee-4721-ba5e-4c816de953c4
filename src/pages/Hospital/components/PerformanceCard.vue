<script setup lang="ts">
import { Comparison, StatisticText } from '@/components';

interface Data {
  name: string;
  value: string | number;
}

export interface Props {
  title: string;
  subTitle?: string;
  data: [Data, Data, Data];
  mom: number;
  yoy: number;
}
defineProps<Props>();
</script>

<template>
  <div class="rounded bg-[#F7F8FA] p-16 text-sm leading-1 text-[#3A4762]">
    <div class="mb-16 flex items-center gap-16 border-b border-[#E1E5ED] pb-12">
      <span class="font-medium text-[#15233F]">{{ title }}</span>
      <span v-if="subTitle" class="text-xs text-[#939CAE]">
        {{ subTitle }}
      </span>
    </div>
    <div class="grid grid-cols-3">
      <StatisticText :title="data[0].name" :value="data[0].value" />
      <div class="item">
        <StatisticText :title="data[1].name" :value="data[1].value" />
        <Comparison :yoy="yoy" :mom="mom" class="mt-16 gap-16" />
      </div>
      <div class="item">
        <StatisticText :title="data[2].name" :value="`${data[2].value}%`" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.item {
  position: relative;
}
.item::before {
  content: '';
  position: absolute;
  z-index: 1;
  width: 1px;
  height: 70%;
  top: 10px;
  bottom: 0;
  left: -16px;
  background: #e1e5ed;
}
</style>
