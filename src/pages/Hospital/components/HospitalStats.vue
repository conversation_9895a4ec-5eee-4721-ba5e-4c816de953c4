<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { useRouteQuery } from '@vueuse/router';
import { ElSkeleton } from 'element-plus';
import { computed } from 'vue';
import { fetchHospitalInfo } from '@/api';

// 使用 useRouteQuery 获取 URL 中的 hospitalId 参数
// 当 URL 中的 hospitalId 变化时，这个值会自动更新
const hospitalId = useRouteQuery('hospitalId', undefined);
const marketId = useRouteQuery('marketId', '0', { transform: Number });
/** 医院名称 */
const hospitalName = useRouteQuery<string>('hospitalName');

// 只有当 hospitalId 有值时才启用查询
const enabled = computed(() => !!hospitalName.value && !!marketId.value);

// 使用 Vue Query 的 useQuery 钩子获取医院信息
// 当 queryKey 中的值（包括 hospitalId）变化时，Vue Query 会自动重新获取数据
const { data, isLoading } = useQuery({
  // 关键点：将 hospitalId 作为 queryKey 的一部分
  // 当 hospitalId 变化时，queryKey 也会变化，触发重新获取
  queryKey: ['hospitalInfo', hospitalId],
  queryFn: () => fetchHospitalInfo(marketId.value, hospitalName.value ?? '', hospitalId.value),
  enabled,
});
</script>

<template>
  <div class="rounded-md bg-white p-24 shadow">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="flex h-200 items-center justify-center">
      <ElSkeleton :rows="3" animated />
    </div>

    <!-- 数据内容 -->
    <div v-else>
      <p class="text-[26px] font-medium text-[#203549]">
        {{ hospitalName }}
      </p>
      <div class="mt-16 grid grid-cols-11 gap-16">
        <div
          class="col-span-6 grid h-140 flex-1 grid-cols-9 items-center justify-between rounded bg-[#f7f8fa] p-16"
        >
          <div class="col-span-5 flex h-full flex-col justify-between">
            <div class="text-sm text-[#3A4762]">
              <p class="mb-4">
                全院PCI手术量
              </p>
              <p class="text-[32px] leading-1">
                {{ data?.pciSurgicalVolume }}
              </p>
            </div>
            <div class="inline-flex gap-8 text-sm text-[#3A4762]">
              <span>全国占比</span>
              <span>{{ data?.nationalRate }}%</span>
              <span class="ml-12">全省占比</span>
              <span>{{ data?.provinceRate }}%</span>
            </div>
          </div>
          <div class="col-span-4 flex h-full items-center">
            <div class="mr-16 h-58 w-1 bg-[#E1E5ED]" />
            <div class="flex h-full flex-col justify-between">
              <div class="text-sm text-[#3A4762]">
                <p class="mb-4">
                  覆盖手术量
                </p>
                <p class="text-[32px] leading-1">
                  {{ data?.coveredSurgicalVolume }}
                </p>
              </div>
              <div class="inline-flex gap-8 text-sm text-[#3A4762]">
                <span>覆盖率</span>
                <span>{{ data?.coverageRate }}%</span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="col-span-5 flex h-140 flex-1 justify-between rounded bg-[#f7f8fa] p-16"
        >
          <div class="text-sm text-[#3A4762]">
            <p class="mb-4">
              在管会员
            </p>
            <p class="text-[32px] leading-1">
              {{ data?.managePatientNum }}
            </p>
          </div>
          <div class="text-sm text-[#3A4762]">
            <p class="mb-4">
              转化率
            </p>
            <p class="text-[32px] leading-1">
              {{ data?.transformRate }}%
            </p>
          </div>
          <div class="text-sm text-[#3A4762]">
            <p class="mb-4">
              续费率
            </p>
            <p class="text-[32px] leading-1">
              {{ data?.renewRate }}%
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
