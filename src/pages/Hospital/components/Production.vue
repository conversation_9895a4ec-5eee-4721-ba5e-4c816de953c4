<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { useRouteQuery } from '@vueuse/router';
import dayjs from 'dayjs';
import { computed, ref } from 'vue';
import { fetchMarketInvestmentData, fetchProjectInvestmentData, fetchSellerInvestmentData } from '@/api';
import { CardTitle, DoughnutChart, StatisticText, Times } from '@/components';

defineOptions({
  name: 'Production',
});

// 获取路由参数中的医院ID
const hospitalId = useRouteQuery('hospitalId', '0', { transform: Number });
const marketHospitalId = useRouteQuery('marketId', '0', { transform: Number });
const hospitalName = useRouteQuery<string>('hospitalName');
// 时间范围
const startTime = ref<number | undefined>(undefined);
const endTime = ref<number | undefined>(undefined);

// 查询是否启用
const queryEnabled = computed(() => {
  return !!hospitalId.value && hospitalId.value !== 0 && !!marketHospitalId.value && !!startTime.value && !!endTime.value;
});

// 使用 useQuery 获取市场投产数据
const { data: marketInvestmentData, isLoading: marketLoading } = useQuery({
  queryKey: ['marketInvestmentData', hospitalId, startTime, endTime],
  queryFn: () =>
    fetchMarketInvestmentData({
      hospitalId: hospitalId.value,
      startTime: startTime.value,
      endTime: endTime.value,
      marketHospitalId: marketHospitalId.value,
      hospitalName: hospitalName.value,
    }),
  enabled: queryEnabled,
});

// 使用 useQuery 获取销售投产数据
const { data: sellerInvestmentData, isLoading: sellerLoading } = useQuery({
  queryKey: ['sellerInvestmentData', hospitalId, startTime, endTime],
  queryFn: () =>
    fetchSellerInvestmentData({
      hospitalId: hospitalId.value,
      startTime: startTime.value,
      endTime: endTime.value,
      marketHospitalId: marketHospitalId.value,
      hospitalName: hospitalName.value,
    }),
  enabled: queryEnabled,
});

// 使用 useQuery 获取科研投产数据
const { data: projectInvestmentData, isLoading: projectLoading } = useQuery({
  queryKey: ['projectInvestmentData', hospitalId, startTime, endTime],
  queryFn: () =>
    fetchProjectInvestmentData({
      hospitalId: hospitalId.value,
      startTime: startTime.value,
      endTime: endTime.value,
      marketHospitalId: marketHospitalId.value,
      hospitalName: hospitalName.value,
    }),
  enabled: queryEnabled,
});

// 处理时间选择变化
function handleDateChange([start, end]: [string, string]) {
  startTime.value = start ? dayjs(start).startOf('day').valueOf() : undefined;
  endTime.value = end ? dayjs(end).endOf('day').valueOf() : undefined;
}

// 格式化投入占比数据为饼图所需格式
const marketInvestChartData = computed(() => {
  return marketInvestmentData.value?.investList?.map(item => ({
    name: item.investType,
    value: item.investAmount,
  })) ?? [];
});

// 格式化销售投入占比数据为饼图所需格式
const sellerInvestChartData = computed(() => {
  return sellerInvestmentData.value?.investList?.map(item => ({
    name: item.investType,
    value: item.investAmount,
  })) ?? [];
});

// 格式化科研投入占比数据为饼图所需格式
const projectInvestChartData = computed(() => {
  return projectInvestmentData.value?.investList?.map(item => ({
    name: item.investType,
    value: item.investAmount,
  })) ?? [];
});
</script>

<template>
  <div class="grid grid-cols-1 gap-16 text-sm text-[#3A4762]">
    <Times class="sticky top-0 z-10 bg-white" @date-change="handleDateChange" />
    <CardTitle title="市场投产" />
    <div v-loading="marketLoading" class="grid grid-cols-3 gap-16">
      <div class="col-span-2 grid grid-cols-4 rounded bg-[#F7F8FA] p-16">
        <StatisticText
          title="投入金额"
          :value="marketInvestmentData?.totalInvestAmount || 0"
        />
        <div class="col-span-3">
          <p class="mb-16">
            投入占比
          </p>
          <DoughnutChart
            :size="72"
            :radius="['70%', '100%']"
            :data="marketInvestChartData"
            :inner-label="false"
          />
        </div>
      </div>
      <div class="flex justify-between rounded bg-[#F7F8FA] p-16">
        <StatisticText
          title="产出PCI手术量（台/年）"
          :value="marketInvestmentData?.pciSurgicalVolume || 0"
        />
        <StatisticText
          title="千台手术成本"
          :value="marketInvestmentData?.surgicalCostVolume || 0"
        />
      </div>
    </div>
    <CardTitle title="销售投产" />
    <div v-loading="sellerLoading" class="grid grid-cols-3 gap-16">
      <div class="col-span-2 grid grid-cols-4 rounded bg-[#F7F8FA] p-16">
        <StatisticText
          title="投入金额"
          :value="sellerInvestmentData?.totalInvestAmount || 0"
        />
        <div class="col-span-3">
          <p class="mb-16">
            投入占比
          </p>
          <DoughnutChart
            :radius="['70%', '100%']"
            :size="72"
            :data="sellerInvestChartData"
            :inner-label="false"
          />
        </div>
      </div>
      <div class="flex justify-between rounded bg-[#F7F8FA] p-16">
        <StatisticText
          title="产出订单量（单）/产出金额"
          :value="`${sellerInvestmentData?.orderNum || 0}/${sellerInvestmentData?.orderAmount || 0}`"
        />
        <StatisticText
          title="部门投产比"
          :value="sellerInvestmentData?.invOutputRate || 0"
        />
      </div>
    </div>
    <CardTitle title="科研投产" />
    <div v-loading="projectLoading" class="grid grid-cols-3 gap-16">
      <div class="col-span-2 grid grid-cols-4 rounded bg-[#F7F8FA] p-16">
        <StatisticText
          title="投入金额"
          :value="projectInvestmentData?.totalInvestAmount || 0"
        />
        <div class="col-span-3">
          <p class="mb-16">
            投入占比
          </p>
          <DoughnutChart
            :size="72"
            :radius="['70%', '100%']"
            :data="projectInvestChartData"
            :inner-label="false"
          />
        </div>
      </div>
      <div class="flex justify-between rounded bg-[#F7F8FA] p-16">
        <StatisticText
          title="预期产出/实际产出"
          :value="`${projectInvestmentData?.expectOutput || 0}/${projectInvestmentData?.actualOutput || 0}`"
        />
        <StatisticText
          title="科研投产比"
          :value="projectInvestmentData?.invOutputRate || 0"
        />
      </div>
    </div>
  </div>
</template>
