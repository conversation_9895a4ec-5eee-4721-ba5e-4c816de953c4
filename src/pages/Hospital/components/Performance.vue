<script setup lang="tsx">
import { CaretBottom, CaretTop } from '@element-plus/icons-vue';
import { useQuery } from '@tanstack/vue-query';
import { useRouteQuery } from '@vueuse/router';
import dayjs from 'dayjs';
import { ElIcon, ElTableColumn } from 'element-plus';
import { computed, ref, useTemplateRef, watch } from 'vue';
import { fetchHospitalMarketPeopleList, fetchHospitalSellerPeopleList, fetchMarketPerformanceData, fetchSellerPerformanceData } from '@/api';
import { CardTitle, InfiniteTable, PeopleDrawer, Times } from '@/components';
import PerformanceCard from './PerformanceCard.vue';

const hospitalId = useRouteQuery('hospitalId', null, { transform: val => val ? Number(val) : null });
const marketId = useRouteQuery('marketId', '0', { transform: Number });
const startTime = ref<number | undefined>(dayjs().startOf('month').valueOf());
const endTime = ref<number | undefined>(dayjs().endOf('month').valueOf());
const sellerRef = useTemplateRef('sellerRef');
const marketRef = useTemplateRef('marketRef');

const queryEnabled = computed(() => {
  return !!hospitalId.value && hospitalId.value !== 0 && !!startTime.value && !!endTime.value && !!marketId.value;
});

const { data: performanceData } = useQuery({
  queryKey: ['performanceData', hospitalId, startTime, endTime],
  queryFn: () => fetchMarketPerformanceData({
    hospitalId: hospitalId.value,
    startTime: startTime.value,
    endTime: endTime.value,
    marketHospitalId: marketId.value,
  }),
  enabled: queryEnabled,
  retry: false,
});

const { data: sellerPerformanceData } = useQuery({
  queryKey: ['sellerPerformanceData', hospitalId, startTime, endTime],
  queryFn: () => fetchSellerPerformanceData({
    hospitalId: hospitalId.value,
    startTime: startTime.value,
    endTime: endTime.value,
    marketHospitalId: marketId.value,
  }),
  enabled: queryEnabled,
  retry: false,
});

const osUserId = ref(-1);
const deptId = ref(-1);
const deptType = ref();
const userId = ref(-1);
const openDrawer = ref(false);

watch([startTime, endTime, hospitalId], () => {
  sellerRef.value?.clear();
  marketRef.value?.clear();
});

// 查询销售人员列表的函数
async function fetchSellerPeople(params: { pageNumber: number; pageSize: number }) {
  const res = await fetchHospitalSellerPeopleList({
    hospitalId: hospitalId.value,
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
    startTime: startTime.value,
    endTime: endTime.value,
    marketHospitalId: marketId.value,
  });
  return {
    total: res.total,
    contents: res.contents || [],
    success: true,
  };
}

function showPeopleDrawer(row: any) {
  openDrawer.value = true;
  osUserId.value = row.osUserId;
  deptId.value = row.deptId;
  deptType.value = row.deptType;
  userId.value = row.userId;
}

async function fetchMarketPeople(params: { pageNumber: number; pageSize: number }) {
  const res = await fetchHospitalMarketPeopleList({
    hospitalId: hospitalId.value,
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
    startTime: startTime.value,
    endTime: endTime.value,
    marketHospitalId: marketId.value,
  });
  return {
    total: res.total,
    contents: res.contents || [],
    success: true,
  };
}

/**
 * 处理时间选择变化
 * @param param 时间段
 * @param param.0 开始时间
 * @param param.1 结束时间
 */
function handleDateChange([start, end]: [string, string]) {
  startTime.value = start ? dayjs(start).startOf('day').valueOf() : undefined;
  endTime.value = end ? dayjs(end).endOf('day').valueOf() : undefined;
}
</script>

<template>
  <div class="grid grid-cols-1 gap-16">
    <!-- 时间选择器 -->
    <Times @date-change="handleDateChange" />
    <CardTitle title="市场业绩" />
    <PerformanceCard
      title="市场开发"
      sub-title="手术量单位：台/年"
      :data="[
        {
          name: '手术量开发指标',
          value: performanceData?.surgicalVolume ?? 0,
        },
        {
          name: '完成开发手术量',
          value: performanceData?.completeSurgicalVolume ?? 0,
        },
        {
          name: '市场开发指标完成率',
          value: performanceData?.quotaCompleteRate ?? 0,
        },
      ]"
      :yoy="performanceData?.compareRate ?? 0"
      :mom="performanceData?.ringRate ?? 0"
    />
    <CardTitle title="市场人员" />
    <PeopleDrawer
      v-model="openDrawer"
      :user-id="userId"
      :dept-id="deptId"
      :dept-type="deptType"
      :os-user-id="osUserId"
    />
    <InfiniteTable ref="marketRef" :query-fn="fetchMarketPeople" :height="300">
      <ElTableColumn prop="userName" label="姓名" />
      <ElTableColumn prop="postName" label="岗位" />
      <ElTableColumn prop="surgicalVolume" label="手术量开发指标" width="130" />
      <ElTableColumn prop="completeSurgicalVolume" label="完成开发手术量" width="130" />
      <ElTableColumn prop="surgicalCompareRate" label="开发手术量同比" width="130">
        <template #default="{ row }">
          <div class="flex items-center">
            <ElIcon :class="row.surgicalCompareRate >= 0 ? 'w-16 text-[#2FB324]' : 'w-16 text-[#E63746]'">
              <CaretTop v-if="row.surgicalCompareRate >= 0" />
              <CaretBottom v-else />
            </ElIcon>
            <span class="pl-6">
              {{ Math.abs(row.surgicalCompareRate) }}
              %
            </span>
          </div>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="surgicalRingRate" label="开发手术量环比" width="130">
        <template #default="{ row }">
          <div class="flex items-center">
            <ElIcon :class="row.surgicalRingRate >= 0 ? 'w-16 text-[#2FB324]' : 'w-16 text-[#E63746]'">
              <CaretTop v-if="row.surgicalRingRate >= 0" />
              <CaretBottom v-else />
            </ElIcon>
            <span class="pl-6">
              {{ Math.abs(row.surgicalRingRate) }}
              %
            </span>
          </div>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="quotaCompleteRate" label="市场开发指标完成率" width="150">
        <template #default="{ row }">
          <span>
            {{ row.quotaCompleteRate }}
            %
          </span>
        </template>
      </ElTableColumn>
      <ElTableColumn label="详情" width="80" fixed="right">
        <template #default="{ row }">
          <span
            class="cursor-pointer text-[#409EFF]"
            @click="showPeopleDrawer(row)"
          >
            详情
          </span>
        </template>
      </ElTableColumn>
    </InfiniteTable>
    <CardTitle title="销售业绩" />
    <PerformanceCard
      title="销售达成"
      sub-title="销售指标单位：订单"
      :data="[
        {
          name: '医院手术量',
          value: sellerPerformanceData?.surgicalVolume ?? 0,
        },
        {
          name: '完成订单量',
          value: sellerPerformanceData?.completeOrderNum ?? 0,
        },
        {
          name: '手术转化率',
          value: sellerPerformanceData?.surgicalTransformRate ?? 0,
        },
      ]"
      :yoy="sellerPerformanceData?.compareRate ?? 0"
      :mom="sellerPerformanceData?.ringRate ?? 0"
    />
    <CardTitle title="销售人员" />
    <InfiniteTable ref="sellerRef" :query-fn="fetchSellerPeople" :height="300">
      <ElTableColumn prop="userName" label="姓名" />
      <ElTableColumn prop="postName" label="岗位" />
      <ElTableColumn prop="quotaNum" label="销售指标" />
      <ElTableColumn prop="completeOrderVolume" label="完成订单量" />
      <ElTableColumn prop="orderCompareRate" label="完成订单量同比">
        <template #default="{ row }">
          <div class="flex items-center">
            <ElIcon :class="row.orderCompareRate >= 0 ? 'w-16 text-[#2FB324]' : 'w-16 text-[#E63746]'">
              <CaretTop v-if="row.orderCompareRate >= 0" />
              <CaretBottom v-else />
            </ElIcon>
            <span class="pl-6">
              {{ Math.abs(row.orderCompareRate) }}
              %
            </span>
          </div>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="orderRingRate" label="完成订单量环比">
        <template #default="{ row }">
          <div class="flex items-center">
            <ElIcon :class="row.orderRingRate >= 0 ? 'w-16 text-[#2FB324]' : 'w-16 text-[#E63746]'">
              <CaretTop v-if="row.orderRingRate >= 0" />
              <CaretBottom v-else />
            </ElIcon>
            <span class="pl-6">
              {{ Math.abs(row.orderRingRate) }}
              %
            </span>
          </div>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="quotaCompleteRate" label="销售指标完成率">
        <template #default="{ row }">
          <span>
            {{ row.quotaCompleteRate }}
            %
          </span>
        </template>
      </ElTableColumn>
      <ElTableColumn label="详情" width="80" fixed="right">
        <template #default="{ row }">
          <span
            class="cursor-pointer text-[#409EFF]"
            @click="showPeopleDrawer(row)"
          >
            详情
          </span>
        </template>
      </ElTableColumn>
    </InfiniteTable>
  </div>
</template>

<style scoped>
.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #3a4762;
  position: relative;
  padding-left: 12px;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #4080ff;
  border-radius: 2px;
}
</style>
