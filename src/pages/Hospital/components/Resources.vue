<script setup lang="ts">
import { CardTitle, Table } from '@/components';
import { ElTableColumn } from 'element-plus';

defineOptions({
  name: 'Resources',
});

interface MarketStaff {
  name: string;
  position: string;
  age: number;
  department: string;
  post: string;
  phone: string;
  remark: string;
  yearGrowth: number;
  monthGrowth: number;
  performance: number;
}

const tableData = ref<MarketStaff[]>([
  {
    name: '余琴',
    position: '市场总监',
    age: 999,
    department: '99999',
    post: '市场总监',
    phone: '99999',
    remark: '',
    yearGrowth: 5,
    monthGrowth: -40,
    performance: 40,
  },
  {
    name: '刘应才',
    position: '市场总监',
    age: 999,
    department: '99999',
    post: '市场总监',
    phone: '99999',
    remark: '',
    yearGrowth: 5,
    monthGrowth: -40,
    performance: 40,
  },
  {
    name: '刘应才',
    position: '市场总监',
    age: 999,
    department: '99999',
    post: '市场总监',
    phone: '99999',
    remark: '',
    yearGrowth: 5,
    monthGrowth: -40,
    performance: 40,
  },
  {
    name: '刘应才',
    position: '市场总监',
    age: 999,
    department: '99999',
    post: '市场总监',
    phone: '99999',
    remark: '',
    yearGrowth: 5,
    monthGrowth: -40,
    performance: 40,
  },
  {
    name: '余琴',
    position: '市场总监',
    age: 999,
    department: '99999',
    post: '市场总监',
    phone: '99999',
    remark: '',
    yearGrowth: 5,
    monthGrowth: -40,
    performance: 40,
  },
]);
</script>

<template>
  <div class="flex w-full flex-col gap-16">
    <CardTitle title="市场人员" />
    <Table :data="tableData">
      <ElTableColumn prop="name" label="姓名" width="120" />
      <ElTableColumn prop="position" label="性别" width="120" />
      <ElTableColumn prop="age" label="年龄" width="120" />
      <ElTableColumn prop="department" label="所属单位" width="120" />
      <ElTableColumn prop="post" label="岗位" width="120" />
      <ElTableColumn prop="phone" label="电话号码" width="150" />
      <ElTableColumn prop="remark" label="备注" />
    </Table>
  </div>
</template>
