<script setup lang="tsx">
import { useQuery } from '@tanstack/vue-query';
import { useRouteQuery } from '@vueuse/router';
import { ElTableColumn } from 'element-plus';
import { ref, useTemplateRef, watch } from 'vue';
import { fetchHospitalGroupList, fetchHospitalGroupTotalPCI, fetchHospitalHierarchy } from '@/api';
import { CardTitle, HospitalTree, InfiniteTable } from '@/components';

defineOptions({
  name: 'Organization',
});
const workRoomTable = useTemplateRef('workRoomTable');
const hospitalId = useRouteQuery('hospitalId', null, { transform(val) {
  if (val) {
    return Number(val);
  }
  return null;
} });
const marketHospitalId = useRouteQuery('marketId', '0', { transform: Number });
const total = ref<number>(0);

const queryEnable = computed(() => !!marketHospitalId.value);

const { data: totalPCI } = useQuery({
  queryKey: ['fetchHospitalGroupTotalPCI', hospitalId, marketHospitalId],
  queryFn: () => fetchHospitalGroupTotalPCI(hospitalId.value, marketHospitalId.value),
});

const { data: hierarchy } = useQuery({
  queryKey: ['fetchHospitalHierarchy', hospitalId, marketHospitalId],
  queryFn: () => fetchHospitalHierarchy({
    hospitalId: hospitalId.value,
    marketHospitalId: marketHospitalId.value,
  }),
  enabled: queryEnable,
});

watch(hospitalId, () => {
  workRoomTable.value?.clear();
});

async function fetchWorkRoomList(params: { pageNumber: number; pageSize: number }) {
  const res = await fetchHospitalGroupList({
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
    hospitalId: hospitalId.value,
    marketHospitalId: marketHospitalId.value,
  });
  total.value = res.total;
  return {
    total: res.total,
    success: true,
    contents: res.contents || [],
  };
}
</script>

<template>
  <div class="rounded-md bg-white">
    <HospitalTree :dept-list="hierarchy?.framework || []" />

    <!-- 工作室列表 -->
    <div>
      <CardTitle
        title="医院工作室"
        :sub-title="`共计${total}个工作室，${totalPCI ?? 0}台年PCI手术量`"
      />
      <InfiniteTable
        ref="workRoomTable"
        class="mt-16"
        :query-fn="fetchWorkRoomList"
        :height="400"
      >
        <ElTableColumn
          label="工作室名称"
          prop="groupName"
          width="140"
        />
        <ElTableColumn
          prop="groupLeaderName"
          label="工作室组长"
          width="100"
        >
          <template #default="{ row }">
            <span>{{ row.groupLeaderName || '-' }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="groupMemberNameList"
          label="工作室组员"
        >
          <template #default="{ row }">
            <span>{{ row.groupMemberNameList?.join(',') || '-' }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="pciSurgicalVolume"
          label="年PCI手术量"
          width="110"
        />
        <ElTableColumn
          prop="managePatientNum"
          label="在管会员数"
          width="100"
        />
        <ElTableColumn
          prop="groupTransformRate"
          label="工作室转化率"
          width="110"
        >
          <template #default="{ row }">
            <span>{{ row.groupTransformRate ? `${row.groupTransformRate}%` : '-' }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="patientRenewRate"
          label="会员续费率"
          width="100"
        >
          <template #default="{ row }">
            <span>{{ row.patientRenewRate ? `${row.patientRenewRate}%` : '-' }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="groupRefundRate"
          label="工作室退费率"
          width="110"
        >
          <template #default="{ row }">
            <span>{{ row.groupRefundRate ? `${row.groupRefundRate}%` : '-' }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="状态"
          width="60"
          fixed="right"
        >
          <template #default="{ row }">
            <span>{{ row.groupStatus === 'ENABLE' ? '启用' : '禁用' }}</span>
          </template>
        </ElTableColumn>
      </InfiniteTable>
    </div>
  </div>
</template>
