<script setup lang="tsx">
import type { Hospital } from '@/schema';
import { useRouteQuery } from '@vueuse/router';
import { ElTableColumn } from 'element-plus';
import { isEmpty } from 'lodash-es';
import { useTemplateRef, watch } from 'vue';
import { useRouter } from 'vue-router';
import { fetchHospitalList } from '@/api';
import { ChinaMap, InfiniteTable, SourceCard, TabPane, Tabs } from '@/components';
import HospitalStats from './components/HospitalStats.vue';
import Organization from './components/Organization.vue';
import Performance from './components/Performance.vue';
import Production from './components/Production.vue';
import Resources from './components/Resources.vue';

defineOptions({
  name: 'Hospital',
});

const router = useRouter();
/** 当前激活的标签页 */
const activeTab = useRouteQuery('hospitalTab', 'organization');
/** 医院ID */
const hospitalId = useRouteQuery<number | undefined | null>('hospitalId', undefined, { transform: val => val ? Number(val) : undefined });
/** 医院名称 */
const hospitalName = useRouteQuery<string>('hospitalName', '', { transform: String });
/** 市场ID */
const marketId = useRouteQuery('marketId', '0', { transform: Number });
/** 省份名称 */
const province = useRouteQuery<undefined | string>('province');
const hospitalTableRef = useTemplateRef('hospitalTable');

watch([province], () => {
  hospitalTableRef.value?.clear();
});

function goBack() {
  if (window.history.length > 1) {
    router.back();
  }
  else {
    router.push('/overview');
  }
}

async function fetchHospitalInfo(params: { pageNumber: number; pageSize: number }) {
  const res = await fetchHospitalList({
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
    provinceName: province.value || undefined,
  });
  /** 第一条数据默认选中 */
  if (params.pageNumber === 1 && res.contents && !isEmpty(res.contents)) {
    hospitalId.value = res.contents?.[0].hospitalId;
    hospitalName.value = res.contents?.[0].hospitalName || '';
    marketId.value = res.contents?.[0].marketHospitalId;
  }
  return {
    total: res.total,
    success: true,
    contents: res.contents || [],
  };
}

function handleRowClick(hospital: Hospital) {
  hospitalId.value = hospital.hospitalId;
  hospitalName.value = hospital.hospitalName || '';
  marketId.value = hospital.marketHospitalId;
  activeTab.value = 'organization';
}

function handleProvinceSelected(name: string) {
  province.value = name;
}

function tableRowClassName({
  row,
}: {
  row: Hospital;
}) {
  let baseClass = 'text-sm text-[#3A4762] border-t border-[#E1E5ED] cursor-pointer overflow-x-auto';
  const isSameHospitalId = (row.hospitalId === null && hospitalId.value === undefined) || row.hospitalId === hospitalId.value;
  if (row.hospitalName === hospitalName.value && isSameHospitalId) {
    baseClass += ' hrt-hospital-table__selected';
  }
  return baseClass;
}
</script>

<template>
  <div class="grid h-full grid-cols-8 gap-16 overflow-y-hidden">
    <div class="col-span-3 flex flex-col gap-16">
      <div class="relative rounded-md bg-white p-24 shadow">
        <div class="flex items-center justify-between">
          <div
            class="z-10 grid h-32 w-76 cursor-pointer place-content-center rounded-sm border border-[#DCDFE6] text-sm text-[#606266]"
            @click="goBack"
          >
            返回
          </div>
          <h2 class="text-[28px] font-semibold text-[#3A4762]">
            {{ province || '全国' }}
          </h2>
        </div>
        <ChinaMap :province="province" @province-selected="handleProvinceSelected" />
      </div>
      <SourceCard title="医院列表" class="flex-1">
        <InfiniteTable
          ref="hospitalTable"
          :query-fn="fetchHospitalInfo"
          :infinite-scroll-distance="50"
          :row-class-name="tableRowClassName"
          @cell-click="handleRowClick"
        >
          <ElTableColumn prop="provinceName" label="省份" />
          <ElTableColumn prop="cityName" label="地区" />
          <ElTableColumn prop="hospitalName" label="医院名称">
            <template #default="{ row }">
              <span class="cursor-pointer">
                {{ row.hospitalName }}
              </span>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="cooperationStage" label="合作阶段" width="100">
            <template #default="{ row }">
              <div class="flex items-center gap-6">
                <span class="block size-8 shrink-0 rounded-lg bg-[#2FB324]" />
                <span>{{ row.cooperationStage }}</span>
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="pciSurgicalVolume" label="年PCI手术量" width="110" />
        </InfiniteTable>
      </SourceCard>
    </div>
    <div class="col-span-5 flex h-full flex-col gap-16 overflow-y-hidden">
      <HospitalStats />
      <div class="flex-1 overflow-y-auto">
        <Tabs v-model="activeTab" content-class="px-24">
          <TabPane label="医院架构" name="organization">
            <Organization />
          </TabPane>
          <TabPane label="医院投产" name="production">
            <Production />
          </TabPane>
          <TabPane label="医院业绩" name="performance">
            <Performance />
          </TabPane>
          <TabPane label="可用资源" name="resources">
            <Resources />
          </TabPane>
        </Tabs>
      </div>
    </div>
  </div>
</template>

<style scoped>
.shadow {
  box-shadow: 0 0 8px 0 #d9e9ff;
}

/* 组织架构图样式 */
.org-chart {
  position: relative;
}

.office-box,
.dept-box {
  width: 280px;
  background: #f7f8fa;
  border: 1px solid #e1e5ed;
  border-radius: 4px;
  padding: 16px;
  margin: 0 auto;
}

.office-box .title,
.dept-box .title {
  background: #2468f2;
  color: white;
  padding: 8px;
  text-align: center;
  border-radius: 4px;
  margin-bottom: 12px;
  font-size: 14px;
}

.office-box .info,
.dept-box .info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.departments {
  display: flex;
  justify-content: space-around;
  margin-top: 40px;
  position: relative;
}

.departments::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  width: 2px;
  height: 20px;
  background: #e1e5ed;
}

.department {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.department::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  width: 2px;
  height: 20px;
  background: #e1e5ed;
}

.sub-departments {
  display: flex;
  gap: 24px;
  margin-top: 40px;
  position: relative;
}

.sub-departments::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  width: 2px;
  height: 20px;
  background: #e1e5ed;
}

/* 连接线 */
.departments::after {
  content: '';
  position: absolute;
  top: -20px;
  left: 25%;
  width: 50%;
  height: 2px;
  background: #e1e5ed;
}

.sub-departments::after {
  content: '';
  position: absolute;
  top: -20px;
  left: 25%;
  width: 50%;
  height: 2px;
  background: #e1e5ed;
}
</style>

<style lang="css">
.hrt-hospital-table__selected {
  background-color: rgba(41, 83, 245, 0.2) !important;
}
</style>
