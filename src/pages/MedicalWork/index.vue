<template>
  <div class="contain">
    <div id="anchor_work_1" v-loading="workStore.loadings[0]" class="mb-16">
      <LayoutCard
        title-line
        show-more
        title="全部在管患者"
        @click-more="() => (allPatientVisible = true)"
      >
        <div class="flex">
          <Card
            item-height="150px"
            style="min-width: 150px"
            title="全部在管"
            block
            >{{ allData.allPatientNums }}</Card
          >
          <Card
            item-height="150px"
            item-width="316px"
            style="min-width: 318px"
            title="付费在管"
            block
          >
            <div class="w-full">
              <div class="h-40">{{ allData.paidPatientNums }}</div>
              <div class="bottom">
                <div class="item">
                  <span>新购</span>
                  <span
                    >{{ allData.newPatientNums }} ({{
                      allData?.newPercent
                    }}%)</span
                  >
                </div>
                <div class="item">
                  <span>续管</span>
                  <span
                    >{{ allData.renewPatientNums }} ({{
                      allData?.renewPercent
                    }}%)</span
                  >
                </div>
              </div>
            </div>
          </Card>
          <Card
            item-height="150px"
            style="min-width: 150px"
            title="科研在管"
            block
            >{{ allData.researchPatientNums }}</Card
          >
          <div class="right">
            <div class="pie-item">
              <PieChart
                title="服务包分布"
                :bold="false"
                mt="4px"
                :chart-width="100"
                :legend-label-width="160"
                :data="
                  transformPieData(allData.servicePackages, {
                    name: 'productName',
                    value: 'patientNums',
                    percent: 'rate',
                  })
                "
              />
            </div>
            <div class="pie-item">
              <PieChart
                title="年龄占比"
                :bold="false"
                mt="4px"
                :chart-width="100"
                :legend-item-width="160"
                :data="
                  transformPieData(allData.patientAges, {
                    name: 'agePeriod',
                    value: 'patientNum',
                    percent: 'rate',
                  })
                "
              />
            </div>
          </div>
        </div>
      </LayoutCard>
    </div>
    <FixedHeader :top="120">
      <div class="toolbar">
        <Times @date-change="dateChangeHandler" />
        <Anchor :data="MEDICAL_WORK_ANCHOR" />
      </div>
    </FixedHeader>
    <div id="anchor_work_2" v-loading="workStore.loadings[1]">
      <LayoutCard
        title-line
        show-more
        title="在管患者"
        @click-more="() => (managePatientVisible = true)"
      >
        <StackBarChart
          :data-zoom-config="{ endValue: 11 }"
          style="height: 360px"
          :color="['#5285EB', '#9FBCF5']"
          :label-no-wrap="true"
          :x-data="managePatientData.xData"
          :data="managePatientData.data"
        />
      </LayoutCard>
    </div>
    <div id="anchor_work_3" v-loading="workStore.loadings[2]" class="mt-16">
      <LayoutCard
        title-line
        show-more
        title="复查/随访"
        @click-more="() => (rfVisible = true)"
      >
        <ReviewFollowup
          :review-x-data="reviewAndFollowData.review.xData"
          :followup-x-data="reviewAndFollowData.follow.xData"
          :followup-data="reviewAndFollowData.follow.data"
          :review-data="reviewAndFollowData.review.data"
        />
      </LayoutCard>
    </div>
    <div id="anchor_work_4" v-loading="workStore.loadings[3]" class="mt-16">
      <LayoutCard
        title-line
        show-more
        title="指标达标率"
        tips="指标达标率: 达标指标数/测量指标数"
        @click-more="() => (indicatorVisible = true)"
      >
        <StackBarChart
          style="height: 360px"
          :label-no-wrap="true"
          :grid="{ left: 60 }"
          :data-zoom-config="{ endValue: 11 }"
          :color="['#5285EB', '#9FBCF5']"
          :x-data="indicatorData.xData"
          :data="indicatorData.data"
        >
          <template #operation>
            <RadioGroup
              :key="refreshKey"
              value="ALL"
              @on-change="indicatorChangeHandler"
            />
          </template>
        </StackBarChart>
      </LayoutCard>
    </div>
    <div id="anchor_work_5" v-loading="workStore.loadings[4]" class="mt-16">
      <LayoutCard
        title-line
        show-more
        title="团队工作情况"
        @click-more="() => (workVisible = true)"
      >
        <div>
          <CustomTab :key="refreshKey" @on-change="workTypeChange" />
          <GroupWork
            :key="workParams.dateType"
            :consult-x-data="consultData.xData"
            :consult-data="consultData.data"
            :indicator-x-data="riskData.xData"
            :indicator-data="riskData.data"
            :call-x-data="callData.xData"
            :call-data="callData.data"
            @on-change="workParamsChangeHandler"
          />
        </div>
      </LayoutCard>
    </div>
    <div id="anchor_work_6" class="mt-16">
      <LayoutCard title-line title="续费/退费">
        <div class="flex">
          <div v-loading="workStore.loadings[5]" class="w-[50%] mr-16">
            <SubCard style="height: 160px">
              <div class="flex">
                <div class="max-w-150 shrink-0">
                  <div>
                    <Card title="到期续费患者">{{
                      workStore.renew.renewNum
                    }}</Card>
                  </div>
                  <div class="mt-12">
                    <Comparission
                      :hide-yoy="isWholeYear(workStore.times)"
                      :yoy="workStore.renew.yoyRenewPercent"
                      :mom="workStore.renew.momRenewPercent"
                      item-width="150px"
                    />
                  </div>
                </div>
                <div class="flex-1">
                  <BarChart
                    :height="136"
                    title="续费率排名"
                    :zoom-size="8"
                    :grid="{
                      top: 20,
                      right: 90,
                      bottom: renewData.xData.length > 8 ? 60 : 20,
                    }"
                    :data-zoom-config="{
                      height: 12,
                      endValue: 11,
                      minValueSpan: 7,
                    }"
                    :more-text="'更多 >'"
                    :x-data="renewData.xData"
                    :data="renewData.data"
                    @click-more="() => (renewVisible = true)"
                  />
                </div>
              </div>
            </SubCard>
          </div>
          <div v-loading="workStore.loadings[6]" class="w-[50%]">
            <SubCard style="height: 160px">
              <div class="flex h-full">
                <div class="max-w-150 shrink-0">
                  <div>
                    <Card title="退费订单">{{
                      refundData?.currRefundNum
                    }}</Card>
                  </div>
                  <div class="mt-12">
                    <Comparission
                      :hide-yoy="isWholeYear(workStore.times)"
                      :yoy="refundData?.yoyRefundPercent"
                      :mom="refundData?.momRefundPercent"
                      item-width="150px"
                    />
                  </div>
                </div>
                <div class="flex-1 h-128">
                  <NewPieChart
                    title=" "
                    :bold="false"
                    :more-text="'更多 >'"
                    mt="4px"
                    :chart-width="100"
                    :legend-height="100"
                    :data="
                      transformPieData(refundData?.refundReasonList, {
                        name: 'reasonEnum',
                        value: 'reasonNum',
                        percent: 'reasonPercent',
                      })
                    "
                    :radius="['40%', '60%']"
                    @click-more="() => (refundVisible = true)"
                  />
                </div>
              </div>
            </SubCard>
          </div>
        </div>
      </LayoutCard>
    </div>
  </div>
  <AllPatientAnalysis
    v-model:visible="allPatientVisible"
    title="全部患者分析"
  />
  <ManagePatientAnalysis
    v-model:visible="managePatientVisible"
    title="在管患者分析"
  />
  <RFAnylysis v-model:visible="rfVisible" title="患者依从性" />
  <IndicatorAnalysis v-model:visible="indicatorVisible" title="指标控制情况" />
  <WorkAnalysis v-model:visible="workVisible" title="团队工作情况" />
  <RefundAnalysis v-model:visible="refundVisible" title="用户退费分析" />
  <RenewAnalysis v-model:visible="renewVisible" title="用户续费分析" />
</template>

<script setup lang="ts">
import Anchor from '@/components/Anchor/index.vue';
import BarChart from '@/components/BarChart/index.vue';
import Card from '@/components/Card/index.vue';
import Comparission from '@/components/Comparission/index.vue';
import CustomTab from '@/components/CustomTab/index.vue';
import FixedHeader from '@/components/FixedHeader/index.vue';
import LayoutCard from '@/components/LayoutCard/index.vue';
import NewPieChart from '@/components/NewPieChart/index.vue';
import PieChart from '@/components/PieChart/index.vue';
import RadioGroup from '@/components/RadioGroup/index.vue';
import StackBarChart from '@/components/StackBarChart/index.vue';
import SubCard from '@/components/SubCard/index.vue';
import Times from '@/components/Times/index.vue';
import { INDICATORS_MAP, MEDICAL_WORK_ANCHOR } from '@/constant';
import useMedicalWork from '@/store/useMedicalWork';
import useMedicalWorkDrilling from '@/store/useMedicalWorkDrilling';
import { getChartBarDatas, isWholeYear, transformPieData } from '@/utils';
import {
  getDoctorCallSeriesData,
  getDoctorConsultSeriesData,
  getDoctorIndicatorSeriesData,
  getDoctorManagePatientSeriesData,
  getDoctorRFSeriesData,
  getDoctorRiskSeriesData,
} from '@/utils/transformData';
import { sumBy } from 'lodash-es';
import ReviewFollowup from '../MedicalManage/components/module/ReviewFollowup.vue';
import AllPatientAnalysis from './components/drilling/AllPatientAnalysis.vue';
import IndicatorAnalysis from './components/drilling/IndicatorAnalysis.vue';
import ManagePatientAnalysis from './components/drilling/ManagePatientAnalysis.vue';
import RefundAnalysis from './components/drilling/RefundAnalysis.vue';
import RenewAnalysis from './components/drilling/RenewAnalysis.vue';
import RFAnylysis from './components/drilling/RFAnylysis.vue';
import WorkAnalysis from './components/drilling/WorkAnalysis.vue';
import GroupWork from './components/module/GroupWork.vue';

const workStore = useMedicalWork();
const drillingStore = useMedicalWorkDrilling();
const allPatientVisible = ref(false);
const indicatorVisible = ref(false);
const managePatientVisible = ref(false);
const refundVisible = ref(false);
const renewVisible = ref(false);
const rfVisible = ref(false);
const workVisible = ref(false);
const refreshKey = ref(1);
const indicatorParams = ref('ALL');
const workParams = ref({
  dateType: 'WORKING_HOURS',
  callType: 'ALL',
  initiateType: 'ALL',
  indexType: 'ALL',
});
const indicatorChangeHandler = val => {
  indicatorParams.value = val;
  drillingStore.indexType = val;
  workStore.getIndicatorData({ indexType: val });
};
const workTypeChange = val => {
  workParams.value.dateType = val;
  drillingStore.dateType = val;
  workStore.getTeamWoringData({
    dateType: val,
    callType: 'ALL',
    initiateType: 'ALL',
    indexType: 'ALL',
    queryConsult: true,
    queryCall: true,
    queryIndex: true,
  });
};
const workParamsChangeHandler = ({ key, val }) => {
  const needKeyMap = {
    callType: 'queryCall',
    initiateType: 'queryConsult',
    indexType: 'queryIndex',
  };
  const defaultNeed = {
    queryCall: false,
    queryConsult: false,
    queryIndex: false,
  };
  workParams.value[key] = val;
  workStore.getTeamWoringData({
    ...workParams.value,
    ...defaultNeed,
    [needKeyMap[key]]: true,
  });
};
const dateChangeHandler = val => {
  workStore.times = val;
  drillingStore.times = val;
  drillingStore.indexType = 'ALL';
  drillingStore.dateType = 'WORKING_HOURS';
  refreshKey.value += 1;
  workParams.value = {
    dateType: 'WORKING_HOURS',
    callType: 'ALL',
    initiateType: 'ALL',
    indexType: 'ALL',
  };
  indicatorParams.value = 'ALL';
  workStore.pageInit();
};
const allData = computed(() => workStore.allData);

const managePatientData = computed(() => {
  const item = workStore.managePatient.managing;
  return getDoctorManagePatientSeriesData(item);
});
const reviewAndFollowData = computed(() => {
  const item = workStore.reviewAndFollow;
  const { reviewData, followData } = getDoctorRFSeriesData(item, [
    'review',
    'followUp',
  ]);
  return { follow: followData, review: reviewData };
});
const otherParams = computed(() => {
  const curIndicatorName = INDICATORS_MAP[indicatorParams.value].name;
  return { suf: `(${curIndicatorName})` };
});
const indicatorData = computed(() => {
  const item = workStore.indicator.index;
  return getDoctorIndicatorSeriesData({
    list: item,
    otherParams: otherParams.value,
  });
});

const consultData = computed(() => {
  const item = workStore.teamWork.consultStat?.consult;
  return getDoctorConsultSeriesData(item);
});
const riskData = computed(() => {
  const item = workStore.teamWork.riskHandleStat?.riskHandle;
  return getDoctorRiskSeriesData(item);
});
const callData = computed(() => {
  const item = workStore.teamWork.callStat?.call;
  return getDoctorCallSeriesData(item);
});
const renewData = computed(() => {
  const list = workStore.renew.renewRanking ?? [];
  const xData = list?.map(v => v.doctorName!);
  const percent = (sumBy(list, 'renewRate') / list.length).toFixed(2);
  const data = [
    {
      ...getChartBarDatas({
        list: list,
        name: '续费率',
        key: 'renewRate',
        symbol: '%',
        showLabel: true,
        labelSymbol: '%',
        extraList: [
          { name: '到期患者', key: 'expirationNum' },
          { name: '到期续费患者', key: 'renewNum' },
        ],
      }),
      markLine: {
        silent: true,
        label: {
          color: '#E63746',
          formatter: function (params) {
            return params.name + ': ' + percent + '%';
          },
        },
        symbol: 'none',
        lineStyle: {
          color: '#E63746',
          type: 'dashed',
        },
        data: [{ name: '平均', yAxis: percent }],
      },
    },
  ];
  return {
    xData,
    data: data,
  };
});
const refundData = computed(() => workStore.refund);

onMounted(() => {
  workStore.getAllData();
});
defineOptions({
  name: 'MedicalWork',
});
</script>

<style scoped lang="less">
.toolbar {
  width: 100%;
  height: 32px;
  display: flex;
  justify-content: space-between;
}
.right {
  height: 150px;
  margin-right: 16px;
  padding: 16px;
  width: calc(100% - 668px);
  background: #f7f8fa;
  border-radius: 4px;
  display: flex;
  .pie-item {
    width: 50%;
    &:first-child {
      margin-right: 36px;
      position: relative;
      &:after {
        content: '';
        position: absolute;
        height: 58px;
        width: 0;
        border-right: 1px solid #e1e5ed;
        right: 20px;
        top: 46px;
      }
    }
  }
}
.sub-block {
  width: 50%;
  flex: 1;
  flex-shrink: 0;
  position: relative;
  margin-left: 24px;
  &:before {
    position: absolute;
    content: '/';
    font-size: 0;
    top: 40px;
    left: -24px;
    height: 58px;
    border-left: 1px solid #e1e5ed;
  }
}
.bottom {
  font-size: 14px;
  display: flex;
  margin-top: 36px;
  justify-content: space-between;
  .item {
    width: 48%;
    display: flex;
    justify-content: space-between;
    > span:first-child {
      color: #3a4762;
      font-weight: normal;
      height: 40px;
      display: inline-block;
    }
  }
}
</style>
