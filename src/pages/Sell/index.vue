<script setup lang="ts">
import Anchor from '@/components/Anchor/index.vue';
import Card from '@/components/Card/index.vue';
import Comparission from '@/components/Comparission/index.vue';
import FixedHeader from '@/components/FixedHeader/index.vue';
import GroupBarChart from '@/components/GroupBarChart/index.vue';
import LayoutCard from '@/components/LayoutCard/index.vue';
import PieChart from '@/components/PieChart/index.vue';
import Times from '@/components/Times/index.vue';
import { SELL_ANCHOR } from '@/constant';
import useSell from '@/store/useSell';
import useSellDrilling from '@/store/useSellDrilling';
import {
  getDatesRange,
  getMergeType,
  isWholeYear,
  toThousands,
  transformNewOrderData,
  transformNoBuyData,
  transformPieData,
  transformWorkPlan,
} from '@/utils';
import {
  getSellerGroupWorkSeriesData,
  getSellerNewOrderTrendSeriesData,
} from '@/utils/transformData';
import BuyAnalysis from './components/drilling/BuyAnalysis.vue';
import GroupOrderDrilling from './components/drilling/GroupOrderAnalysis.vue';
import NewOrderDrilling from './components/drilling/NewOrderAnalysis.vue';
import RefundAnalysis from './components/drilling/RefundAnalysis.vue';
import UnBuyAnalysis from './components/drilling/UnBuyAnalysis.vue';
import WorkPlanAnalysis from './components/drilling/WorkPlanAnalysis.vue';

import NewOrder from './components/module/NewOrder.vue';
import RenewAndRefund from './components/module/RenewAndRefund.vue';
import UnBuy from './components/module/UnBuy.vue';
import WorkPlan from './components/module/WorkPlan.vue';

defineOptions({
  name: 'Sell',
});
const sellStore = useSell();
const drillingStore = useSellDrilling();
const groupOrderVisible = ref(false);
const newOrderVisible = ref(false);
const buyAnalysisVisible = ref(false);
const unBuyAnalysisVisible = ref(false);
const refundAnalysisVisible = ref(false);
const workPlanAnalysisVisible = ref(false);
const portrayal = ref(false);

const orderTrendData = computed(() => {
  const item = sellStore.orderTrend.orderDateData?.all;
  return getSellerNewOrderTrendSeriesData(item);
});
const ledgerDealData = computed(() => {
  const item = sellStore.ledgerDeal.all ?? {};
  return transformNewOrderData(item, portrayal.value);
});
const ledgerNoDealData = computed(() => {
  const item = sellStore.ledgerNoDeal.all ?? {};
  return transformNoBuyData(item);
});
const workPanData = computed(() => {
  const item = sellStore.workPlan ?? {};
  const { taskData, hourData } = transformWorkPlan(item);
  const { taskBarData, hourBarData } = getSellerGroupWorkSeriesData(
    item,
    'itemTotalAll',
  );
  return {
    taskData: { ...taskData, barData: taskBarData.data },
    hourData: { ...hourData, barData: hourBarData.data },
  };
});
function setDrillingDate(date) {
  const type
    = getMergeType(drillingStore.times) === 'BY_DAY_FIRST' ? 'day' : 'month';
  const res = getDatesRange(date, type);
  drillingStore.tempTimes = res;
}
function newOrderClickItem(item) {
  const curTime = item?.data?.otherParams?.timeStamp;
  if (!curTime)
    return;
  setDrillingDate(curTime);
  newOrderVisible.value = true;
}
function clickWorkPlanItemHandler(item) {
  const curTime = item?.data?.otherParams?.timeStamp;
  if (!curTime)
    return;
  setDrillingDate(curTime);
  workPlanAnalysisVisible.value = true;
}
function dateChangeHandler(val) {
  sellStore.times = val;
  drillingStore.times = val;
  getPageData();
}
function categoryChange(val) {
  portrayal.value = val?.length > 0;
  const params = {
    ledgerType: val.length ? 'EXIST_LEDGER' : 'ALL_LEDGER',
    outHospitalTypes: val,
  };
  sellStore.getLedgerDeal(params);
}
function getPageData() {
  sellStore.pageInit();
}
</script>

<template>
  <div>
    <FixedHeader :top="170">
      <div class="toolbar">
        <Times @date-change="dateChangeHandler" />
        <Anchor :data="SELL_ANCHOR" />
      </div>
    </FixedHeader>
    <div id="anchor_sell_1" v-loading="sellStore.loadings[0]">
      <LayoutCard title="工作室新购" title-line>
        <div class="content">
          <div class="left card">
            <div>
              <Card title="开单工作室" color="#2e6be6">
                {{
                  sellStore.groupData?.orderPaidGroupNums
                }}
              </Card>
            </div>
            <div class="total mt-32">
              <span>全部工作室</span><span>{{ sellStore.groupData?.allGroupNums }}</span>
            </div>
            <div class="more" @click="() => (groupOrderVisible = true)">
              更多 >
            </div>
          </div>
          <div class="right card">
            <div class="shrink-0">
              <div>
                <Card title="有效订单/订单金额" color="#2e6be6">
                  {{ sellStore.groupData?.effectiveOrderNum }}/{{
                    toThousands(sellStore.groupData?.orderAmount)
                  }}
                </Card>
              </div>
              <div class="mt-32">
                <Comparission
                  :hide-yoy="isWholeYear(sellStore.times)"
                  :yoy="sellStore.groupData?.yoyOrderPercent"
                  :mom="sellStore.groupData?.momOrderPercent"
                  :inline="true"
                />
              </div>
            </div>
            <div class="w-140 shrink-0 ml-24">
              <Card title="指标完成">
                <div class="mt-7 ml-4 progress">
                  <el-progress
                    type="circle"
                    :stroke-width="14"
                    :width="90"
                    :indeterminate="true"
                    :percentage="sellStore.groupData?.quotaCompletePercent || 0"
                    color="#5285EB"
                  >
                    <template #default="{ percentage }">
                      <span>
                        <span class="text-[14px]">{{ percentage }}</span>%
                      </span>
                    </template>
                  </el-progress>
                </div>
              </Card>
            </div>
            <div class="sub-block" style="flex: 1">
              <PieChart
                title="省份占比"
                :bold="false"
                mt="4px"
                :data="
                  transformPieData(sellStore.groupData?.provinceSummary, {
                    name: 'provinceName',
                    value: 'orderNum',
                    percent: 'orderPercent',
                  })
                "
                :legend-item-width="200"
                :chart-width="100"
              />
            </div>
            <div class="sub-block flex-1">
              <PieChart
                title="部门占比"
                :bold="false"
                mt="4px"
                :data="
                  transformPieData(sellStore.groupData?.deptSummary, {
                    name: 'deptName',
                    value: 'orderNum',
                    percent: 'orderPercent',
                  })
                "
                :chart-width="100"
              />
            </div>
          </div>
        </div>
      </LayoutCard>
    </div>
    <div id="anchor_sell_2" v-loading="sellStore.loadings[1]" class="mt-16">
      <LayoutCard
        title="新购订单走势"
        :height="400"
        tips="true"
        show-more
        @click-more="() => (newOrderVisible = true)"
      >
        <template #tipContent>
          <div><span>月指标:</span> 当月销售目标</div>
          <div><span>日指标:</span> 当月销售目标/当月天数</div>
          <div><span>有效订单:</span> 当月成交，且未在40天内退款的订单</div>
        </template>
        <GroupBarChart
          :x-data="orderTrendData.xData"
          :data="orderTrendData.data"
          :grid="{ bottom: 80 }"
          :label-no-wrap="true"
          @click-item="newOrderClickItem"
        />
      </LayoutCard>
    </div>
    <div id="anchor_sell_3" v-loading="sellStore.loadings[2]" class="mt-16">
      <LayoutCard
        title="新购成交分析"
        show-more
        @click-more="() => (buyAnalysisVisible = true)"
      >
        <NewOrder :data="ledgerDealData" @category-change="categoryChange" />
      </LayoutCard>
    </div>
    <div id="anchor_sell_4" v-loading="sellStore.loadings[3]" class="mt-16">
      <LayoutCard
        title="未成交分析"
        show-more
        @click-more="() => (unBuyAnalysisVisible = true)"
      >
        <UnBuy :data="ledgerNoDealData" />
      </LayoutCard>
    </div>
    <div
      id="anchor_sell_5"
      v-loading="sellStore.loadings[4]"
      class="mt-16 flex"
    >
      <LayoutCard
        title="续费/退费"
        show-more
        :height="268"
        title-line
        @click-more="() => (refundAnalysisVisible = true)"
      >
        <RenewAndRefund
          :data="sellStore.renewAndRefoud"
          :hide-yoy="isWholeYear(sellStore.times)"
        />
      </LayoutCard>
    </div>
    <div id="anchor_sell_6" v-loading="sellStore.loadings[5]" class="mt-16">
      <LayoutCard
        title="团队工作计划"
        :height="1132"
        title-line
        show-more
        @click-more="() => (workPlanAnalysisVisible = true)"
      >
        <WorkPlan
          :task-data="workPanData.taskData"
          :hour-data="workPanData.hourData"
          @click-item="clickWorkPlanItemHandler"
        />
      </LayoutCard>
    </div>
    <GroupOrderDrilling
      v-model:visible="groupOrderVisible"
      title="工作室订单分析"
    />
    <NewOrderDrilling
      v-model:visible="newOrderVisible"
      title="订单新购达成及转化"
    />
    <BuyAnalysis v-model:visible="buyAnalysisVisible" title="转化效果分析" />
    <UnBuyAnalysis v-model:visible="unBuyAnalysisVisible" title="未成交分析" />
    <RefundAnalysis
      v-model:visible="refundAnalysisVisible"
      title="用户退费分析"
    />
    <WorkPlanAnalysis
      v-model:visible="workPlanAnalysisVisible"
      title="团队工作计划情况"
    />
  </div>
</template>

<style scoped lang="less">
@import url(../../styles/common.less);
.item {
  width: 800px;
  height: 400px;
  border: 1px solid #ccc;
  margin: 20px;
}
.toolbar {
  width: 100%;
  height: 32px;
  display: flex;
  justify-content: space-between;
}
.content {
  display: flex;
  padding-top: 16px;
  .card {
    height: 150px;
    background: #f7f8fa;
    padding: 16px;
  }
  .left {
    position: relative;
    width: 240px;
    margin-right: 16px;
    flex-shrink: 0;
    .more {
      position: absolute;
      top: 16px;
      right: 16px;
      font-size: 14px;
      cursor: pointer;
      color: #2e6be6;
    }
  }
  .right {
    display: flex;
    flex: 1;
    > div {
      height: 100%;
    }
  }
}
.total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  > span:first-child {
    color: #939cae;
    font-size: 14px;
  }
  > span:last-child {
    color: #3a4762;
    font-size: 20px;
  }
}
.progress {
  :deep(svg path:first-child) {
    stroke: #e6eeff;
  }
}
.sub-block {
  position: relative;
  margin-left: 24px;
  &:before {
    position: absolute;
    content: '/';
    font-size: 0;
    top: 40px;
    left: -24px;
    height: 58px;
    border-left: 1px solid #e1e5ed;
  }
}
</style>
