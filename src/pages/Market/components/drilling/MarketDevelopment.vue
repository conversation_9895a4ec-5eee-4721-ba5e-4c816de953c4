<template>
  <Drawer :visible="drawerVisible" :title="title" @close="closeHandler">
    <Search
      v-if="visible"
      :disabled="loading"
      :form-value="{ dates: drillingStore.getCurTimes() }"
      inline
      :enable-keys="['province', 'city', 'dates']"
      @search-change="searchChangeHandler"
    />
    <Summary
      :disabled="loading"
      :value="searchParams.mergeMethod"
      @on-change="summaryChange"
    />
    <LayoutCard v-loading="checkLoading(0)" title="全部" :padding="0" mt="24px">
      <div class="mt-16 mb-16">
        <Statistic
          width="175"
          block
          :data="[...allChartData.hTotal, ...allChartData.sTotal]"
        />
      </div>
      <Development
        :hospital-data="allChartData.hospitalData"
        :surgrey-data="allChartData.surgreyData"
        :size="'mini'"
      />
    </LayoutCard>
    <LayoutCard v-loading="checkLoading(1)" title="部门" :padding="0" mt="36px">
      <Selector
        base="部门"
        :selected-ids="drillingStore.developData.selectedDeptIds"
        :data="
          transformSelectorList(drillingStore.developData.deptStructList, {
            id: 'deptId',
            name: 'deptName',
            pId: 'deptParentId',
          })
        "
        @change="deptSelectorChange"
      />
      <div class="mt-16 mb-16">
        <Statistic
          width="175"
          block
          :data="[...groupChartData.hTotal, ...groupChartData.sTotal]"
        />
      </div>
      <Development
        :hospital-data="groupChartData.hospitalData"
        :surgrey-data="groupChartData.surgreyData"
      >
        <template #hospital>
          <GroupStackBarChart
            title="核心医院开发"
            :size="'mini'"
            :grid="{ bottom: 70 }"
            :data-zoom-config="{ height: 20 }"
            :label-no-wrap="true"
            :x-data="groupChartData.hospitalData.xData"
            :data="groupChartData.hospitalData.data"
            :legends="groupChartData.hospitalData.legends"
          />
        </template>
        <template #surgery>
          <GroupFlatBarChart
            title="手术量开发"
            :size="'mini'"
            :grid="{ bottom: 70 }"
            :data-zoom-config="{ height: 20 }"
            :label-no-wrap="true"
            :x-data="groupChartData.surgreyData.xData"
            :data="groupChartData.surgreyData.data"
          />
        </template>
      </Development>
    </LayoutCard>
    <LayoutCard v-loading="checkLoading(2)" title="个人" :padding="0" mt="36px">
      <Selector
        base="市场人员"
        :selected-ids="drillingStore.developData.selectedMarketIds"
        :data="
          transformSelectorList(drillingStore.developData.marketStructList, {
            id: 'userId',
            name: 'userName',
          })
        "
        @change="personSelectorChange"
      />
      <div class="mt-16 mb-16">
        <Statistic
          width="175"
          block
          :data="[...marketChartData.hTotal, ...marketChartData.sTotal]"
        />
      </div>
      <Development
        :hospital-data="marketChartData.hospitalData"
        :surgrey-data="marketChartData.surgreyData"
      >
        <template #hospital>
          <GroupStackBarChart
            title="核心医院开发"
            :size="'mini'"
            :grid="{ bottom: 70 }"
            :data-zoom-config="{ height: 20 }"
            :label-no-wrap="true"
            :x-data="marketChartData.hospitalData.xData"
            :data="marketChartData.hospitalData.data"
            :legends="marketChartData.hospitalData.legends"
          />
        </template>
        <template #surgery>
          <GroupFlatBarChart
            title="手术量开发"
            :size="'mini'"
            :grid="{ bottom: 70 }"
            :data-zoom-config="{ height: 20 }"
            :label-no-wrap="true"
            :x-data="marketChartData.surgreyData.xData"
            :data="marketChartData.surgreyData.data"
          />
        </template>
      </Development>
    </LayoutCard>
  </Drawer>
</template>

<script setup lang="ts">
import Drawer from '@/components/Drawer/index.vue';
import Search from '@/components/Search/index.vue';
import LayoutCard from '@/components/LayoutCard/index.vue';
import Selector from '@/components/Selector/index.vue';
import Statistic from '@/components/Statistic/index.vue';
import Summary from '@/components/Summary/index.vue';
import GroupFlatBarChart from '@/components/GroupFlatBarChart/index.vue';
import GroupStackBarChart from '@/components/GroupStackBarChart/index.vue';
import Development from '../module/Development.vue';
import useMarketDrilling from '@/store/useMarketDrilling';
import { transformSelectorList } from '@/utils';
import { getMarketDevelopSeriesData } from '@/utils/transformData';
import { useMarketSearch } from '@/hooks/useMarketSearch';
import { useLoading } from '@/hooks/useLoading';

interface IProps {
  title: string;
  visible: boolean;
}

const { loading, loadingType, checkLoading } = useLoading();
const drillingStore = useMarketDrilling();
const props = defineProps<IProps>();
const emit = defineEmits(['update:visible']);
const drawerVisible = ref(false);
const {
  searchParams,
  normalInit,
  summaryChange,
  searchChangeHandler,
  deptSelectorChange,
  personSelectorChange,
} = useMarketSearch(drillingStore.getDevelopData, loading, loadingType, [
  'provinceName',
  'regionId',
]);
const getHTotal = item => {
  return [
    { text: '目标医院', value: item?.targetHospitalNum },
    { text: '已开发医院', value: item?.developHospitalNum },
    {
      text: '医院开发进度',
      value:
        item?.hospitalDevelopPercent !== undefined
          ? +item?.hospitalDevelopPercent + '%'
          : '',
    },
  ];
};
const getSTotal = item => {
  return [
    { text: '预估手术量', value: item?.estSurgicalVolume },
    { text: '已开发手术量', value: item?.developOperationNum },
    {
      text: '手术量开发进度',
      value:
        item?.developOperationPercent !== undefined
          ? item?.developOperationPercent + '%'
          : '',
    },
  ];
};
const allChartData = computed(() => {
  const item = drillingStore.developData;
  const hTotal = getHTotal(item.operationNum?.all);
  const sTotal = getSTotal(item.operationNum?.all);
  const res = getMarketDevelopSeriesData(item);
  return { ...res, hTotal, sTotal };
});
const groupChartData = computed(() => {
  const item = drillingStore.developData;
  const hTotal = getHTotal(item.operationNum?.dept);
  const sTotal = getSTotal(item.operationNum?.dept);
  const res = getMarketDevelopSeriesData(item, 'deptName', 'dept', true);
  return { ...res, hTotal, sTotal };
});
const marketChartData = computed(() => {
  const item = drillingStore.developData;
  const hTotal = getHTotal(item.operationNum?.market);
  const sTotal = getSTotal(item.operationNum?.market);
  const res = getMarketDevelopSeriesData(item, 'marketName', 'market', true);
  return { ...res, hTotal, sTotal };
});

const closeHandler = () => {
  drawerVisible.value = false;
  emit('update:visible', false);
};
const init = () => {
  normalInit(drillingStore.getCurTimes());
};
watch(
  () => props.visible,
  val => {
    drawerVisible.value = val;
    if (val) {
      init();
    } else {
      searchParams.value = {};
      drillingStore.tempTimes = [];
      drillingStore.developData = {};
    }
  }
);

defineOptions({
  name: 'MarketDevelopment',
});
</script>

<style scoped lang="less">
// todo
</style>
