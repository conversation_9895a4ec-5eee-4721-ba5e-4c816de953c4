<template>
  <div class="contain">
    <div v-loading="marketStore.loadings[0]" class="flex mb-16">
      <div class="block-item mr-16 w-[50%]">
        <SubCard height="182px">
          <div class="flex">
            <div class="h-110">
              <Card style="min-width: 260px" title="已开发手术量/预估手术量"
                >{{ sumData?.developSurgicalVolume }}/{{
                  sumData?.estSurgicalVolume
                }}</Card
              >
              <Progress
                label="手术量开发进度"
                :value="sumData?.surgicalDevelopPercent"
              />
            </div>

            <div class="flex-1 h-150">
              <PieChart
                title="省份占比"
                :bold="false"
                mt="12px"
                :chart-width="110"
                :legend-height="120"
                :data="
                  transformPieData(sumData?.provinceList, {
                    name: 'provinceName',
                    value: 'provinceSurgicalVolume',
                    percent: 'provinceSurgicalPercent',
                  })
                "
              />
            </div>
          </div>
        </SubCard>
      </div>
      <div class="block-item w-[50%]">
        <SubCard height="182px" pure>
          <div class="flex">
            <div class="h-110">
              <Card item-width="220px" title="已开发医院/目标医院"
                >{{ sumData?.developHospitalNum }}/{{
                  sumData?.targetHospitalNum
                }}</Card
              >
              <!-- <Progress
                label="医院开发进度"
                :value="sumData?.hospitalDevelopPercent"
              /> -->
            </div>
            <div class="middle">
              <div
                v-for="item in sumLevelData"
                :key="item?.title"
                class="middle-item"
              >
                <div class="title">
                  <span class="label">{{ item?.title }}</span>
                  <span class="value"
                    >{{ item?.total?.[1] }}/{{ item?.total?.[0] }} ({{
                      item?.percent
                    }}%)</span
                  >
                </div>
                <div class="content">
                  <div>
                    <span class="label">待开发</span>
                    <span class="value">{{ item.detail[0] }}</span>
                  </div>
                  <div>
                    <span class="label">开发中</span>
                    <span class="value">{{ item.detail[1] }}</span>
                  </div>
                  <div>
                    <span class="label">已暂停</span>
                    <span class="value">{{ item.detail[2] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SubCard>
      </div>
    </div>
    <FixedHeader :top="120">
      <div class="toolbar">
        <Times @date-change="dateChangeHandler" />
        <Anchor :data="MARKET_ANCHOR" />
      </div>
    </FixedHeader>
    <div id="anchor_market_1" v-loading="marketStore.loadings[1]">
      <LayoutCard
        title-line
        tips="true"
        show-more
        title="市场开发情况"
        @click-more="() => (developVisible = true)"
      >
        <template #tipContent>
          <div><span>核心医院:</span> 处于开发流程中的医院</div>
          <div><span>手术量开发:</span> 以市场交接的工作室手术量为准</div>
        </template>
        <Development
          :hospital-data="developData.hospitalData"
          :surgrey-data="developData.surgreyData"
          @click-item="clickDevelopItemHandler"
        />
      </LayoutCard>
    </div>
    <div id="anchor_market_2" v-loading="marketStore.loadings[2]" class="mt-16">
      <LayoutCard
        title-line
        show-more
        title="团队工作"
        @click-more="() => (groupWorkVisible = true)"
      >
        <GroupWork
          :x-data="visitData.xData"
          :data="visitData.data"
          @click-item="clickGroupWorkItemHandler"
        />
      </LayoutCard>
    </div>
    <div id="anchor_market_3" v-loading="marketStore.loadings[3]" class="mt-16">
      <LayoutCard
        title-line
        show-more
        title="团队工作计划"
        @click-more="() => (workPlanVisible = true)"
      >
        <WorkPlan
          :task-data="workPanData.taskData"
          :hour-data="workPanData.hourData"
          @click-item="clickWorkPlanItemHandler"
        />
      </LayoutCard>
    </div>
  </div>
  <MarketDevelopment v-model:visible="developVisible" title="市场开发情况" />
  <GroupWorkAnalysis v-model:visible="groupWorkVisible" title="用户拜访记录" />
  <WorkPlanAnalysis
    v-model:visible="workPlanVisible"
    title="团队工作计划情况"
  />
</template>

<script setup lang="ts">
import Anchor from '@/components/Anchor/index.vue';
import Card from '@/components/Card/index.vue';
import FixedHeader from '@/components/FixedHeader/index.vue';
import LayoutCard from '@/components/LayoutCard/index.vue';
import PieChart from '@/components/PieChart/index.vue';
import Progress from '@/components/Progress/index.vue';
import SubCard from '@/components/SubCard/index.vue';
import Times from '@/components/Times/index.vue';
import { MARKET_ANCHOR } from '@/constant';
import WorkPlan from '@/pages/Sell/components/module/WorkPlan.vue';
import useMarket from '@/store/useMarket';
import useMarketDrilling from '@/store/useMarketDrilling';
import {
  getDatesRange,
  getMergeType,
  transformPieData,
  transformWorkPlan,
} from '@/utils';
import {
  getMarketDevelopSeriesData,
  getMarketVisitSeriesData,
  getSellerGroupWorkSeriesData,
} from '@/utils/transformData';
import GroupWorkAnalysis from './components/drilling/GroupWorkAnalysis.vue';
import MarketDevelopment from './components/drilling/MarketDevelopment.vue';
import WorkPlanAnalysis from './components/drilling/WorkPlanAnalysis.vue';
import Development from './components/module/Development.vue';
import GroupWork from './components/module/GroupWork.vue';

const marketStore = useMarket();
const marketDrillingStore = useMarketDrilling();
const developVisible = ref(false);
const groupWorkVisible = ref(false);
const workPlanVisible = ref(false);

const sumData = computed(() => marketStore.sumData);

const sumLevelData = computed(() => {
  const v = sumData.value ?? {};
  return [
    {
      title: '甲级',
      total: [v.firstTargetHospitalNum, v.firstDevelopHospitalNum],
      percent: v.firstHospitalDevelopPercent,
      detail: [
        v.firstWaitDevelopNum,
        v.firstDevelopingNum,
        v.firstPauseDevelopNum,
      ],
    },
    {
      title: '乙级',
      total: [v.secondTargetHospitalNum, v.secondDevelopHospitalNum],
      percent: v.secondHospitalDevelopPercent,
      detail: [
        v.secondWaitDevelopNum,
        v.secondDevelopingNum,
        v.secondPauseDevelopNum,
      ],
    },
    {
      title: '丙级',
      total: [v.threeTargetHospitalNum, v.threeDevelopHospitalNum],
      percent: v.threeHospitalDevelopPercent,
      detail: [
        v.threeWaitDevelopNum,
        v.threeDevelopingNum,
        v.threePauseDevelopNum,
      ],
    },
  ];
});
const developData = computed(() => {
  const item = marketStore.developData;
  const { hospitalData, surgreyData } = getMarketDevelopSeriesData(item);
  return { hospitalData, surgreyData };
});
const visitData = computed(() => {
  const item = marketStore.visitData?.visitStatList?.all;
  return getMarketVisitSeriesData(item);
});
const workPanData = computed(() => {
  const item = marketStore.workPlan ?? {};
  const { taskData, hourData } = transformWorkPlan(item);
  const { taskBarData, hourBarData } = getSellerGroupWorkSeriesData(
    item,
    'itemTotalAll'
  );
  return {
    taskData: { ...taskData, barData: taskBarData.data },
    hourData: { ...hourData, barData: hourBarData.data },
  };
});
const setDrillingDate = date => {
  const type =
    getMergeType(marketDrillingStore.times) === 'BY_DAY_FIRST'
      ? 'day'
      : 'month';
  const res = getDatesRange(date, type);
  marketDrillingStore.tempTimes = res;
};
const clickWorkPlanItemHandler = item => {
  const curTime = item?.data?.otherParams?.timeStamp;
  if (!curTime) return;
  setDrillingDate(curTime);
  workPlanVisible.value = true;
};
const clickGroupWorkItemHandler = item => {
  const curTime = item?.data?.otherParams?.timeStamp;
  if (!curTime) return;
  setDrillingDate(curTime);
  groupWorkVisible.value = true;
};
const clickDevelopItemHandler = item => {
  const curTime = item?.data?.otherParams?.timeStamp;
  if (!curTime) return;
  setDrillingDate(curTime);
  developVisible.value = true;
};
const dateChangeHandler = val => {
  marketStore.times = val;
  marketDrillingStore.times = val;
  getPageData();
};
const getPageData = () => {
  marketStore.pageInit();
};
onMounted(() => {
  marketStore.getSumData();
});

defineOptions({
  name: 'Market',
});
</script>

<style scoped lang="less">
.block-item {
  background: #fff;
  border-radius: 4px;
}
.toolbar {
  width: 100%;
  height: 32px;
  display: flex;
  justify-content: space-between;
}
.middle {
  height: 150px;
  display: flex;
  flex: 1;
}
.middle-item {
  background: #f7f8fa;
  border-radius: 4px;
  flex-shrink: 0;
  padding: 16px;
  display: flex;
  margin-right: 14px;
  width: calc(33% - 6px);
  position: relative;
  flex-wrap: wrap;
  .title {
    width: 100%;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    font-weight: bold;
    position: relative;
    padding-bottom: 12px;
    border-bottom: 1px solid #e1e5ed;
    .label {
      color: #3a4762;
    }
    .value {
      color: #2e6be6;
    }
  }
  .content {
    margin-top: 4px;
    width: 100%;
    font-size: 14px;
    > div {
      width: 100%;
      display: flex;
      justify-content: space-between;
      height: 20px;
      margin-top: 8px;
      .label {
        color: #7a8599;
      }
      .value {
        color: #3a4762;
        font-weight: bold;
      }
    }
  }
}
.more {
  position: absolute;
  right: 8px;
  top: 24px;
  font-size: 14px;
  color: #2e6be6;
  cursor: pointer;
}
</style>
