<template>
  <div class="contain">
    <div v-loading="customerStore.loadings[0]" class="mb-16">
      <LayoutCard
        title="全部客户分布"
        title-line
        :hide-symbol="true"
        show-more
        @click-more="() => (totalVisible = true)"
      >
        <div class="flex pt-16">
          <Card block title="客户数量" item-height="150px" item-width="288px">{{
            sumData?.customerTotalNum
          }}</Card>
          <div class="right">
            <div class="sub-block" style="width: 31%">
              <PieChart
                title="转化价值占比"
                :bold="false"
                mt="4px"
                :chart-width="100"
                :data="
                  transformPieData(sumData?.transformValueList, {
                    name: 'transformValueType',
                    value: 'customerNum',
                    percent: 'customerPercent',
                  })
                "
              />
            </div>
            <div class="sub-block" style="width: 31%">
              <PieChart
                title="kol 占比"
                :bold="false"
                mt="4px"
                :chart-width="100"
                :data="
                  transformPieData(sumData?.kolList, {
                    name: 'kolType',
                    value: 'customerNum',
                    percent: 'customerPercent',
                  })
                "
              />
            </div>
            <div class="sub-block" style="width: 31%">
              <PieChart
                title="观念占比"
                :bold="false"
                mt="4px"
                :chart-width="100"
                :data="
                  transformPieData(sumData?.conceptList, {
                    name: 'conceptType',
                    value: 'customerNum',
                    percent: 'customerPercent',
                  })
                "
              />
            </div>
          </div>
        </div>
      </LayoutCard>
    </div>
    <FixedHeader :top="120">
      <div class="toolbar">
        <div class="date">
          <Times
            :enable-keys="['today', 'date']"
            @date-change="dateChangeHandler"
          >
            <Summary
              style="padding: 0; border-bottom: 0"
              color="#3A4762"
              label="查看维度"
              :value="customerStore.type"
              :data="CUSTOMER_DS"
              @on-change="categoryChange"
            />
          </Times>
        </div>
      </div>
    </FixedHeader>
    <div class="customer-card">
      <!-- <div class="relative pt-8 ml-16 mr-16">
        <div class="more">更多 ></div>
      </div> -->
      <SubCard v-loading="customerStore.loadings[1]" pure padding="24px">
        <BarChart
          :height="360"
          color="#5285EB"
          :grid="grid"
          :y-axis="yaxis"
          :x-data="transformData.xData"
          :data="transformData.data"
          title="转化价值分析"
          desc="根据预估手术量、当月付费订单量、当月转化率进行综合评价"
          :enable-click-listener="true"
          :tooltip="customTooltip"
          @click-item="clickConversionBarItem"
        />
      </SubCard>
      <SubCard v-loading="customerStore.loadings[2]" pure padding="24px">
        <BarChart
          :height="360"
          more-text="更多 >"
          color="#5285EB"
          :grid="{ ...grid, left: 80 }"
          :y-axis="cashYaxis"
          :x-data="cashData.xData"
          :data="cashData.data"
          :data-zoom-config="{
            endValue: ['needWardData', 'needHospitalData'].includes(
              customerStore.type
            )
              ? 17
              : 29,
          }"
          :label-no-wrap="true"
          title="累计现金投产比排名"
          desc="根据客户的直接现金投入和订单量进行综合计算"
          @click-more="() => (cashVisible = true)"
        />
      </SubCard>
    </div>
  </div>
  <CustomerAnalysis
    v-model:visible="totalVisible"
    :type="'needCustomerData'"
    title="客户分布情况"
  />
  <ConversionAnalysis
    v-model:visible="conversionVisible"
    title="工作室转化价值分析"
    :type="customerStore.type"
    :time="customerStore.time"
    :ids="ids"
  />
  <CashAnalysis
    v-model:visible="cashVisible"
    :type="customerStore.type"
    :time="customerStore.time"
    title="现金投产比分析"
  />
</template>

<script setup lang="ts">
import BarChart from '@/components/BarChart/index.vue';
import Card from '@/components/Card/index.vue';
import FixedHeader from '@/components/FixedHeader/index.vue';
import LayoutCard from '@/components/LayoutCard/index.vue';
import PieChart from '@/components/PieChart/index.vue';
import SubCard from '@/components/SubCard/index.vue';
import Summary from '@/components/Summary/index.vue';
import Times from '@/components/Times/index.vue';
import { CUSTOMER_DS, CUSTOMER_DS_MAP } from '@/constant';
import useCustomer from '@/store/useCustomer';
import { customerTransformTooltip, transformPieData } from '@/utils';
import {
  getCustomerCashSeriesData,
  getCustomerTransformSeriesData,
} from '@/utils/transformData';
import CashAnalysis from './components/drilling/CashAnalysis.vue';
import ConversionAnalysis from './components/drilling/ConversionAnalysis.vue';
import CustomerAnalysis from './components/drilling/CustomerAnalysis.vue';
import { cashYaxis, grid, yaxis } from './config';

const customerStore = useCustomer();
const totalVisible = ref(false);
const conversionVisible = ref(false);
const cashVisible = ref(false);
const ids = ref<number[]>([]);

const customTooltip = computed(() => {
  return customerTransformTooltip(customerStore.time, customerStore.type);
});
const sumData = computed(() => customerStore.sumData);
const transformData = computed(() => {
  return getCustomerTransformSeriesData(
    customerStore.transformData,
    CUSTOMER_DS_MAP[customerStore.type].field
  );
});
const cashData = computed(() => {
  return getCustomerCashSeriesData(
    customerStore.cashData,
    CUSTOMER_DS_MAP[customerStore.type].field
  );
});
const categoryChange = val => {
  customerStore.type = val;
  init();
};
const dateChangeHandler = val => {
  customerStore.time = val;
  init();
};
const clickConversionBarItem = data => {
  const field = CUSTOMER_DS_MAP[customerStore.type].field;
  const dataList = customerStore.transformData[field + 'List'];
  const curItem = dataList[data.dataIndex];
  const curIds = curItem[field + 'Id'];
  ids.value = curIds;
  conversionVisible.value = true;
};
const init = () => {
  customerStore.pageInit();
};
onMounted(() => {
  customerStore.getCustomSumData();
});

defineOptions({
  name: 'MainBoard',
});
</script>

<style scoped lang="less">
.right {
  display: flex;
  background: #f7f8fa;
  width: calc(100% - 210px);
}
.sub-block {
  padding: 16px;
  height: 150px;
}
.toolbar {
  width: 100%;
  height: 32px;
  display: flex;
  justify-content: space-between;
}
.more {
  position: absolute;
  right: 8px;
  top: 24px;
  font-size: 14px;
  color: #2e6be6;
  cursor: pointer;
}
.customer-card {
  background: #fff;
  border-radius: 6px;
}
</style>
