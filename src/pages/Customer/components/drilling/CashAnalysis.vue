<script setup lang="ts">
import dayjs from 'dayjs';
import CheckGroup from '@/components/CheckGroup/index.vue';
import Drawer from '@/components/Drawer/index.vue';
import LineChart from '@/components/LineChart/index.vue';
import SubCard from '@/components/SubCard/index.vue';
import { CUSTOMER_DS_MAP } from '@/constant';
import { useLoading } from '@/hooks/useLoading';
import useCustomerDrilling from '@/store/useCustomerDrilling';
import { getEnd, getStart } from '@/utils';
import { getCustomerCashAnalySeriesData } from '@/utils/transformData';
import { grid } from '../../config';

interface CashAnalysisProps {
  title: string;
  visible: boolean;
  type: string;
  time: string;
}

defineOptions({
  name: 'CashAnalysis',
});
const props = defineProps<CashAnalysisProps>();
const emit = defineEmits(['update:visible']);
const drillingStore = useCustomerDrilling();
const drawerVisible = ref(false);
const { loading } = useLoading();
const selectedIds = computed(
  () => drillingStore.cashDataAnalysis?.businessIds ?? [],
);
const cashDataAnalysis = computed(() => {
  const item = drillingStore.cashDataAnalysis?.ratio;
  const xData = item?.map(v => v.key!);
  const res = getCustomerCashAnalySeriesData(item);
  return { xData, data: res };
});
function checkChangeHandler(val) {
  const params = { selectIds: val };
  searchHandler(params);
}
function searchHandler(params) {
  const mergeParams = {
    ...params,
    startTime: getStart(dayjs().subtract(1, 'year')),
    endTime: getEnd(Date.now()),
    [props.type]: true,
  };
  drillingStore.getCustomerCashRatioDataAnalysis(mergeParams, loading);
}
function closeHandler() {
  drawerVisible.value = false;
  emit('update:visible', false);
}
function init() {
  searchHandler({});
}
watch(
  () => props.visible,
  (val) => {
    drawerVisible.value = val;
    if (val) {
      init();
    }
    else {
      drillingStore.cashDataAnalysis = {};
    }
  },
);
</script>

<template>
  <Drawer :visible="drawerVisible" :title="title" @close="closeHandler">
    <div v-loading="loading">
      <CheckGroup
        v-if="visible"
        :label="CUSTOMER_DS_MAP[type].name"
        :data="drillingStore.cashDataAnalysis?.businessIdsInfo ?? []"
        :selected-ids="selectedIds"
        @on-change="checkChangeHandler"
      />
      <div class="mt-24">
        <SubCard pure>
          <LineChart
            :height="400"
            :zoom-size="13"
            :grid="{
              ...grid,
              left: 80,
            }"
            title="投产比走势"
            :x-data="cashDataAnalysis.xData"
            :data="cashDataAnalysis.data"
          />
        </SubCard>
      </div>
    </div>
  </Drawer>
</template>
