<template>
  <Drawer :visible="drawerVisible" :title="title" @close="closeHandler">
    <div v-loading="loading">
      <CheckGroup
        v-if="visible"
        :data="drillingStore.transformDataAynalysis?.businessIdsInfo ?? []"
        :selected-ids="selectedIds"
        :label="CUSTOMER_DS_MAP[type].name"
        @on-change="checkChangeHandler"
      />
      <div class="mt-24">
        <SubCard pure>
          <LineChart
            :height="400"
            :zoom-size="13"
            :grid="grid"
            title="转化价值走势"
            :x-data="transformDataAnalysis.xData"
            :data="transformDataAnalysis.data"
          />
        </SubCard>
      </div>
    </div>
  </Drawer>
</template>

<script setup lang="ts">
import Drawer from '@/components/Drawer/index.vue';
import CheckGroup from '@/components/CheckGroup/index.vue';
import SubCard from '@/components/SubCard/index.vue';
import LineChart from '@/components/LineChart/index.vue';
import useCustomerDrilling from '@/store/useCustomerDrilling';
import { getEnd, getStart } from '@/utils';
import { CUSTOMER_DS_MAP } from '@/constant';
import { getCustomerTfAnalySeriesData } from '@/utils/transformData';
import dayjs from 'dayjs';
import { useLoading } from '@/hooks/useLoading';

interface IProps {
  title: string;
  visible: boolean;
  time: string;
  type: string;
  ids: number[];
}

const drillingStore = useCustomerDrilling();
const grid = { top: 30, left: 50, right: 30, bottom: 80 };
const props = defineProps<IProps>();
const emit = defineEmits(['update:visible']);
const drawerVisible = ref(false);
const { loading } = useLoading();

const selectedIds = computed(
  () => drillingStore.transformDataAynalysis?.businessIds ?? []
);
const transformDataAnalysis = computed(() => {
  const item = drillingStore.transformDataAynalysis?.trans;
  const xData = item?.map(v => v.key!);
  const res = getCustomerTfAnalySeriesData(item);
  return { xData, data: res };
});
const checkChangeHandler = val => {
  const params = { selectIds: val };
  searchHandler(params);
};
const searchHandler = params => {
  const { type, ids } = props;
  const mergeParams = {
    ...params,
    startTime: getStart(dayjs().subtract(1, 'year')),
    endTime: getEnd(Date.now()),
    [type]: true,
    [CUSTOMER_DS_MAP[type].field + 'Ids']: ids,
  };
  drillingStore.getCustomerTransformDataAnalysis(mergeParams, loading);
};
const closeHandler = () => {
  drawerVisible.value = false;
  emit('update:visible', false);
};
const init = () => {
  searchHandler({});
};
watch(
  () => props.visible,
  val => {
    drawerVisible.value = val;
    if (val) {
      init();
    } else {
      drillingStore.transformDataAynalysis = {};
    }
  }
);

defineOptions({
  name: 'ConversionAnalysis',
});
</script>

<style scoped lang="less">
// todo
</style>
