<script setup lang="ts">
import {
  fetchDashboardMapData,
  fetchDashboardPatientData,
  getPatients,
} from '@/api';
import { DASHBOARD_REFRESH_INTERVAL } from '@/constant';
import { useQuery } from '@tanstack/vue-query';
import { computed } from 'vue';
import Header from './Header.vue';
import MainDiagnosticDistribution from './MainDiagnosticDistribution.vue';
import ManagingPatients from './ManagingPatients.vue';
import Map from './Map.vue';
import MarketDynamics from './MarketDynamics.vue';
import ReviewFollowUpRate from './ReviewFollowUpRate.vue';
import Statistic from './Statistic.vue';
import TargetComplianceRate from './TargetComplianceRate.vue';
import TrendOfConsultation from './TrendOfConsultation.vue';

defineOptions({
  name: 'Dashboard',
});

const { data } = useQuery({
  queryKey: ['ManagingPatients'],
  queryFn: () => getPatients(),
  refetchInterval: DASHBOARD_REFRESH_INTERVAL,
});
const { data: mapInfo } = useQuery({
  queryKey: ['fetchDashboardMapData'],
  queryFn: () => fetchDashboardMapData(),
  refetchInterval: DASHBOARD_REFRESH_INTERVAL,
});

const { data: patientsData } = useQuery({
  queryKey: ['fetchDashboardPatientData'],
  queryFn: () => fetchDashboardPatientData(),
  refetchInterval: DASHBOARD_REFRESH_INTERVAL,
});

const managingPatients = computed(() => {
  return data
    ? [
        {
          name: '科研会员',
          value: data.value?.researchPatientNums || 0,
        },
        {
          name: '续费会员',
          value: data.value?.renewPatientNums || 0,
        },
        {
          name: '新购会员',
          value: data.value?.newPatientNums || 0,
        },
      ]
    : [];
});
</script>

<template>
  <div class="dashboard">
    <Header />
    <div
      class="my-24 box-border grid flex-1 grid-cols-4 gap-32 overflow-hidden px-31"
      style="height: calc(100vh - 142px)"
    >
      <div class="col-span-1 flex h-full flex-col gap-38 overflow-hidden">
        <ManagingPatients :data="managingPatients" />
        <TrendOfConsultation
          :data="
            data?.patientMonthStat?.map(item => ({
              name: item.month,
              value: item.patientNums,
            }))
          "
        />
      </div>
      <div
        class="col-span-2 flex h-full flex-col overflow-hidden"
        style="height: calc(100vh - 142px)"
      >
        <Statistic
          :data="[
            {
              name: '省份',
              value: mapInfo?.provinceNums || 0,
            },
            {
              name: '地区',
              value: mapInfo?.regionNums || 0,
            },
            {
              name: '合作医院',
              value: mapInfo?.hospitalNums || 0,
            },
            {
              name: '专家工作室',
              value: mapInfo?.groupNums || 0,
            },
          ]"
        />
        <Map :data="mapInfo?.provinces" class="z-0 -mt-20" />
        <MarketDynamics :data="mapInfo?.logDetails" />
      </div>
      <div
        class="col-span-1 flex h-full flex-col gap-15 overflow-hidden"
        style="height: calc(100vh - 142px)"
      >
        <MainDiagnosticDistribution :data="data?.patientStat" />
        <div class="flex flex-1 flex-col gap-15">
          <TargetComplianceRate :data="patientsData?.indexStats" />
          <ReviewFollowUpRate :data="patientsData?.reviewStats" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import '@/styles/fonts.css';

.dashboard {
  width: 100%;
  height: 100dvh;
  background: url('/src/assets/imgs/dashboard/map-background.webp') no-repeat center center;
  @apply flex min-h-screen flex-col overflow-hidden bg-cover;
}
</style>
