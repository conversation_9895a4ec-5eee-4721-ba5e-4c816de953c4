<script setup lang="ts">
import { computed } from 'vue';

defineOptions({
  name: 'Statistic',
});
const { data = [] } = defineProps<{
  data?: { name: string; value: number }[];
}>();

const stats = computed(() =>
  data.map((item) => {
    return {
      name: item.name,
      value: Array.from(item.value.toString()),
    };
  }),
);
</script>

<template>
  <div class="z-10 flex w-full justify-between gap-26">
    <div
      v-for="(stat, index) in stats" :key="stat.name" class="card" :class="{
        'grow-[2]': index >= 2,
        'grow': index <= 1,
      }"
    >
      <div class="mb-8 text-base text-[#DEEEFA]">
        {{ stat.name }}
      </div>
      <div class="flex items-center justify-center gap-9">
        <div v-for="num in stat.value" :key="num" class="card-item">
          {{ num }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.card {
  background: url('@/assets/imgs/dashboard/statistic.webp') no-repeat;
  background-size: 100% 100%;
  height: 158px;
  @apply flex flex-col items-center justify-end px-46 pb-28;
}

.card-item {
  background: url('@/assets/imgs/dashboard/statistic-wrapper.webp') no-repeat;
  background-size: 100% 100%;
  width: 34px;
  color: #66ffff;
  font-size: 24px;
  height: 40px;
  @apply flex items-center justify-center font-black;
}
</style>
