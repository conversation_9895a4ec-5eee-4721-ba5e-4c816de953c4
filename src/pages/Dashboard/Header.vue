<script setup lang="ts">
import { useNow } from '@vueuse/core';
import dayjs from 'dayjs';
import { computed } from 'vue';
import headerImg from '@/assets/imgs/dashboard/header.webp';

const now = useNow();
const date = computed(() => {
  const date = new Date(now.value);
  const dayOfWeek = date.getDay();
  const dayOfWeekMap = ['日', '一', '二', '三', '四', '五', '六'];
  return dayjs(now.value).format(`YYYY年MM月DD日 周${dayOfWeekMap[dayOfWeek]}`);
});
const time = computed(() => {
  return dayjs(now.value).format('HH:mm:ss');
});
</script>

<template>
  <div
    class="flex h-94 w-full items-center justify-between bg-gradient-to-b from-[#071136] to-[#112a87] px-32"
  >
    <div class="w-[23%]">
      <img
        class="h-36 brightness-0 contrast-[100%] invert"
        src="/src/assets/imgs/header_logo.png"
        alt="heart med icon"
      >
    </div>
    <div class="title" :style="{ backgroundImage: `url(${headerImg})` }">
      哈瑞特医疗大数据中心
    </div>
    <div class="flex w-[23%] items-center justify-end">
      <div class="flex flex-col items-center">
        <span class="text-[10px] text-white">{{ date }}</span>
        <span class="text-lg font-bold text-white">{{ time }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.title {
  @apply flex w-full justify-center bg-no-repeat pt-30 text-[48px] leading-1 text-[#E0EFFA];
  font-family: '创客贴金刚体', sans-serif;
  background-size: 100% 100%;
  height: 118px;
  width: 1035px;
}
</style>
