<!-- 患者指标达标率 -->
<script setup lang="ts">
import type { IApiDpCarouselPatientIndexQueryIndexStats } from '@/interface/type';
import StackedAreaChart from '@/components/StackedAreaChart/index.vue';
import { formatDecimalTrim } from '@/lib';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import { ref } from 'vue';
import DashboardCard from './components/DashboardCard.vue';
import NavTabs from './components/NavTabs.vue';

defineOptions({
  name: 'TargetComplianceRate',
});

const { data = [] } = defineProps<TargetComplianceRateProps>();

export interface TargetComplianceRateProps {
  data?: IApiDpCarouselPatientIndexQueryIndexStats[];
}

const currentTab = ref([
  { label: '血压', color: '#0C66EC' },
  { label: '心率', color: '#40F3FE' },
  { label: '血糖', color: '#E2BB31' },
  { label: 'LDL-c', color: '#04D467' },
]);

const chartsData = computed(() => {
  const chartsSeriesData = cloneDeep(data);
  chartsSeriesData.sort(
    (a, b) => dayjs(a.month).valueOf() - dayjs(b.month).valueOf(),
  );
  // 只返回最后6个数据
  return chartsSeriesData.slice(-7, -1);
});

const chartsSeriesData = computed(() => {
  return [
    {
      name: '血压',
      data: chartsData.value.map(item =>
        formatDecimalTrim((item.bpStandTimes / item.bpTestTimes) * 100, 2),
      ),
    },
    {
      name: '心率',
      data: chartsData.value.map(item =>
        formatDecimalTrim((item.hrStandTimes / item.hrTestTimes) * 100),
      ),
    },
    {
      name: '血糖',
      data: chartsData.value.map(item =>
        formatDecimalTrim((item.bgStandTimes / item.bgTestTimes) * 100),
      ),
    },
    {
      name: 'LDL-c',
      data: chartsData.value.map(item =>
        formatDecimalTrim((item.ldlStandTimes / item.ldlTestTimes) * 100),
      ),
    },
  ];
});
</script>

<template>
  <DashboardCard title="会员指标达标率" class="box-border flex-1" small>
    <template #header>
      <NavTabs :tabs="currentTab" />
    </template>
    <div class="flex h-full flex-col pt-8">
      <div class="mb-8 flex items-center gap-11 pl-12 pt-8 text-sm">
        <span class="text-[#DEEEFA]">胸痛中心认证标准</span>
        <span class="text-white">60%</span>
      </div>
      <StackedAreaChart
        title="会员指标达标率"
        :y-axis-label-format="value => value > 0 ? `${value}%` : `${value}`"
        :x-axis-data="chartsData.map(item => item.month)"
        :series-data="chartsSeriesData"
        :smooth="false"
        :gradient="true"
        class="flex-1"
        :show-mark-line="true"
        :mark-line-value="60"
      />
    </div>
  </DashboardCard>
</template>
