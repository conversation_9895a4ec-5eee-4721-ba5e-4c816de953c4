<script setup lang="ts">
import blueTooltip from '@/assets/imgs/dashboard/doughnut-chart-tooltip-blue.webp';
import cyanBlueTooltip from '@/assets/imgs/dashboard/doughnut-chart-tooltip-cyan-blue.webp';
import greenTooltip from '@/assets/imgs/dashboard/doughnut-chart-tooltip-green.webp';
import redTooltip from '@/assets/imgs/dashboard/doughnut-chart-tooltip-red.webp';
import yellowTooltip from '@/assets/imgs/dashboard/doughnut-chart-tooltip-yellow.webp';
import { formatDecimal, formatNumber } from '@/lib';
import { PieChart } from 'echarts/charts';
import { GridComponent, LegendComponent } from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { cloneDeep } from 'lodash-es';
import { computed, ref, useAttrs, watch } from 'vue';
import VChart from 'vue-echarts';

export interface DoughnutChartProps {
  colors?: string[];
  seriesData?: {
    name: string;
    value: number;
    /** 百分比 */
    rate?: number;
  }[];
  /** 标题 */
  title?: string;
  /** 是否显示总计 */
  showTotal?: boolean;
}

defineOptions({
  name: 'DoughnutChart',
});

const {
  colors = ['#0C66EC', '#40F3FE', '#E2BB31', '#2ED57B', '#DE6230'],
  title = '',
  seriesData = [],
  showTotal = true,
} = defineProps<DoughnutChartProps>();

use([CanvasRenderer, PieChart, LegendComponent, GridComponent]);

const attrs = useAttrs();
const data = ref<DoughnutChartProps['seriesData']>([]);

const total = computed(() => {
  return data.value?.reduce((acc, curr) => acc + curr.value, 0) || 0;
});

const maxValue = computed(() => {
  return Math.max(...(data.value?.map(item => item.value) || []), 0);
});

/** 最大的环形项结束的角度 */
const endAngle = computed(() => {
  const maxAngle = Math.ceil((maxValue.value / total.value) * 360);
  if (maxAngle <= 90) {
    return 90 - maxAngle;
  }
  return Math.abs(360 - (maxAngle - 90));
});

const maxItem = computed(() => {
  return data.value?.find(item => item.value === maxValue.value) || null;
});

const backgroundImageUrls = [
  blueTooltip,
  cyanBlueTooltip,
  yellowTooltip,
  greenTooltip,
  redTooltip,
];

const option = computed(() => {
  return {
    color: colors,
    grid: {
      left: '0',
      right: '0',
      bottom: '0',
      top: '0',
      containLabel: true,
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: [74, 90],
        center: ['50%', '50%'],
        padAngle: 3,
        startAngle: 90,
        endAngle: endAngle.value,
        data: maxItem.value ? [maxItem.value] : [],
        label: {
          show: false,
          position: 'center',
        },
        labelLine: {
          show: false,
        },
      },
      {
        name: 'Access From',
        type: 'pie',
        padAngle: 3,
        radius: [80, 90],
        center: ['50%', '50%'],
        startAngle: endAngle.value,
        endAngle: 90,
        data: data.value?.filter(item => item.name !== maxItem.value?.name),
        label: {
          show: false,
          position: 'center',
        },
        labelLine: {
          show: false,
        },
      },
    ],
  };
});

watch(
  () => seriesData,
  (newVal) => {
    const sortedData = cloneDeep(newVal);
    sortedData.sort((a, b) => b.value - a.value);
    data.value = sortedData;
  },
  { immediate: true },
);
</script>

<template>
  <div class="flex items-center justify-between">
    <div class="relative size-180">
      <VChart
        class="size-180" :class="[attrs.class]"
        :style="attrs.style"
        :option="option"
        :autoresize="true"
      />
      <div
        class="absolute left-0 top-0 flex size-full items-center justify-center"
      >
        <div
          class="bg flex size-120 items-center justify-center rounded-full border-[3px] border-[#324095]"
        >
          <div class="flex flex-col items-center justify-center gap-4 text-sm">
            <span>{{ title }}</span>
            <span v-if="showTotal" class="font-semibold text-[#deeffa]">
              {{ formatNumber(total) }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="flex flex-col gap-9">
      <div
        v-for="(item, index) in data"
        :key="item.name"
        class="item gap-16"
        :style="{ backgroundImage: `url(${backgroundImageUrls[index]})` }"
      >
        <div class="flex items-center gap-8 overflow-hidden">
          <div
            class="size-8 shrink-0 rounded-sm"
            :style="{ backgroundColor: colors[index] }"
          />
          <span class="w-full truncate">
            {{ item.name }}
          </span>
        </div>
        <div class="flex shrink-0 items-center">
          <span>{{ formatNumber(item.value) }}</span>
          <span class="text-white">｜</span>
          <div class="w-45 font-medium">
            {{ item?.rate || formatDecimal((item.value / total) * 100, 2) }}%
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg {
  background: radial-gradient(50% 50% at 50% 50%, #0931e9 0%, rgba(14, 148, 241, 0) 70%);
  color: rgba(222, 238, 250, 0.65);
}

.item {
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 230px;
  height: 34px;
  color: #deeefa;
  font-size: 14px;
  @apply flex items-center justify-between px-12;
}
</style>
