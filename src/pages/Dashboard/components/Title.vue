<script setup lang="ts">
const { title } = defineProps<{
  title: string;
}>();
</script>

<template>
  <div
    class="flex h-40 items-center justify-center border-l-[7px] border-[#2953F5] bg-[rgba(20,64,163,0.5)] font-bold text-white"
  >
    <span class="title">{{ title }}</span>
  </div>
</template>

<style scoped>
.title {
  font-size: 22px;
  color: #b0c9f5;
  @apply leading-1;
  text-align: left;
  font-style: normal;
  text-transform: none;
  position: relative;
  font-family: '创客贴金刚体', sans-serif;
  -webkit-text-stroke: 0.2px #2953f5;
  letter-spacing: 1px;
}
.title::before {
  content: '';
  position: absolute;
  left: -18px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: #40f3fe;
  border-radius: 50%;
}

.title::after {
  content: '';
  position: absolute;
  right: -17px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: #40f3fe;
  border-radius: 50%;
}
</style>
