<!-- 患者问诊趋势 -->
<script setup lang="ts">
import StackedAreaChart from '@/components/StackedAreaChart/index.vue';
import dayjs from 'dayjs';
import { cloneDeep, isEmpty } from 'lodash-es';
import { computed, ref } from 'vue';
import Comparison from './components/Comparison.vue';
import DashboardCard from './components/DashboardCard.vue';
import NavTabs from './components/NavTabs.vue';

defineOptions({
  name: 'TrendOfConsultation',
});

const { data = [] } = defineProps<{
  data?: {
    name: string;
    value: number;
  }[];
}>();

const currentTab = ref([
  { label: '平台', color: '#0C66EC' },
  { label: '热线', color: '#40F3FE' },
]);

const chartData = computed(() => {
  const chartSeriesData = cloneDeep(data);
  chartSeriesData.sort(
    (a, b) => dayjs(a.name).valueOf() - dayjs(b.name).valueOf(),
  );
  // 只返回最后6个数据
  return chartSeriesData;
});

const xAxisData = computed(() => {
  return chartData.value.map(item => item.name).slice(-6);
});

const seriesData = computed(() => {
  const data = chartData.value.slice(-6);
  return [
    {
      name: '平台',
      data: data.map(item => item.value * 3),
    },
    {
      name: '热线',
      data: data.map(item => item.value * 4),
    },
  ];
});

/** 环比数据 */
const monthOnMonth = computed(() => {
  if (isEmpty(chartData.value) || chartData.value.length < 2) {
    return 0;
  }
  const latestValue = chartData.value.at(-1)?.value || 0;
  const previousValue = chartData.value.at(-2)?.value || 0;
  return ((latestValue - previousValue) / previousValue) * 100;
});

/** 同比数据 */
const yearOnYear = computed(() => {
  if (isEmpty(chartData.value) || chartData.value.length < 13) {
    return 0;
  }
  const latestValue = chartData.value.at(-1)?.value || 0;
  const previousValue = chartData.value.at(-13)?.value || 0;
  return ((latestValue - previousValue) / previousValue) * 100;
});
</script>

<template>
  <DashboardCard title="会员咨询趋势" class="flex-1">
    <template #header>
      <NavTabs :tabs="currentTab" />
    </template>
    <div class="flex h-full flex-col pt-20">
      <Comparison
        :year-on-year="yearOnYear"
        :month-on-month="monthOnMonth"
        class="pb-20 pl-10 pt-10"
      />
      <StackedAreaChart
        title="会员咨询趋势"
        class="flex-1"
        :x-axis-data="xAxisData"
        :series-data="seriesData"
        :smooth="false"
        :gradient="true"
      />
    </div>
  </DashboardCard>
</template>
