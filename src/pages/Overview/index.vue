<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { useLocalStorage } from '@vueuse/core';
import { useRouteQuery } from '@vueuse/router';
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { fetchGeneralCustomerDistributionStatus, fetchGeneralPatientStatus } from '@/api';
import { BarLine<PERSON>hart, CardTitle, DoughnutChart, SourceCard } from '@/components';
import HospitalBoard from '@/pages/Overview/components/HospitalBoard.vue';
import MarketDevelopment from '@/pages/Overview/components/MarketDevelopment.vue';
import OrganizationalStructure from '@/pages/Overview/components/OrganizationalStructure.vue';
import Output from '@/pages/Overview/components/Output.vue';
import SalesAchieved from '@/pages/Overview/components/SalesAchieved.vue';

defineOptions({
  name: 'Overview',
});
const router = useRouter();
const statStartTime = useRouteQuery('statStartTime', '', { transform: Number });
const statEndTime = useRouteQuery('statEndTime', '', { transform: Number });
const province = useRouteQuery<string | undefined>('province', undefined, { transform: val => val || undefined });
const osUserId = useLocalStorage('DATA_PLATFORM_USERID', null, {
  listenToStorageChanges: true,
  serializer: {
    read: v => v ? Number(v) : null,
    write: v => v ? String(v) : '',
  },
});

const { data: customerDistribution } = useQuery({
  queryKey: ['客户占比', statStartTime, statEndTime, province],
  queryFn: () =>
    fetchGeneralCustomerDistributionStatus({
      statStartTime: statStartTime.value,
      statEndTime: statEndTime.value,
      provinceName: province.value,
      osUserId: osUserId.value,
    }),
});

const { data: patientData } = useQuery({
  queryKey: ['fetchGeneralPatientStatus', statStartTime, statEndTime, province],
  queryFn: () =>
    fetchGeneralPatientStatus({
      statStartTime: statStartTime.value,
      statEndTime: statEndTime.value,
      provinceName: province.value,
      osUserId: osUserId.value,
    }),
});

const mainDiagnosisDistribution = computed(() => {
  return (
    patientData.value?.patientDiagnosisGeneralStatDTOS?.map(item => ({
      name: item.diagnosis,
      value: item.patientNums,
    })) ?? []
  );
});
</script>

<template>
  <div class="grid h-full grid-cols-[450px_1fr_450px] gap-16 overflow-y-auto 3xl:grid-cols-7">
    <!-- 市场开发区域 -->
    <div class="flex flex-col gap-16 3xl:col-span-2">
      <MarketDevelopment />
      <SalesAchieved />
    </div>

    <!-- 中间区域 -->
    <div class="flex flex-col gap-16 3xl:col-span-3">
      <HospitalBoard />
      <OrganizationalStructure />
    </div>

    <!-- 右侧区域 -->
    <div class="flex flex-col gap-16 3xl:col-span-2">
      <Output />
      <SourceCard
        title="在管患者主诊断分布"
        action-text="管理分析"
        class="flex-1"
        @action="router.push('/medical-manage')"
      >
        <div class="flex flex-col gap-12">
          <DoughnutChart
            :data="mainDiagnosisDistribution"
            style="height: 100px"
            :inner-label="false"
          />
          <CardTitle title="缴费数/续费率" />
          <BarLineChart
            class="flex-1"
            :x-axis="{
              data: patientData?.renewalDateStatDTOS?.map(item => item.statDate) ?? [],
            }"
            :series="[
              {
                type: 'bar',
                data: patientData?.renewalDateStatDTOS?.map(item => item.renewalNum) ?? [],
                name: '续费数',
              },
              {
                type: 'line',
                data: patientData?.renewalDateStatDTOS?.map(item => item.renewalRate) ?? [],
                name: '续费率',
                tooltip: {
                  valueFormatter(value) {
                    return `${value}%`;
                  },
                },
              },
            ]"
            :grid="{
              bottom: 0,
              left: 20,
            }"
          />
        </div>
      </SourceCard>
      <SourceCard
        title="客户占比"
        action-text="客户分析"
        class="flex-1 shrink-0"
        @action="router.push('/customer')"
      >
        <DoughnutChart
          :data="
            customerDistribution?.transformValueList?.map(item => ({
              name: item.transformValueType,
              value: item.customerNum,
            })) ?? []
          "
          style="height: 140px"
          class="flex-1"
          total-title="客户总量"
        />
      </SourceCard>
    </div>
  </div>
</template>
