<script setup lang="ts">
import { ArrowRightBold } from '@element-plus/icons-vue';
import { useQuery } from '@tanstack/vue-query';
import { useSessionStorage } from '@vueuse/core';
import { useRouteQuery } from '@vueuse/router';
import dayjs from 'dayjs';

import { ElIcon } from 'element-plus';
import { useRouter } from 'vue-router';
import { fetchGeneralHospitalStatus } from '@/api';

import { ChinaMap, SourceCard, Times } from '@/components';
import DescriptionsItem from '@/pages/Overview/components/DescriptionsItem.vue';

defineOptions({
  name: 'Overview',
});

const router = useRouter();
const statStartTime = useRouteQuery('statStartTime', '', { transform: Number });
const statEndTime = useRouteQuery('statEndTime', '', { transform: Number });
const province = useRouteQuery<string | undefined>('province', undefined, { transform: val => val || undefined });
const provinces = useSessionStorage<string[]>('hospital-board-provinces', []);
const { data } = useQuery({
  queryKey: ['generalHospitalStatus', statStartTime, statEndTime, province],
  queryFn: () =>
    fetchGeneralHospitalStatus({
      statStartTime: statStartTime.value,
      statEndTime: statEndTime.value,
      provinceName: province.value,
    }),
});

function navToHospital() {
  router.push('/hospital');
}

function dateChange([start, end]: [string, string]) {
  statStartTime.value = dayjs(start).startOf('day').valueOf();
  statEndTime.value = dayjs(end).endOf('day').valueOf();
}

function handleProvinceSelected(name: string) {
  province.value = name;
}

watch(data, (val) => {
  if (!province.value && val?.provinceList) {
    provinces.value = val.provinceList;
  }
}, { immediate: true });
</script>

<template>
  <SourceCard class="p-0">
    <Times @date-change="dateChange" />
    <div class="-mx-16 my-0 h-1 bg-[#E1E5ED]" />
    <div class="flex items-center gap-14">
      <h2 v-if="!province" class="hidden text-[28px] font-semibold text-[#3A4762] 3xl:block">
        全国
      </h2>
      <div class="flex-1 rounded border border-[#2E6BE6] bg-[#E6EEFF] px-8 py-12 leading-1">
        <div class="flex items-center gap-8" :class="{ 'mb-16': !province }">
          <div class="text-sm font-medium text-[#15233F]">
            业务覆盖
          </div>
          <div
            class="grid flex-1 items-center gap-y-10 text-nowrap text-xs leading-1 text-[#7A8599]"
            :class="{
              'grid-cols-4': !province,
              'grid-cols-3': province,
            }"
          >
            <DescriptionsItem v-if="!province" label="省份" :divider="false">
              {{ data?.provinceSize ?? 0 }}
            </DescriptionsItem>
            <DescriptionsItem label="地区" :divider="!province">
              {{ data?.regionSize ?? 0 }}
            </DescriptionsItem>
            <DescriptionsItem label="合作医院">
              {{ data?.hospitalSize ?? 0 }}
            </DescriptionsItem>
            <DescriptionsItem label="医生工作室">
              {{ data?.groupSize ?? 0 }}
            </DescriptionsItem>
          </div>
        </div>
        <div v-if="!province" class="flex items-center gap-8">
          <div class="text-sm font-medium text-[#15233F]">
            患者分布
          </div>
          <div
            class="grid flex-1 grid-cols-4 items-center gap-y-10 text-nowrap text-xs leading-1 text-[#7A8599]"
          >
            <DescriptionsItem label="全部在管" :divider="false">
              {{ data?.patientSize ?? 0 }}
            </DescriptionsItem>
            <DescriptionsItem label="付费会员">
              {{ data?.patientPaidSize ?? 0 }}
            </DescriptionsItem>
            <DescriptionsItem label="科研患者">
              {{ data?.patientScientificSize ?? 0 }}
            </DescriptionsItem>
            <DescriptionsItem label="历史患者">
              {{ data?.patientHistorySize ?? 0 }}
            </DescriptionsItem>
          </div>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-between">
      <h2 v-if="!province" class="block text-[28px] font-semibold text-[#3A4762] 3xl:hidden">
        全国
      </h2>
      <div v-if="province" class="flex items-center gap-12">
        <h2 class="text-[28px] font-semibold text-[#3A4762]">
          {{ province }}
        </h2>
        <div class="flex h-32 w-76 cursor-pointer items-center justify-center rounded-sm border border-[#dcdfe6] text-sm text-[#606266]" @click="province = undefined">
          返回
        </div>
      </div>
      <div class="ml-auto cursor-pointer text-right text-sm text-[#2E6BE6]" @click="navToHospital">
        医院看板
        <ElIcon size="9">
          <ArrowRightBold />
        </ElIcon>
      </div>
    </div>
    <ChinaMap
      :province="province"
      :provinces="provinces"
      :drill-down="true"
      @province-selected="handleProvinceSelected"
    />
  </SourceCard>
</template>

<style scoped lang="less"></style>
