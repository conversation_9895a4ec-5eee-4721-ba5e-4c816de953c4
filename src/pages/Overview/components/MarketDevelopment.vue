<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { useRouteQuery } from '@vueuse/router';
import { useRouter } from 'vue-router';
import { fetchGeneralMarketStatus } from '@/api';
import { SourceCard } from '@/components';
import { moneyFormat } from '@/lib';
import BusinessCard from '@/pages/Overview/components/BusinessCard.vue';
import Funnel from '@/pages/Overview/components/Funnel.vue';

const router = useRouter();
const statStartTime = useRouteQuery('statStartTime', '', { transform: Number });
const statEndTime = useRouteQuery('statEndTime', '', { transform: Number });
const province = useRouteQuery('province', undefined, { transform: val => val || undefined });

const { data } = useQuery({
  queryKey: ['市场开发generalMarketStatus', statStartTime, statEndTime, province],
  queryFn: () =>
    fetchGeneralMarketStatus({
      statStartTime: statStartTime.value,
      statEndTime: statEndTime.value,
      provinceName: province.value,
    }),
});

function handleMarketAnalysis() {
  router.push('/market');
}
</script>

<template>
  <SourceCard
    title="市场开发"
    sub-title="手术量单位：台手术/年"
    action-text="市场分析"
    @action="handleMarketAnalysis"
  >
    <Funnel
      class="3xl:mb-16"
      :data="[
        {
          name: 'PCI手术总量',
          value: data?.pciSurgeryTotalNum ?? 0,
        },
        {
          name: '目标PCI手术量',
          value: data?.pciSurgeryTargetNum ?? 0,
          outerLabel: '目标市场占有率',
          outerValue: data?.targetMarketRate ?? 0,
        },
        {
          name: '覆盖PCI手术量',
          value: data?.pciSurgeryCoverNum ?? 0,
          outerLabel: '覆盖率',
          outerValue: data?.coverRate ?? 0,
        },
        {
          name: '新购订单',
          value: data?.newPurchaseOrderNum ?? 0,
          outerLabel: '达成率',
          outerValue: data?.achievedRate ?? 0,
        },
      ]"
    />
    <div class="flex gap-16 overflow-x-scroll">
      <BusinessCard
        v-for="area in data?.marketGeneralStatList || []"
        :key="area.departmentName"
        :title="area.departmentName"
        class="shrink-0"
        :target="{
          target: area.marketMetricsNum,
          done: area.achievedNum,
          name: '市场指标',
        }"
        :yoy="area.yoySurgeryPercent"
        :mom="area.momSurgeryPercent"
        :style="{ width: 'calc(50% - 8px)' }"
      >
        <template #footer>
          <div class="mt-8 flex justify-between rounded bg-[#E6EEFF] py-8 pl-10 pr-4 text-xs font-medium text-[#3A4762]">
            <div class="font-normal text-[#7A8599]">
              资金投入(元)
            </div>
            <div>{{ moneyFormat((area.investCopyAmount || 0) + area.investFeeReimburse) }}</div>
          </div>
        </template>
      </BusinessCard>
    </div>
  </SourceCard>
</template>
