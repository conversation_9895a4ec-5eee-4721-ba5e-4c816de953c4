<script setup lang="ts">
import type { EChartsOption } from 'echarts';
import { FunnelChart } from 'echarts/charts';
import { GridComponent, LegendComponent } from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { computed } from 'vue';
import VChart from 'vue-echarts';
import { formatDecimal } from '@/lib';

export interface FunnelProps {
  data?: {
    value: number;
    name: string;
    outerLabel?: string;
    outerValue?: number;
  }[];
}

const { data = [] } = defineProps<FunnelProps>();

use([CanvasRenderer, LegendComponent, GridComponent, FunnelChart]);

const chartsData = computed(() => {
  const result = data.map((item) => {
    return {
      ...item,
      name: `${item.name}：${item.value}`,
      outerLabel: item.outerLabel ? `${item.outerLabel}（${formatDecimal(item.outerValue || 0, 2)}%）` : '',
    };
  });
  return result;
});

const options = computed(() => {
  return {
    color: ['#113B8F', '#1E52BA', '#2E6BE6', '#5285EB'],
    grid: {
      left: 0,
    },
    series: [
      {
        name: 'Funnel',
        type: 'funnel',
        left: 0,
        top: 0,
        bottom: 0,
        width: '70%',
        min: 0,
        max: 100,
        minSize: '120px',
        maxSize: '200px',
        sort: 'none',
        gap: 0,
        label: {
          show: true,
          color: '#3A4762',
          fontSize: '12px',
        },
        labelLine: {
          length: 50,
          lineStyle: {
            width: 1,
            type: 'dashed',
            color: '#4C80E9',
            dashOffset: 4,
          },
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 0,
        },
        data: chartsData.value.map((item, index) => ({
          value: item.outerValue ?? 0,
          name: item.outerLabel,
          label: {
            show: index !== 0,
          },
        })),
      },
      {
        name: 'Funnel',
        type: 'funnel',
        left: 0,
        top: 0,
        bottom: 0,
        width: '70%',
        min: 0,
        max: 100,
        minSize: '120px',
        maxSize: '200px',
        sort: 'none',
        gap: 0,
        label: {
          show: true,
          position: 'inside',
          color: 'white',
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 0,
        },
        z: 100,
        data: chartsData.value.map(item => ({
          value: item.value,
          name: item.name,
        })),
      },
    ],
  } as EChartsOption;
});
</script>

<template>
  <VChart :option="options" class="h-132" autoresize />
</template>
