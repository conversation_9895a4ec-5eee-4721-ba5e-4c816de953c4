<script setup lang="ts">
import { ElProgress } from 'element-plus';
import { computed } from 'vue';
import { Comparison } from '@/components';
import { formatDecimal } from '@/lib';

export interface BusinessCardProps {
  title: string;
  /** 同比 */
  yoy: number;
  /** 环比 */
  mom: number;
  /** 指标 */
  target: {
    /** 目标值 */
    target: number;
    /** 完成的值 */
    done: number;
    /** 名称 */
    name: string;
  };
}

const { title, yoy, mom, target } = defineProps<BusinessCardProps>();

const staticList = computed(() => {
  return [
    {
      label: target.name,
      value: target.target,
    },
    {
      label: '完成量',
      value: target.done,
    },
    {
      label: '完成率',
      value: `${formatDecimal(((target.done / (target.target || 1)) || 0) * 100, 1)}%`,
    },
  ];
});

const percentage = computed(() => {
  const val = ((target.done / (target.target || 1)) || 0) * 100;
  return val > 100 ? 100 : val;
});
</script>

<template>
  <div class="rounded-md bg-[#F7F8FA] px-16 py-10">
    <!-- 标题 -->
    <div class="mb-8 text-sm font-medium text-[#15233F]">
      {{ title }}
    </div>

    <!-- 同比环比 -->
    <Comparison :yoy="yoy" :mom="mom" class="mb-10 gap-16 text-[#7A8599]" :fraction-digits="1" />
    <slot name="content" />

    <!-- 饼图和指标 -->
    <div class="space-y-8">
      <p class="text-xs font-medium text-[#3A4762]">
        完成进度
      </p>
      <ElProgress
        :percentage="percentage"
        :stroke-width="8"
        class="mb-12 mt-8"
      >
        <div class="text-xs text-[#3A4762]">
          {{ target.done }}（{{ staticList[2].value }}）
        </div>
      </ElProgress>
      <!-- 右侧指标 -->
      <div class="flex flex-1 flex-col justify-center gap-4 overflow-hidden text-[12px] leading-4 3xl:gap-12 ">
        <template v-for="item in staticList" :key="item.label">
          <div class="flex w-full items-center gap-8 overflow-hidden">
            <span class="w-50 shrink-0 text-[#7A8599]">{{ item.label }}</span>
            <span class="flex-1 border-b border-dashed border-[#9FBCF5]" />
            <span class="inline-block min-w-25 truncate text-end font-medium text-[#3A4762]">{{ item.value }}</span>
          </div>
        </template>
      </div>
    </div>
    <slot name="footer" />
  </div>
</template>
