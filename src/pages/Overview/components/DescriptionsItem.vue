<script setup lang="ts">
export interface DescriptionsItemProps {
  label: string;
  divider?: boolean;
}

const { label, divider = true } = defineProps<DescriptionsItemProps>();
</script>

<template>
  <div
    class="inline-flex items-center gap-8 pl-16 leading-1"
    :class="{
      'border-l border-[#9FBCF5]': divider,
    }"
  >
    <span class="text-xs">{{ label }}</span>
    <span class="-mt-2 text-base  font-bold text-[#15233F]">
      <slot />
    </span>
  </div>
</template>
