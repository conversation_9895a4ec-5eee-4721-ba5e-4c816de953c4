<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { useRouteQuery } from '@vueuse/router';
import { computed } from 'vue';
import { fetchGeneralOrderStatus } from '@/api';
import { <PERSON>Title, <PERSON>hnut<PERSON>hart, SourceCard } from '@/components';
import { formatDecimal } from '@/lib';

const statStartTime = useRouteQuery('statStartTime', '', { transform: Number });
const statEndTime = useRouteQuery('statEndTime', '', { transform: Number });
const province = useRouteQuery('province', undefined, { transform: val => val || undefined });

const { data } = useQuery({
  queryKey: ['fetchGeneralOrderStatus', statStartTime, statEndTime, province],
  queryFn: () =>
    fetchGeneralOrderStatus({
      statStartTime: statStartTime.value,
      statEndTime: statEndTime.value,
      provinceName: province.value,
    }),
});

const chartData = computed(() => {
  if (!data.value) {
    return [];
  }
  return [
    {
      name: '人力成本',
      value: data.value.laborCostsAmount,
    },
    {
      name: '专家投入',
      value: data.value.expertInvestAmount,
    },
    {
      name: '专家劳务费',
      value: data.value.expertServiceAmount,
    },
    {
      name: '报销',
      value: data.value.reimburseAmount,
    },
  ];
});

const indicators = computed(() => {
  if (!data.value) {
    return [];
  }
  const total = chartData.value.reduce((prev, curr) => prev + curr.value, 0);
  return [
    { label: '合计产出', value: data.value?.effectiveOrderAmount },
    { label: '新购订单', value: data.value?.newPurchaseOrderCount },
    { label: '续费订单', value: data.value?.renewalOrderCount },
    {
      label: '投产比',
      value: formatDecimal((data.value.effectiveOrderAmount || 0) / total * 100, 2),
    },
  ];
});
</script>

<template>
  <SourceCard title="产出情况" sub-title="单位：元" class="flex-1">
    <div class="flex flex-1 flex-col gap-12">
      <div class="grid grid-cols-4 rounded-md bg-[#F7F8FA] py-4">
        <div v-for="item in indicators" :key="item.label" class="flex flex-col items-center gap-4">
          <p class="text-xs text-[#7A8599]">
            {{ item.label }}
          </p>
          <p class="text-sm text-[#3A4762]">
            {{ item.value }}
          </p>
        </div>
      </div>
      <CardTitle title="资金投入" sub-title="单位：元" />
      <DoughnutChart :data="chartData" total-title="总投入" class="flex-1" />
    </div>
  </SourceCard>
</template>
