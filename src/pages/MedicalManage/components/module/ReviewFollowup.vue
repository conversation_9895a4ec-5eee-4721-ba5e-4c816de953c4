<template>
  <div class="flex flex-col gap-16">
    <SubCard width="100%" padding="0" pure>
      <div class="h-320">
        <slot name="review">
          <StackBarChart
            title="复查完成率"
            :color="['#5285EB', '#9FBCF5']"
            :zoom-size="size === 'mini' ? 5 : 12"
            :data-zoom-config="{
              minValueSpan: 5,
              endValue: size === 'mini' ? 5 : 11,
            }"
            :grid="{ bottom: 80 }"
            :size="size"
            :x-data="reviewXData"
            :data="reviewData"
          />
        </slot>
      </div>
    </SubCard>
    <div class="h-9 w-full bg-[#e1e5ed]"></div>
    <SubCard width="100%" padding="0" pure>
      <div class="h-320">
        <slot name="followup">
          <StackBarChart
            title="随访完成率"
            :color="['#5285EB', '#9FBCF5']"
            :zoom-size="size === 'mini' ? 5 : 12"
            :data-zoom-config="{
              minValueSpan: 5,
              endValue: size === 'mini' ? 5 : 11,
            }"
            :grid="{ bottom: 80 }"
            :size="size"
            :x-data="followupXData"
            :data="followupData"
          />
        </slot>
      </div>
    </SubCard>
  </div>
</template>

<script setup lang="ts">
import StackBarChart from '@/components/StackBarChart/index.vue';
import SubCard from '@/components/SubCard/index.vue';

interface IProps {
  size?: 'mini' | 'default';
  reviewData?: any[];
  reviewXData?: any[];
  followupData?: any[];
  followupXData?: any[];
}
defineProps<IProps>();
defineOptions({
  name: 'ReviewFollowup',
});
</script>

<style scoped lang="less">
// todo
</style>
