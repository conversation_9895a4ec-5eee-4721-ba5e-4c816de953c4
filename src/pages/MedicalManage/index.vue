<template>
  <div class="space-y-16 rounded-md bg-white p-24">
    <CardTitle
      title="全部患者分布"
      action="更多"
      class="text-lg"
      @click="allPatientVisible = true"
    />
    <div class="h-1 w-full bg-[#E1E5ED]"></div>
    <div class="flex">
      <Card item-height="150px" title="全部患者" block>{{
        allData?.totalNum
      }}</Card>
      <Card
        item-height="150px"
        item-width="400px"
        style="min-width: 298px"
        title="付费患者"
        block
      >
        <div class="w-full">
          <div class="h-40">{{ allData?.payNum }}</div>
          <div class="bottom">
            <div class="item">
              <span>历史</span>
              <span
                >{{ allData?.historyPayNum }} ({{
                  allData?.historyPayPercent
                }}%)</span
              >
            </div>
            <div class="item">
              <span>在管</span>
              <span
                >{{ allData?.currentPayNum }} ({{
                  allData?.currentPayPercent
                }}%)</span
              >
            </div>
          </div>
        </div>
      </Card>
      <Card
        item-height="150px"
        item-width="400px"
        style="min-width: 298px"
        title="科研病例"
        block
      >
        <div class="w-full">
          <div class="h-40">{{ allData?.scientificNum }}</div>
          <div class="bottom">
            <div class="item">
              <span>历史</span>
              <span
                >{{ allData?.historyScientificNum }} ({{
                  allData?.historyScientificPercent
                }}%)</span
              >
            </div>
            <div class="item">
              <span>在管</span>
              <span
                >{{ allData?.currentScientificNum }} ({{
                  allData?.currentScientificPercent
                }}%)</span
              >
            </div>
          </div>
        </div>
      </Card>
      <div class="right">
        <PieChart
          title="主诊断分布"
          :bold="false"
          mt="4px"
          :chart-width="100"
          :data="
            transformPieData(allData?.diagnosisStatistic ?? [], {
              name: 'diagnosis',
              value: 'patientNums',
              percent: 'rate',
            })
          "
        />
      </div>
    </div>
    <div class="pt-34">
      <div class="toolbar">
        <Times
          :enable-keys="['duration-year', 'custom-year']"
          @date-change="dateChangeHandler"
        />
        <div class="flex-1">
          <el-date-picker
            v-model="drillingStore.times"
            type="daterange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            :disabled-date="disableDateHandler"
            @change="orderTimeChangeHandler"
          />
        </div>
      </div>
    </div>
    <CardTitle
      title="复查/随访"
      class="text-lg"
      action="更多"
      @click="rfVisible = true"
    />
    <div class="h-1 w-full bg-[#E1E5ED]"></div>
    <ReviewFollowup
      :review-x-data="reviewAndFollowData.follow.xData"
      :followup-x-data="reviewAndFollowData.review.xData"
      :followup-data="reviewAndFollowData.follow.data"
      :review-data="reviewAndFollowData.review.data"
    />
    <CardTitle
      title="指标达标率"
      tips="指标达标率: 达标指标数/测量指标数"
      class="text-lg"
      action="更多"
      @click="indicatorVisible = true"
    />

    <StackBarChart
      style="height: 320px"
      :label-no-wrap="true"
      :grid="{ left: 60 }"
      :color="['#5285EB', '#9FBCF5']"
      :x-data="indicatorData.xData"
      :data="indicatorData.data"
    >
      <template #operation>
        <RadioGroup
          :value="indicatorParams"
          :data="INDICATORS"
          style="margin-top: 0"
          @on-change="indicatorChangeHandler"
        />
      </template>
    </StackBarChart>
    <CardTitle
      title="会员退费分析"
      class="text-lg"
      action="更多"
      @click="refundVisible = true"
    />
    <div class="flex">
      <SubCard width="50%" style="height: 280px" padding="16px 16px 0" pure>
        <LineChart
          color="#2FB324"
          :height="280"
          :y-axis="{
            type: 'value',
            min: 0,
            axisLabel: {
              formatter: '{value}',
            },
          }"
          :x-data="refundData?.xData ?? []"
          :data="refundData?.data"
          :grid="{ bottom: 70 }"
        />
      </SubCard>
      <SubCard width="50%" style="height: 300px" padding="16px 16px 0" pure>
        <NewPieChart
          title="退费原因"
          bold
          :data="
            transformPieData(
              manageStore.refund.refundReasonDetail?.all?.refundReasonList,
              {
                name: 'reasonEnum',
                value: 'reasonNum',
                percent: 'reasonPercent',
              }
            )
          "
        />
      </SubCard>
    </div>
  </div>
  <AllPatientAnalysis
    v-model:visible="allPatientVisible"
    title="全部患者分布"
  />
  <RFAnalysis v-model:visible="rfVisible" title="患者依从性" />
  <IndicatorAnalysis v-model:visible="indicatorVisible" title="指标控制情况" />
  <RefundAnalysis v-model:visible="refundVisible" title="用户退费分析" />
</template>

<script setup lang="ts">
import { CardTitle } from '@/components';
import Card from '@/components/Card/index.vue';
import LineChart from '@/components/LineChart/index.vue';
import NewPieChart from '@/components/NewPieChart/index.vue';
import PieChart from '@/components/PieChart/index.vue';
import RadioGroup from '@/components/RadioGroup/index.vue';
import StackBarChart from '@/components/StackBarChart/index.vue';
import SubCard from '@/components/SubCard/index.vue';
import Times from '@/components/Times/index.vue';
import { INDICATORS, INDICATORS_MAP } from '@/constant';
import useMedicalManage from '@/store/useMedicalManage';
import useMedicalManageDrilling from '@/store/useMedicalManageDrilling';
import { transformPieData } from '@/utils';
import {
  getDoctorIndicatorSeriesData,
  getDoctorRFSeriesData,
} from '@/utils/transformData';
import dayjs from 'dayjs';
import AllPatientAnalysis from './components/drilling/AllPatientAnalysis.vue';
import IndicatorAnalysis from './components/drilling/IndicatorAnalysis.vue';
import RefundAnalysis from './components/drilling/RefundAnalysis.vue';
import RFAnalysis from './components/drilling/RFAnalysis.vue';
import ReviewFollowup from './components/module/ReviewFollowup.vue';

const manageStore = useMedicalManage();
const drillingStore = useMedicalManageDrilling();
const allPatientVisible = ref(false);
const indicatorVisible = ref(false);
const refundVisible = ref(false);
const rfVisible = ref(false);
const indicatorParams = ref('ALL');
const allData = computed(() => manageStore.allData.distribution);
const reviewAndFollowData = computed(() => {
  const item = manageStore.reviewAndFollow;
  const { reviewData, followData } = getDoctorRFSeriesData(item, [
    'review',
    'followUp',
  ]);
  return { follow: followData, review: reviewData };
});
const otherParams = computed(() => {
  const curIndicatorName = INDICATORS_MAP[indicatorParams.value].name;
  return { suf: `(${curIndicatorName})` };
});
const indicatorData = computed(() => {
  const item = manageStore.indicator.index;
  return getDoctorIndicatorSeriesData({
    list: item,
    otherParams: otherParams.value,
  });
});

const refundData = computed(() => {
  const item = manageStore.refund?.refundNumDetail?.all;
  const xData = item?.map(v => v.key!);
  const data = [
    {
      name: '退费订单',
      type: 'line',
      symbol: 'none',
      data: item
        ?.map(v => v.dataList?.[0].refundNum ?? 0)
        .map(v => ({
          name: '退费订单',
          value: v,
        })),
    },
  ];
  return { xData, data };
});
const disableDateHandler = val => {
  return val > dayjs().endOf('d').valueOf();
};
const orderTimeChangeHandler = val => {
  drillingStore.times = val;
  manageStore.times = val;
  manageStore.pageInit({ indicatorType: indicatorParams.value });
};
const indicatorChangeHandler = val => {
  indicatorParams.value = val;
  drillingStore.indexType = val;
  manageStore.getIndicatorData({ indexType: val });
};
const dateChangeHandler = val => {
  manageStore.manageYears = val;
  drillingStore.manageYear = val;
  drillingStore.indexType = indicatorParams.value;
  manageStore.pageInit({ indicatorType: indicatorParams.value });
};

onMounted(() => {
  manageStore.getAllData();
});

defineOptions({
  name: 'MedicalManage',
});
</script>

<style scoped lang="less">
.toolbar {
  width: 100%;
  height: 32px;
  display: flex;
  justify-content: space-between;
}
.right {
  height: 150px;
  padding: 16px;
  width: unset;
  background: #f7f8fa;
  border-radius: 4px;
  display: flex;
  flex: 1;
}

.bottom {
  font-size: 14px;
  display: flex;
  margin-top: 36px;
  justify-content: space-between;
  .item {
    width: 40%;
    display: flex;
    justify-content: space-between;
    > span:first-child {
      color: #3a4762;
      font-weight: normal;
    }
  }
}
</style>
