import { http } from '@/network';
import {
  IApiDpSellerPlanSumStatParams,
  IApiDpSellerPlanSumStat,
  IApiDpSellerOrderRenewAndRefundSumStatParams,
  IApiDpSellerOrderRenewAndRefundSumStat,
  IApiDpSellerOrderRefundAnalysisStatParams,
  IApiDpSellerOrderRefundAnalysisStat,
  IApiDpSellerGroupSumStatParams,
  IApiDpSellerGroupSumStat,
  IApiDpSellerGroupOrderStatParams,
  IApiDpSellerGroupOrderStat,
  IApiDpSellerOrderNpTrendStatParams,
  IApiDpSellerOrderNpTrendStat,
  IApiDpSellerOrderNpAchieveStatParams,
  IApiDpSellerOrderNpAchieveStat,
  IApiDpSellerLedgerNoDealAgeStatParams,
  IApiDpSellerLedgerNoDealAgeStat,
  IApiDpSellerLedgerNoDealStatParams,
  IApiDpSellerLedgerNoDealStat,
  IApiDpSellerLedgerDealAgeStatParams,
  IApiDpSellerLedgerDealAgeStat,
  IApiDpSellerLedgerDealStatParams,
  IApiDpSellerLedgerDealStat,
} from '@/interface/type';

/** 工作室新购数据统计 */
export const getSellerGroupData = (params: IApiDpSellerGroupSumStatParams) => {
  return http.post<IApiDpSellerGroupSumStat>({
    url: '/api/dp/seller/group/sum/stat',
    method: 'post',
    data: params,
    customConfig: { ignoreRequestParams: true },
  });
};
/** 工作室订单分析 */
export const getSellerGroupAnalysis = (
  params: IApiDpSellerGroupOrderStatParams
) => {
  return http.post<IApiDpSellerGroupOrderStat>({
    url: '/api/dp/seller/group/order/stat',
    method: 'post',
    data: params,
  });
};
/** 新购订单走势 */
export const getSellerOrderTrendOuter = (
  params: IApiDpSellerOrderNpTrendStatParams,
  ignoreRequestParams = false
) => {
  return http.post<IApiDpSellerOrderNpTrendStat>({
    url: '/api/dp/seller/order/show/np/trend/stat',
    method: 'post',
    data: params,
    customConfig: { ignoreRequestParams },
  });
};
/** 新购订单走势详情 */
export const getSellerOrderTrend = (
  params: IApiDpSellerOrderNpTrendStatParams,
  ignoreRequestParams = false
) => {
  return http.post<IApiDpSellerOrderNpTrendStat>({
    url: '/api/dp/seller/order/np/trend/stat',
    method: 'post',
    data: params,
    customConfig: { ignoreRequestParams },
  });
};
/** 新购订单达成转化、省份部门占比 */
export const getSellerAchieve = (
  params: IApiDpSellerOrderNpAchieveStatParams
) => {
  return http.post<IApiDpSellerOrderNpAchieveStat>({
    url: '/api/dp/seller/order/np/achieve/stat',
    method: 'post',
    data: params,
  });
};
/** 新购成交分析-转化效果分析 */
export const getSellerDeal = (
  params: IApiDpSellerLedgerDealStatParams,
  ignoreRequestParams = false
) => {
  return http.post<IApiDpSellerLedgerDealStat>({
    url: '/api/dp/seller/ledger/deal/stat',
    method: 'post',
    data: params,
    customConfig: { ignoreRequestParams },
  });
};
/** 新购成交分析-成交年龄分析 */
export const getSellerDealAge = (
  params: IApiDpSellerLedgerDealAgeStatParams
) => {
  return http.post<IApiDpSellerLedgerDealAgeStat>({
    url: '/api/dp/seller/ledger/deal/age/stat',
    method: 'post',
    data: params,
  });
};
/** 未成交分析 */
export const getSellerNoDeal = (
  params: IApiDpSellerLedgerNoDealStatParams,
  ignoreRequestParams = false
) => {
  return http.post<IApiDpSellerLedgerNoDealStat>({
    url: '/api/dp/seller/ledger/no/deal/stat',
    method: 'post',
    data: params,
    customConfig: { ignoreRequestParams },
  });
};
/** 未成交分析-未成交年龄分析 */
export const getSellerNoDealAge = (
  params: IApiDpSellerLedgerNoDealAgeStatParams
) => {
  return http.post<IApiDpSellerLedgerNoDealAgeStat>({
    url: '/api/dp/seller/ledger/no/deal/age/stat',
    method: 'post',
    data: params,
  });
};
/** 用戶退費分析--汇总统计 */
export const getSellerRefundAnylysis = (
  params: IApiDpSellerOrderRefundAnalysisStatParams
) => {
  return http.post<IApiDpSellerOrderRefundAnalysisStat>({
    url: '/api/dp/seller/order/refund/analysis/stat',
    method: 'post',
    data: params,
  });
};

/** 续费/退费--汇总统计 */
export const getSelleRenewAndRefund = (
  params: IApiDpSellerOrderRenewAndRefundSumStatParams
) => {
  return http.post<IApiDpSellerOrderRenewAndRefundSumStat>({
    url: '/api/dp/seller/order/renew/and/refund/sum/stat',
    method: 'post',
    data: params,
    customConfig: { ignoreRequestParams: true },
  });
};

/** 团队每日工作计划 */
export const getSellerWorkPlan = (
  params: IApiDpSellerPlanSumStatParams,
  ignoreRequestParams = false
) => {
  return http.post<IApiDpSellerPlanSumStat>({
    url: '/api/dp/seller/plan/sum/stat',
    method: 'post',
    data: params,
    customConfig: { ignoreRequestParams },
  });
};
