import type {
  EmployeePerfStatQueryParams,
} from '@/schema';
import { http } from '@/network';
import {
  EmployeeInvestDetailResSchema,
  EmployeeInvestmentDataSchema,
  EmployeeInvestTrendSchema,
  EmployeeOutputDetailResSchema,
  EmployeeOutputTrendSchema,
} from '@/schema';

/**
 * 条件查询员工投产数据
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.startTime 开始时间（可选）
 * @param params.endTime 结束时间（可选）
 * @returns 员工投产数据
 */
export async function fetchEmployeeInvestmentData(params: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/inv/output/query/info',
    data: params,
    schema: EmployeeInvestmentDataSchema,
  });
}

/**
 * 条件查询员工产出趋势
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.startTime 开始时间（可选）
 * @param params.endTime 结束时间（可选）
 */
export async function fetchEmployeeOutputTrend(params: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/inv/output/query/output/trend',
    data: params,
    schema: EmployeeOutputTrendSchema,
  });
}

/**
 * 条件查询员工投入趋势
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.startTime 开始时间（可选）
 * @param params.endTime 结束时间（可选）
 * @returns 员工投入趋势
 */
export async function fetchEmployeeInvestTrend(params: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/inv/output/query/inv/trend',
    data: params,
    schema: EmployeeInvestTrendSchema,
  });
}

/**
 * 条件分页查询员工产出明细
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.pageNumber 页码（可选）
 * @param params.pageSize 每页条数（可选）
 * @param params.startTime 开始时间（可选）
 * @param params.endTime 结束时间（可选）
 * @returns 员工产出明细列表
 */
export async function fetchEmployeeOutputDetail(params: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/inv/output/page/query/output/detail',
    data: params,
    schema: EmployeeOutputDetailResSchema,
  });
}

/**
 * 条件分页查询员工投入明细
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.pageNumber 页码（可选）
 * @param params.pageSize 每页条数（可选）
 * @param params.startTime 开始时间（可选）
 * @param params.endTime 结束时间（可选）
 * @returns 员工投入明细列表
 */
export async function fetchEmployeeInvestDetail(params: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/inv/output/page/query/inv/detail',
    data: params,
    schema: EmployeeInvestDetailResSchema,
  });
}
