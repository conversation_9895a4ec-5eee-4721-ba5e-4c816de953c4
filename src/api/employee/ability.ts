import type { EmployeePerfStatQueryParams } from '@/schema';
import { http } from '@/network';
import {
  BusinessAbilityDetailDataSchema,
  BusinessAbilityTrendDataSchema,
  DevelopmentPotentialDetailDataSchema,
  DevelopmentPotentialTrendDataSchema,
  EmployeeAbilitySchema,
  EmployeeAbilityTrendSchema,
  PerformanceEvaluationDetailDataSchema,
  PerformanceEvaluationTrendDataSchema,
  ProfessionalAbilityDetailDataSchema,
  ProfessionalAbilityTrendDataSchema,
  SubjectiveInitiativeDetailDataSchema,
  SubjectiveInitiativeTrendDataSchema,
} from '@/schema/employee/ability';

/**
 * 获取员工当前能力
 * @param data 员工信息
 * @returns 员工当前能力
 */
export async function fetchEmployeeAbility(data: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/ability/query/current',
    data,
    schema: EmployeeAbilitySchema,
  });
}

/**
 * 获取员工能力得分趋势
 * @param data 员工信息
 * @returns 员工能力得分趋势
 */
export async function fetchEmployeeAbilityTrend(data: EmployeePerfStatQueryParams) {
  return await http.post({
    url: '/api/dp/emp/ability/query/trend',
    data,
    schema: EmployeeAbilityTrendSchema,
  });
}

// 业务达成能力相关接口

/**
 * 获取员工业务达成趋势
 * @param data 员工信息
 * @returns 员工业务达成趋势
 */
export async function fetchEmployeeBusinessAbilityTrend(data: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/ability/query/biz/achieve/trend',
    data,
    schema: BusinessAbilityTrendDataSchema,
  });
}

/**
 * 获取员工业务达成明细
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.pageNumber 页码（可选）
 * @param params.pageSize 每页条数（可选）
 * @returns 员工业务达成明细
 */
export async function fetchEmployeeBusinessAbilityDetail(params: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/ability/page/query/biz/achieve/detail',
    data: params,
    schema: BusinessAbilityDetailDataSchema,
  });
}

// 专业能力相关接口

/**
 * 获取员工专业能力趋势
 * @param data 员工信息
 * @returns 员工专业能力趋势
 */
export async function fetchEmployeeProfessionalAbilityTrend(data: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/ability/query/prof/ability/trend',
    data,
    schema: ProfessionalAbilityTrendDataSchema,
  });
}

/**
 * 获取员工专业能力明细
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.pageNumber 页码（可选）
 * @param params.pageSize 每页条数（可选）
 * @returns 员工专业能力明细
 */
export async function fetchEmployeeProfessionalAbilityDetail(params: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/ability/page/query/prof/ability/detail',
    data: params,
    schema: ProfessionalAbilityDetailDataSchema,
  });
}

// 主观能动性相关接口

/**
 * 获取员工主观能动性趋势
 * @param data 员工ID
 * @returns 员工主观能动性趋势
 */
export async function fetchEmployeeSubjectiveInitiativeTrend(data: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/ability/query/subj/trend',
    data,
    schema: SubjectiveInitiativeTrendDataSchema,
  });
}

/**
 * 获取员工主观能动性明细
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.pageNumber 页码（可选）
 * @param params.pageSize 每页条数（可选）
 * @returns 员工主观能动性明细
 */
export async function fetchEmployeeSubjectiveInitiativeDetail(params: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/ability/page/query/subj/detail',
    data: params,
    schema: SubjectiveInitiativeDetailDataSchema,
  });
}

// 绩效考核相关接口

/**
 * 获取员工绩效考核趋势
 * @param data 员工信息
 * @returns 员工绩效考核趋势
 */
export async function fetchEmployeePerformanceEvaluationTrend(data: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/ability/query/perf/trend',
    data,
    schema: PerformanceEvaluationTrendDataSchema,
  });
}

/**
 * 获取员工绩效考核明细
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.pageNumber 页码（可选）
 * @param params.pageSize 每页条数（可选）
 * @returns 员工绩效考核明细
 */
export async function fetchEmployeePerformanceEvaluationDetail(params: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/ability/page/query/perf/detail',
    data: params,
    schema: PerformanceEvaluationDetailDataSchema,
  });
}

// 发展潜力相关接口

/**
 * 获取员工发展潜力趋势
 * @param data 员工信息
 * @returns 员工发展潜力趋势
 */
export async function fetchEmployeeDevelopmentPotentialTrend(data: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/ability/query/develop/pot/trend',
    data,
    schema: DevelopmentPotentialTrendDataSchema,
  });
}

/**
 * 获取员工发展潜力明细（培训考试明细）
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.pageNumber 页码（可选）
 * @param params.pageSize 每页条数（可选）
 * @returns 员工发展潜力明细
 */
export async function fetchEmployeeDevelopmentPotentialDetail(params: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/ability/page/query/develop/pot/detail',
    data: params,
    schema: DevelopmentPotentialDetailDataSchema,
  });
}
