import type {
  EmployeePerfStatQueryParams,
} from '@/schema';
import { http } from '@/network';
import {
  EmployeeGroupListResSchema,
  EmployeeGroupTotalInfoSchema,
  EmployeeOrderTrendSchema,
  EmployeePerfStatSchema,
} from '@/schema';

/**
 * 条件查询员工业绩统计数据
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.startTime 开始时间（可选）
 * @param params.endTime 结束时间（可选）
 * @returns 员工业绩统计数据
 */
export async function fetchEmployeePerfStat(params: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/perf/query/perf/stat',
    data: params,
    schema: EmployeePerfStatSchema,
  });
}

export async function fetchEmployeeOrderTrend(params: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/perf/query/order/trend',
    data: params,
    schema: EmployeeOrderTrendSchema,
  });
}

/**
 * 条件查询员工负责工作室总计信息
 * @param osUserId 员工ID
 * @returns 员工负责工作室总计信息
 */
export async function fetchEmployeeGroupTotalInfo(data: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/perf/query/group/total/info',
    data,
    schema: EmployeeGroupTotalInfoSchema,
  });
}

/**
 * 条件分页查询员工负责工作室列表
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.pageNumber 页码（可选）
 * @param params.pageSize 每页条数（可选）
 * @returns 员工负责工作室列表
 */
export async function fetchEmployeeGroupList(params: EmployeePerfStatQueryParams) {
  return http.post({
    url: '/api/dp/emp/perf/page/query/group/list',
    data: params,
    schema: EmployeeGroupListResSchema,
  });
}
