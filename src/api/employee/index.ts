import { http } from '@/network';
import { EmployeeInfoSchema } from '@/schema';

export * from '@/api/employee/ability';
export * from '@/api/employee/inv';
export * from '@/api/employee/perf';

/**
 * 获取员工信息
 * @param osUserId 员工ID
 * @param deptType 部门类型
 * @returns 员工信息
 */
export async function fetchEmployeeInfo(osUserId: number, deptType: string, deptId: number) {
  return http.post({
    url: '/api/dp/emp/query/detail/info',
    data: { osUserId, deptType, deptId },
    schema: EmployeeInfoSchema,
  });
}
