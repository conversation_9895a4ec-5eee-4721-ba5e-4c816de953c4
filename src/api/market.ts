import {
  IApiDpMarketHospitalSumStatParams,
  IApiDpMarketHospitalSumStat,
  IApiDpMarketDevelopStatParams,
  IApiDpMarketDevelopStat,
  IApiDpMarketVisitStatParams,
  IApiDpMarketVisitStat,
  IApiDpMarketPlanSumStatParams,
  IApiDpMarketPlanSumStat,
} from '@/interface/type';
import { http } from '@/network';

/** 市场医院汇总数据统计 */
export const getMarketSum = (params: IApiDpMarketHospitalSumStatParams) => {
  return http.post<IApiDpMarketHospitalSumStat>({
    url: '/api/dp/market/hospital/sum/stat',
    method: 'post',
    data: params,
  });
};

/** 市场开发情况 */
export const getMarketDevelop = (
  params: IApiDpMarketDevelopStatParams,
  ignoreRequestParams = false
) => {
  return http.post<IApiDpMarketDevelopStat>({
    url: '/api/dp/market/develop/stat',
    method: 'post',
    data: params,
    customConfig: { ignoreRequestParams },
  });
};

/** 团队工作--市场拜访记录 */
export const getMarketVisit = (
  params: IApiDpMarketVisitStatParams,
  ignoreRequestParams = false
) => {
  return http.post<IApiDpMarketVisitStat>({
    url: '/api/dp/market/visit/stat',
    method: 'post',
    data: params,
    customConfig: { ignoreRequestParams },
  });
};

/** 团队每日工作计划 */
export const getMarketPlan = (
  params: IApiDpMarketPlanSumStatParams,
  ignoreRequestParams = false
) => {
  return http.post<IApiDpMarketPlanSumStat>({
    url: '/api/dp/market/plan/sum/stat',
    method: 'post',
    data: params,
    customConfig: { ignoreRequestParams },
  });
};
