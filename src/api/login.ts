import type { IApiBpUserLogin, IApiBpUserLoginParams } from '@/interface/type';
import { http } from '@/network';

export function login(params: IApiBpUserLoginParams) {
  return http.post<IApiBpUserLogin>({
    url: '/api/bp/user/login',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

export function logout() {
  return http.post({
    url: '/api/bp/user/loginOut',
    method: 'post',
    data: {},
    customConfig: { reductDataFormat: false },
  });
}
