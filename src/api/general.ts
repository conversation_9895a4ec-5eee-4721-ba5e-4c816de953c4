import type {
  GeneralQueryParams,
} from '@/schema';
import { http } from '@/network';
import {
  CustomerTypeGeneralResSchema,
  DeptMarketGeneralStatSchema,
  DeptSellerGeneralResSchema,
  GeneralHospitalResSchema,
  GeneralOrderResSchema,
  GeneralPatientResSchema,
} from '@/schema';

/**
 * 获取市场开发数据
 * @param data 请求参数
 * @returns 市场开发数据
 */
export async function fetchGeneralMarketStatus(data: GeneralQueryParams) {
  return http.post({
    url: '/api/dp/general/market/sum/stat',
    data,
    schema: DeptMarketGeneralStatSchema,
  });
}

/**
 * 获取销售达成数据
 * @param data 请求参数
 */
export async function fetchGeneralSellerStatus(data: GeneralQueryParams) {
  return http.post({
    url: '/api/dp/general/seller/sum/stat',
    data,
    schema: DeptSellerGeneralResSchema,
  });
}

/**
 * 获取客户占比数据
 * @param data 请求参数
 * @returns 客户占比数据
 */
export async function fetchGeneralCustomerDistributionStatus(data: GeneralQueryParams) {
  return http.post({
    url: '/api/dp/customer/customer/num/stat',
    data,
    schema: CustomerTypeGeneralResSchema,
  });
}

/**
 * 获取总看板-业务覆盖、患者分布数据
 * @param data 请求参数
 * @returns 总看板-业务覆盖、患者分布数据
 */
export async function fetchGeneralHospitalStatus(data: GeneralQueryParams) {
  return http.post({
    url: '/api/dp/general/hospital/sum/stat',
    data,
    schema: GeneralHospitalResSchema,
  });
}
/**
 * 获取总看板-产出情况、资金投入数据
 * @param data 请求参数
 * @returns 总看板-产出情况、资金投入数据
 */
export async function fetchGeneralOrderStatus(data: GeneralQueryParams) {
  return http.post({
    url: '/api/dp/general/order/sum/stat',
    data,
    schema: GeneralOrderResSchema,
  });
}

/**
 * 获取总看板-在管患者数据
 * @param data 请求参数
 */
export async function fetchGeneralPatientStatus(data: GeneralQueryParams) {
  return http.post({
    url: '/api/dp/general/patient/distribution/stat',
    data,
    schema: GeneralPatientResSchema,
  });
}
