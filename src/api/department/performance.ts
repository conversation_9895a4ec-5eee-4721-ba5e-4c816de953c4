import type { DeptPerformanceQueryParams } from '@/schema/department/performance';
import { http } from '@/network';
import {
  DeptExamScoreTrendSchema,
  DeptPerformanceTrendSchema,
  DeptPerfScoreTrendSchema,
  DeptSubjScoreTrendSchema,
} from '@/schema/department/performance';

/**
 * 条件查询部门业绩走势
 * @param params 查询参数
 * @param params.deptId 部门ID
 * @param params.startTime 开始时间（可选）
 * @param params.endTime 结束时间（可选）
 * @returns 部门业绩走势数据
 */
export async function fetchDeptPerformanceTrend(params: DeptPerformanceQueryParams) {
  return http.post({
    url: '/api/dp/dept/perf/query/perf/trend',
    data: params,
    schema: DeptPerformanceTrendSchema,
  });
}

/**
 * 条件查询绩效评分走势
 * @param params 查询参数
 * @param params.deptId 部门ID
 * @param params.startTime 开始时间（可选）
 * @param params.endTime 结束时间（可选）
 * @returns 绩效评分走势数据
 */
export async function fetchDeptPerfScoreTrend(params: DeptPerformanceQueryParams) {
  return http.post({
    url: '/api/dp/dept/perf/query/perf/score/trend',
    data: params,
    schema: DeptPerfScoreTrendSchema,
  });
}

/**
 * 条件查询主观能动性得分走势
 * @param params 查询参数
 * @param params.deptId 部门ID
 * @param params.startTime 开始时间（可选）
 * @param params.endTime 结束时间（可选）
 * @returns 主观能动性得分走势数据
 */
export async function fetchDeptSubjScoreTrend(params: DeptPerformanceQueryParams) {
  return http.post({
    url: '/api/dp/dept/perf/query/subj/score/trend',
    data: params,
    schema: DeptSubjScoreTrendSchema,
  });
}

/**
 * 条件查询考核成绩走势
 * @param params 查询参数
 * @param params.deptId 部门ID
 * @param params.startTime 开始时间（可选）
 * @param params.endTime 结束时间（可选）
 * @returns 考核成绩走势数据
 */
export async function fetchDeptExamScoreTrend(params: DeptPerformanceQueryParams) {
  return http.post({
    url: '/api/dp/dept/perf/query/exam/score/trend',
    data: params,
    schema: DeptExamScoreTrendSchema,
  });
}
