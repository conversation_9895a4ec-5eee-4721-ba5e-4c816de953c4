import type { DeptEmployeeQueryParams } from '@/schema/department/employee';
import { http } from '@/network';
import {
  DeptAbilityDistResSchema,
  DeptEmployeeDistResSchema,
  DeptEmployeeListResSchema,
  DeptHierarchyResSchema,
  DeptPositionLevelResSchema,
} from '@/schema/department/employee';

/**
 * 条件分页查询员工清单
 * @param params 查询参数
 * @returns 员工清单
 */
export async function fetchEmployeeList(params: DeptEmployeeQueryParams) {
  const res = await http.post({
    url: '/api/dp/dept/emp/query/employee/list',
    data: params,
    schema: DeptEmployeeListResSchema,
  });
  return res;
}

/**
 * 条件查询部门员工分布
 * @param deptId 部门ID
 * @param deptType 部门类型
 * @returns 部门员工分布数据
 */
export async function fetchEmployeeDist(deptId: number, deptType: string) {
  const res = await http.post({
    url: '/api/dp/dept/emp/query/employee/dist',
    data: { deptId, deptType },
    schema: DeptEmployeeDistResSchema,
  });
  return res;
}

/**
 * 条件查询部门职级占比
 * @param deptId 部门ID
 * @param deptType 部门类型
 * @returns 部门职级占比数据
 */
export async function fetchPositionLevel(deptId: number, deptType: string) {
  const res = await http.post({
    url: '/api/dp/dept/emp/query/position/level',
    data: { deptId, deptType },
    schema: DeptPositionLevelResSchema,
  });
  return res;
}

/**
 * 条件查询部门能力分布
 * @param deptId 部门ID
 * @returns 部门能力分布数据
 */
export async function fetchAbilityDist(deptId: number, deptType: string) {
  const res = await http.post({
    url: '/api/dp/dept/emp/query/ability/dist',
    data: { deptId, deptType },
    schema: DeptAbilityDistResSchema,
  });
  return res;
}

/**
 * 查询部门架构
 * @param deptId 部门ID
 * @returns 部门架构数据
 */
export async function fetchDeptHierarchy(deptId?: number) {
  return await http.post({
    url: '/api/dp/dept/emp/query/hierarchy',
    data: { deptId },
    schema: DeptHierarchyResSchema,
  });
}
