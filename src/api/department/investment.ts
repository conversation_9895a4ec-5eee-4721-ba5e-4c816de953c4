import type {
  DeptInvestmentQueryParams,
} from '@/schema/department/investment';
import { http } from '@/network';
import {
  DeptInvestmentOverviewSchema,
  DeptInvestmentTrendSchema,
  DeptOutputTrendSchema,
} from '@/schema/department/investment';

/**
 * 获取部门投产总览
 * @param params 查询参数
 * @param params.deptId 部门ID
 * @param params.startTime 开始时间（可选）
 * @param params.endTime 结束时间（可选）
 * @returns 部门投产总览数据
 */
export async function fetchDeptInvestmentOverview(params: DeptInvestmentQueryParams) {
  return http.post({
    url: '/api/dp/dept/inv/output/query/total/info',
    data: params,
    schema: DeptInvestmentOverviewSchema,
  });
}

/**
 * 获取部门投入趋势
 * @param params 查询参数
 * @param params.deptId 部门ID
 * @param params.startTime 开始时间（可选）
 * @param params.endTime 结束时间（可选）
 * @param params.mergeMethod 合并方式（按日、按周、按月）
 * @returns 部门投入趋势数据
 */
export async function fetchDeptInvestmentTrend(params: DeptInvestmentQueryParams) {
  return http.post({
    url: '/api/dp/dept/inv/output/query/invest/trend',
    data: params,
    schema: DeptInvestmentTrendSchema,
  });
}

/**
 * 获取部门产出趋势
 * @param params 查询参数
 * @param params.deptId 部门ID
 * @param params.startTime 开始时间（可选）
 * @param params.endTime 结束时间（可选）
 * @param params.mergeMethod 合并方式（按日、按周、按月）
 * @returns 部门产出趋势数据
 */
export async function fetchDeptOutputTrend(params: DeptInvestmentQueryParams) {
  return http.post({
    url: '/api/dp/dept/inv/output/query/output/trend',
    data: params,
    schema: DeptOutputTrendSchema,
  });
}
