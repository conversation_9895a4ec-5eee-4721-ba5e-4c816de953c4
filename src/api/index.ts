import type {
  IApiDpDoctorReviewFollowSelect,
  IApiDpDoctorReviewFollowSelectParams,
} from '@/interface/type';
import { http } from '@/network';

export * from './dashboard';
export * from './department';
export * from './employee';
export * from './general';
export * from './hospital';

/** 筛选条件-系统中存在的省、地区、医院、工作小组、工作室级联 */
export function getSelect(params: IApiDpDoctorReviewFollowSelectParams) {
  return http.post<IApiDpDoctorReviewFollowSelect>({
    url: '/api/dp/doctor/review/follow/select',
    method: 'post',
    data: params,
  });
}

/**
 * 修改密码
 * @param params
 * @param params.oldPwd 旧密码
 * @param params.newPwd 新密码
 * @param params.osUserId 用户ID
 */
export function editPassword(params: {
  oldPwd: string;
  newPwd: string;
  osUserId: number;
}): Promise<boolean> {
  return http.post({
    url: '/api/bp/update/pwd/by/old',
    method: 'post',
    data: params,
  });
}
