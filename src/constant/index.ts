import customerPng from '@/assets/icon/customer.png';
import marketPng from '@/assets/icon/market.png';
import medicalPng from '@/assets/icon/medical.png';
import overviewPng from '@/assets/icon/overview.png';
import salePng from '@/assets/icon/sale.png';
import { keyBy } from 'lodash-es';

export * from './cache';
export * from './map';

/** 角色 */
export const ROLES = {
  /** 销售 */
  SELLER: 'SELLER',
  /** 市场 */
  MARKET: 'MARKET',
  /** 医学 */
  DOCTOR: 'DOCTOR',
  /** 硬件 */
  HARDWARE: 'HARDWARE',
  /** 客户 */
  CUSTOMER: 'CUSTOMER',
} as const;

/** 导航菜单 */
export const MENU_CONFIG = [
  { name: '总览', path: '/overview', icon: overviewPng, role: ROLES.DOCTOR },
  { name: '销售', path: '/sell', role: ROLES.SELLER, icon: salePng },
  { name: '市场', path: '/market', role: ROLES.MARKET, icon: marketPng },
  {
    name: '医学',
    role: ROLES.DOCTOR,
    icon: medicalPng,
    children: [
      { name: '工作情况', path: '/medical-work', role: ROLES.DOCTOR },
      { name: '管理效果', path: '/medical-manage', role: ROLES.DOCTOR },
    ],
  },
  { name: '硬件', path: '/hardware', role: ROLES.HARDWARE },
  { name: '客户', path: '/customer', role: ROLES.CUSTOMER, icon: customerPng },
];
/** 菜单角色映射 */
export const MENU_ROLE_MAP = MENU_CONFIG.reduce((pre, cur) => {
  if (cur.children) {
    const childRes = cur.children.reduce((p, c) => {
      return { ...p, [c.path!]: c.role };
    }, {});
    return { ...pre, ...childRes };
  }
  return { ...pre, [cur.path!]: cur.role };
}, {});
/** 销售面板锚点 */
export const SELL_ANCHOR = [
  { name: '工作室新购', id: 'anchor_sell_1' },
  { name: '新购订单走势', id: 'anchor_sell_2' },
  { name: '新购成交分析', id: 'anchor_sell_3' },
  { name: '未成交分析', id: 'anchor_sell_4' },
  { name: '退费/续费', id: 'anchor_sell_5' },
  { name: '团队工作计划', id: 'anchor_sell_6' },
];
/** 市场面板锚点 */
export const MARKET_ANCHOR = [
  { name: '市场开发情况', id: 'anchor_market_1' },
  { name: '团队工作', id: 'anchor_market_2' },
  { name: '团队工作计划', id: 'anchor_market_3' },
];
/** 医学-工作情况锚点 */
export const MEDICAL_WORK_ANCHOR = [
  // { name: '全部在管患者', id: 'anchor_work_1' },
  { name: '在管患者', id: 'anchor_work_2' },
  { name: '复查/随访', id: 'anchor_work_3' },
  { name: '指标达标率', id: 'anchor_work_4' },
  { name: '团队工作情况', id: 'anchor_work_5' },
  { name: '续费/退费', id: 'anchor_work_6' },
];
/** 医学-管理情况锚点 */
export const MEDICAL_MANAGE_ANCHOR = [
  // { name: '全部患者分布', id: 'anchor_manage_1' },
  { name: '复查/随访', id: 'anchor_manage_2' },
  { name: '指标达标率', id: 'anchor_manage_3' },
  { name: '会员退费分析', id: 'anchor_manage_4' },
];
/** 模块渐变色 */
export const BG_COLORS = ['#FFFBE7', '#EFFCEE', '#FEF5EB', '#E7EBFF'];

export const CUSTOMER_DS = [
  {
    name: '客户',
    id: 'needCustomerData',
    field: 'customer',
  },
  {
    name: '病区',
    id: 'needWardData',
    field: 'ward',
  },
  {
    name: '医院',
    id: 'needHospitalData',
    field: 'hospital',
  },
  {
    name: '地区',
    id: 'needRegionData',
    field: 'region',
  },
];
export const TEAM_CONSULT = [
  { id: 'ALL', name: '全部' },
  { id: 'BY_PATIENT', name: '患者发起' },
  { id: 'BY_DOCTOR', name: '管理团队发起' },
];
export const TEAM_INDICATOR = [
  { id: 'ALL', name: '全部' },
  { id: 'BLOOD_PRESSURE', name: '血压' },
  { id: 'HEART_RATE', name: '心率' },
  { id: 'BLOOD_SUGAR', name: '血糖' },
];
export const TEAM_CALL = [
  { id: 'ALL', name: '全部' },
  { id: 'CALL_IN', name: '呼入' },
  { id: 'CALL_OUT', name: '呼出' },
];
export const INDICATORS = [
  { id: 'ALL', name: '全部' },
  { id: 'BLOOD_PRESSURE', name: '血压' },
  { id: 'HEART_RATE', name: '心率' },
  { id: 'BLOOD_SUGAR', name: '血糖' },
  { id: 'LDL', name: '低密度脂蛋白' },
];
export const INDICATORS_MAP = keyBy(INDICATORS, 'id');
export const CUSTOMER_DS_MAP = keyBy(CUSTOMER_DS, 'id');
export const TEAM_CONSULT_MAP = keyBy(TEAM_CONSULT, 'id');
export const TEAM_INDICATOR_MAP = keyBy(TEAM_INDICATOR, 'id');
export const TEAM_CALL_MAP = keyBy(TEAM_CALL, 'id');

/** Echarts 颜色集合 */
export const ECHARTS_COLORS = [
  '#5285EB',
  '#4EC244',
  '#E88B48',
  '#43A1E6',
  '#B4CC3B',
  '#EBB852',
  '#3BB4CC',
  '#EBD254',
  '#EB525F',
  '#D63CBD',
  '#9FBCF5',
  '#98E092',
  '#F2BE99',
  '#95CDF5',
  '#D6E68C',
  '#F5D89F',
  '#8CD6E6',
  '#F5E69D',
  '#F59FA6',
  '#EB8DDB',
];

/**
 * 汇总方式
 */
export const MERGE_METHOD = [
  { id: 'BY_DAY_FIRST', name: '按日' },
  { id: 'BY_WEEK_FIRST', name: '按周' },
  { id: 'BY_MONTH_FIRST', name: '按月' },
];
