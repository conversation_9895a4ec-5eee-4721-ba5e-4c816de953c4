import type { AbilityResult, JobStatus, JobSubStatus, Sex } from '@/schema';

/**
 * 性别
 */
export const SEX_TEXT: Record<Sex, string> = {
  FEMALE: '女',
  MALE: '男',
  UNKNOWN: '未知',
} as const;

/**
 * 员工状态
 */
export const JOB_STATUS_TEXT: Record<JobStatus, string> = {
  LEAVE_JOB: '离职',
  ON_JOB: '在职',
  WAIT_ENTRY: '等待入职',
};

/**
 * 员工子状态
 */
export const JOB_SUB_STATUS_TEXT: Record<JobSubStatus, string> = {
  NO_STATUS: '无状态',
  OFFICIAL: '正式员工',
  PROBATION: '试用期',
  RESIGNED: '已离职',
  WAIT_FOR_LEAVE: '待离职',
};

export const ABILITY_RESULT_TEXT: Record<AbilityResult, string> = {
  BACKBONE: '中坚力量',
  BUSINESS_CORE: '业务核心骨干',
  PERF_WEAK_QUA_OK: '业绩不佳但素质尚可',
  QUA_CORE: '素质核心骨干',
  QUA_WEAK_ABI_OK: '素质不佳但能力尚可',
  SUPER_EMP: '超级员工',
  UNDER_PERFORMER: '失败者',
};

/**
 * 能力类型
 */
export const ABILITY_TYPE = {
  PERF_ACHIEVE: '业绩达成',
  PROF_ABILITY: '专业能力',
  DEVELOP_POT: '发展潜力',
  PERF_ASSESS: '绩效考核',
  SUBJ_ACTIVITY: '主观能动性',
} as const;
