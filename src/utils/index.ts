import { useLocalStorage } from '@vueuse/core';
import dayjs from 'dayjs';
import { sumBy } from 'lodash-es';
import {
  COLORS_MORE,
  getTooltipConfig,
  LABEL,
} from '@/components/BaseChart/options';
import { CUSTOMER_DS_MAP } from '@/constant';
import { USERID_KEY } from '@/constant/cache';

export * from './decrypt';

/** 格式化时间未指定格式字符串 */
export function formatTimeTemplate(time: string | undefined | number, template = 'YYYY-MM-DD HH:mm:ss') {
  if (!time)
    return undefined;
  return dayjs(time).format(template);
}

/** 获取当月开始结束时间范围 */
export function getDatesRange(date, type: any = 'month') {
  const start = dayjs(date).startOf(type).format('YYYY-MM-DD');
  let end = dayjs(date).endOf(type).format('YYYY-MM-DD');
  if (type === 'month') {
    end = dayjs().isSame(end, 'month') ? dayjs().format('YYYY-MM-DD') : end;
  }
  return [start, end];
}
interface IChartDataParams {
  list?: Record<string, any>[];
  key: string;
  name: string;
  radius?: boolean;
  color?: string;
  extraList?: {
    name: string;
    key: string;
    symbol?: string;
    format?: (v: number) => string;
  }[];
  hiddenNames?: string[];
  symbol?: string;
  subName?: string;
  subGroupName?: string;
  groupNameKey?: string;
  stack?: string;
  groupName?: string;
  otherParams?: any;
  order?: number;
  showLabel?: boolean;
  labelSymbol?: string;
  format?: (val: number) => string;
}
/** 普通柱状图数据生成 */
export function getChartBarDatas({
  list = [],
  key,
  name,
  radius = false,
  extraList = [],
  symbol,
  format,
  order,
  hiddenNames = [],
  otherParams,
  showLabel = false,
  labelSymbol = '',
}: IChartDataParams) {
  return {
    name,
    data: list.map(item => ({
      value: item[key] ? item[key] : 0,
      customSymbol: format ? '' : symbol,
      order,
      format,
      label: showLabel
        ? labelSymbol
          ? { ...LABEL, formatter: v => v.value + labelSymbol }
          : LABEL
        : null,
      itemStyle: {
        borderRadius: radius ? [4, 4, 0, 0] : null,
      },
      otherParams: { ...otherParams, timeStamp: item.timeStamp },
      extraList: extraList.map(v => ({
        ...v,
        label: v.name,
        value: v.format
          ? v.format(item?.[v.key])
          : `${item?.[v.key] ?? 0}${v.symbol ?? ''}`,
      })),
      hiddenNames,
    })),
  };
}
/** 普通折线图数据生成 */
export function getChartLineDatas({
  list = [],
  key,
  name,
  format,
  order,
  otherParams,
  hiddenNames,
  symbol = '%',
}: IChartDataParams) {
  return {
    name,
    type: 'line',
    symbol: 'none',
    yAxisIndex: 1,
    color: '#2FB324',
    data: list.map(item => ({
      value: (item[key] ?? 0).toFixed(2),
      order,
      format,
      otherParams,
      hiddenNames,
      customSymbol: format ? '' : symbol,
    })),
  };
}
/** 获取分组平铺图表 data  */
export function getCurData({
  list,
  groupName,
  subGroupName,
  groupNameKey,
  subName,
  key,
  format,
  order,
  otherParams,
  hiddenNames,
  radius = false,
  showLabel,
  extraList = [],
}: any) {
  let res
    = list?.map(v => v?.dataList?.find(val => val[groupNameKey] === groupName))
      ?? [];
  res = res.map((item) => {
    const curVal = item?.[key] ?? 0;
    const result: any = {
      value: curVal,
      format,
      order,
      itemName: subName,
      subGroupName,
      hiddenNames,
      otherParams,
      itemStyle: {
        borderRadius: radius ? [2, 2, 0, 0] : null,
      },
      label: showLabel ? LABEL : null,
    };
    result.extraList = extraList.map(v => ({
      ...v,
      label: v.name,
      value: v.format
        ? v.format(item?.[v.key])
        : `${(item?.[v.key] ?? 0).toFixed(2)}${v.symbol ?? ''}`,
    }));
    return result;
  });
  return res;
}
/** 获取分组stack图表 data */
export function getCurStackData({
  list,
  groupName,
  subGroupName,
  groupNameKey,
  name,
  key,
  radius = false,
  otherParams,
  format,
  order,
  extraList = [],
}: any) {
  let res
    = list?.map(v => v?.dataList?.find(val => val[groupNameKey] === groupName))
      ?? [];
  res = res.map((item) => {
    const curVal = item?.[key] ?? null;
    const result: any = {
      value: curVal,
      itemName: name,
      groupName,
      subGroupName,
      otherParams,
      format,
      order,
      itemStyle: {
        borderRadius: radius ? [2, 2, 0, 0] : null,
      },
    };
    result.extraList = extraList.map(v => ({
      ...v,
      label: v.name,
      value: v.format
        ? v.format(item?.[v.key])
        : `${item?.[v.key] ?? 0}${v.symbol ?? ''}`,
    }));
    return result;
  });
  return res;
}
/** 获取分组stack折线图 series 配置 */
export function getChartStackLineDatas({
  list = [],
  key,
  name,
  groupName,
  subGroupName,
  groupNameKey,
  otherParams,
  format,
  order,
  symbol = '%',
  extraList = [],
}: IChartDataParams) {
  return {
    name,
    type: 'line',
    connectNulls: true,
    symbol: 'none',
    yAxisIndex: 1,
    data: getCurStackData({
      list,
      groupName,
      subGroupName,
      otherParams,
      name,
      format,
      order,
      groupNameKey,
      key,
      extraList,
    }).map(v => ({
      ...v,
      customSymbol: format ? '' : symbol,
    })),
  };
}
/** 获取分组 stack 柱状图 series 配置 */
export function getChartStackBarDatas({
  list = [],
  key,
  name,
  groupName,
  subGroupName,
  groupNameKey,
  radius = false,
  otherParams,
  extraList = [],
  format,
  order,
  stack,
}: IChartDataParams) {
  return {
    name,
    barMaxWidth: 20,
    barGap: 0,
    xAxisIndex: 0,
    stack,
    data: getCurStackData({
      list,
      groupName,
      subGroupName,
      groupNameKey,
      otherParams,
      name,
      format,
      order,
      key,
      radius,
      extraList,
    }),
  };
}
/** 获取分组柱状图 series 配置 */
export function getChartFlatBarDatas({
  list = [],
  key,
  name,
  subName,
  subGroupName,
  groupNameKey,
  color,
  radius = false,
  extraList = [],
  otherParams,
  format,
  order,
  stack,
  showLabel = false,
}: IChartDataParams) {
  return {
    name,
    barMaxWidth: 20,
    color,
    barGap: 0,
    xAxisIndex: 0,
    stack,
    data: getCurData({
      list,
      format,
      order,
      groupName: name,
      subGroupName,
      groupNameKey,
      otherParams,
      subName,
      key,
      radius,
      showLabel,
      extraList,
    }),
  };
}
/** 获取分组折线图 series 配置 */
export function getChartFlatLineDatas({
  list = [],
  key,
  name,
  subName,
  groupNameKey,
  subGroupName,
  hiddenNames,
  otherParams,
  format,
  order,
  symbol = '%',
  extraList = [],
}: IChartDataParams) {
  return {
    name,
    type: 'line',
    connectNulls: true,
    symbol: 'none',
    yAxisIndex: 1,
    data: getCurData({
      list,
      groupName: name,
      subGroupName,
      otherParams,
      subName,
      hiddenNames,
      format,
      order,
      groupNameKey,
      key,
      extraList,
    }).map(v => ({
      ...v,
      customSymbol: format ? '' : symbol,
    })),
  };
}
export function toFixed(number, precision = 2) {
  number = `${Math.round(`${+number}e${precision}`) / 10 ** precision}`;
  const s = number.split('.');
  if ((s[1] || '').length < precision) {
    s[1] = s[1] || '';

    s[1] += Array.from({ length: precision - s[1].length }).fill('0').join('');
  }

  return s.join('.');
}
/** 成交分析数据转换 */
export function transformNewOrderData(item: any, portrayal = false) {
  const ledgerSum = item?.ledgerSum ?? {};
  const ledgerAgeSpread = item?.ledgerAgeSpread ?? [];
  const ledgerKeyPerson = item?.ledgerKeyPerson ?? [];
  const ledgerDealReason = item?.ledgerDealReason ?? [];
  const ledgerCommTrans = item?.ledgerCommTrans ?? {};
  const {
    groupNosNum,
    commLedgerNum,
    registerLedgerNum,
    registerUserNum,
    dealUserNum,
  } = ledgerCommTrans;
  return {
    value: ledgerSum,
    age: transformPieData(ledgerAgeSpread, {
      name: 'ageRange',
      value: 'patientNum',
      percent: 'patientNumPercent',
    }),
    personData: {
      xData: ledgerKeyPerson.map(v => v.keyPersonName),
      data: [
        {
          name: '数量',
          data: ledgerKeyPerson
            .map(v => v.keyPersonNum)
            .map(v => ({ value: v, label: LABEL })),
        },
      ],
    },
    reasonData: {
      xData: ledgerDealReason.map(v => v.dealReasonName),
      data: [
        {
          name: '数量',
          data: ledgerDealReason
            .map(v => v.dealReasonNum)
            .map(v => ({ value: v, label: LABEL })),
        },
      ],
    },
    commTransData: {
      groupNosNum: {
        value: groupNosNum,
        subVal: groupNosNum ? '100.00' : '0.00',
      },
      registerUserNum: {
        value: registerUserNum,
        subVal: groupNosNum
          ? toFixed((registerUserNum * 100) / groupNosNum)
          : '0.00',
      },
      registerLedgerNum: {
        value: registerLedgerNum,
        subVal: registerUserNum
          ? toFixed((registerLedgerNum * 100) / registerUserNum)
          : '0.00',
      },
      commLedgerNum: {
        value: commLedgerNum,
        subVal: registerLedgerNum
          ? toFixed((commLedgerNum * 100) / registerLedgerNum)
          : '0.00',
      },
      dealUserNum: {
        value: dealUserNum,
        subVal: commLedgerNum
          ? toFixed((dealUserNum * 100) / commLedgerNum)
          : '0.00',
      },
    },
    portrayal,
  };
}
/** 未成交分析数据转换 */
export function transformNoBuyData(item: any) {
  const ledgerSum = item?.ledgerSum ?? {};
  const ledgerAgeSpread = item?.ledgerAgeSpread ?? [];
  const ledgerKeyPerson = item?.ledgerKeyPerson ?? [];
  const ledgerNoDealReason = item?.ledgerNoDealReason ?? [];
  return {
    value: ledgerSum,
    age: transformPieData(ledgerAgeSpread, {
      name: 'ageRange',
      value: 'patientNum',
      percent: 'patientNumPercent',
    }),
    personData: {
      xData: ledgerKeyPerson.map(v => v.keyPersonName),
      data: [
        {
          name: '数量',
          data: ledgerKeyPerson
            .map(v => v.keyPersonNum)
            .map(v => ({ value: v, label: LABEL })),
        },
      ],
    },
    reasonData: {
      xData: ledgerNoDealReason.map(v => v.noDealReasonName),
      data: [
        {
          name: '数量',
          data: ledgerNoDealReason
            .map(v => v.noDealReasonNum)
            .map(v => ({ value: v, label: LABEL })),
        },
      ],
    },
  };
}
/** 市场 - 团队工作计划数据转换 */
export function transformWorkPlan(item, key = 'All') {
  const taskTotal
    = item.itemNumDateStat?.[`itemTotal${key}`]?.reduce(
      (pre, cur) => pre + sumBy(cur?.dataList, 'planItemNum'),
      0,
    ) ?? 0;
  const taskXData = item.itemNumDateStat?.[`itemTotal${key}`]?.map(
    v => v.statDate,
  );
  const taskPlanPieData = transformPieData(
    item.itemNumExamineStat?.[`itemDetail${key}`]?.itemList,
    { name: 'itemName', value: 'itemNum', percent: 'itemNumPercent' },
  );
  const taskCompletePieData = transformPieData(
    item.itemNumExecuteStat?.[`itemDetail${key}`]?.itemList,
    { name: 'itemName', value: 'itemNum', percent: 'itemNumPercent' },
  );
  const hourTotal = Number(
    (
      item.itemTimeDateStat?.[`itemTotal${key}`]?.reduce(
        (pre, cur) => pre + sumBy(cur?.dataList, 'planItemTime'),
        0,
      ) ?? 0
    ).toFixed(1),
  );
  const hourXData = item.itemTimeDateStat?.[`itemTotal${key}`]?.map(
    v => v.statDate,
  );
  const hourPlanPieData = transformPieData(
    item.itemTimeExamineStat?.[`itemDetail${key}`]?.itemList,
    { name: 'itemName', value: 'itemTime', percent: 'itemTimePercent' },
  );
  const hourCompletePieData = transformPieData(
    item.itemTimeExecuteStat?.[`itemDetail${key}`]?.itemList,
    { name: 'itemName', value: 'itemTime', percent: 'itemTimePercent' },
  );
  return {
    taskData: {
      total: taskTotal,
      taskXData,
      taskPlanPieData,
      taskCompletePieData,
    },
    hourData: {
      total: hourTotal,
      hourXData,
      hourPlanPieData,
      hourCompletePieData,
    },
  };
}

/** 金额千分位 */
export function toThousands(n) {
  if (n === void 0)
    return '';
  const parts = n.toString().split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return parts
    .join('.')
    .replace(/(\.\d*?)0+$/, '$1')
    .replace(/\.$/, '');
}

/** 饼图数据转换 */
export function transformPieData(list: any = [], keyMap: any = {}) {
  const keys = Object.keys(keyMap);
  const res = list?.map((v) => {
    return keys.reduce((pre, cur) => {
      return { ...pre, [cur]: v[keyMap[cur]] };
    }, {});
  });
  return res;
}

/** 基础请求参数 */
export function getBaseParams() {
  return { osUserId: useLocalStorage(USERID_KEY, 0).value };
}
export function generateNormalBarLineData({
  list,
  config,
  colorKey,
  xDataKey = 'statDate',
  groupNameKey,
  subGroupNameKey = '',
  group = false,
  timeStampKey = '',
  hiddenNames = [],
  otherParams = {},
  showLabel = false,
}) {
  if (group) {
    const groups
      = list
        ?.find(v => v.dataList?.length)
        ?.dataList
        ?.map(v => ({
          groupName: v[groupNameKey],
          subGroupName: v[subGroupNameKey],
        }))
        ?.filter(v => !!v) ?? [];

    const res: any[] = [];
    for (let i = 0; i < groups.length; i++) {
      for (const v of config) {
        const item = {
          ...v,
          list,
          subName: v.name,
          name: groups[i].groupName,
          subGroupName: groups[i].subGroupName,
          otherParams,
          hiddenNames,
          radius: v.type === 'bar',
          groupNameKey,
          showLabel,
          color: v.key === colorKey ? COLORS_MORE[i] : undefined,
        };
        if (v.type === 'line') {
          res.push(getChartFlatLineDatas(item));
        }
        else {
          res.push(getChartFlatBarDatas(item));
        }
      }
    }
    return {
      xData: list?.map(v => v[xDataKey]),
      data: res,
    };
  }
  else {
    const flatItem = list?.map(v => ({
      label: v[xDataKey],
      timeStamp: v[timeStampKey],
      ...v.dataList?.[0],
    }));
    const data = config.map((v) => {
      const item = { ...v, list: flatItem, hiddenNames, showLabel };
      if (v.type === 'line') {
        return getChartLineDatas(item);
      }
      return getChartBarDatas(item);
    });

    return {
      xData: flatItem?.map(v => v.label),
      data,
    };
  }
}
export function generateGroupLineData({
  list,
  symbol = '%',
  config,
  groupNameKey,
}) {
  const groups
    = list
      ?.find(v => v.dataList?.length)
      ?.dataList
      ?.map(v => v?.[groupNameKey]) ?? [];

  const res: any[] = [];
  for (let i = 0; i < groups.length; i++) {
    for (const v of config) {
      res.push({
        name: groups[i],
        type: 'line',
        symbol: 'none',
        data: list.map((item) => {
          const curItem = item?.dataList?.[i] ?? {};
          return {
            value: (curItem?.[v.key] ?? 0)?.toFixed(2),
            name: v.name,
            customSymbol: symbol,
            extraList: v.extraList.map(val => ({
              ...val,
              label: val.name,
              value: val.format
                ? val.format(item?.[val.key])
                : `${curItem[val.key] ?? 0}${val.symbol ?? ''}`,
            })),
          };
        }),
      });
    }
  }
  return res;
}
export function generateStackBarData({
  list,
  config,
  xDataKey = 'statDate',
  groupNameKey,
  group = false,
  hiddenNames = [],
  timeStampKey = '',
  subGroupNameKey = '',
  otherParams = {},
  showLabel = false,
}) {
  if (group) {
    const groups
      = list
        ?.find(v => v.dataList?.length)
        ?.dataList
        ?.map(v => ({
          groupName: v[groupNameKey],
          subGroupName: v[subGroupNameKey],
        })) ?? [];
    const res: any[] = [];
    for (let i = 0; i < groups.length; i++) {
      for (const v of config) {
        const item = {
          ...v,
          list,
          otherParams,
          hiddenNames,
          groupName: groups[i].groupName,
          subGroupName: groups[i].subGroupName,
          name: v.name,
          groupNameKey,
        };
        if (v.type === 'bar') {
          item.stack = `stack_${i}`;
          res.push(getChartStackBarDatas(item));
        }
        else {
          res.push(getChartStackLineDatas(item));
        }
      }
    }
    return {
      xData: list?.map(v => v[xDataKey]),
      data: res,
      legends: config.map(v => v.name),
    };
  }
  else {
    const flatItem = list?.map(v => ({
      label: v[xDataKey],
      timeStamp: v[timeStampKey],
      ...v.dataList?.[0],
    }));
    const data = config.map((v) => {
      const item = {
        ...v,
        otherParams,
        hiddenNames,
        list: flatItem,
        showLabel,
      };
      if (v.type === 'line') {
        return getChartLineDatas(item);
      }
      return { ...getChartBarDatas(item), stack: 'total' };
    });
    return {
      xData: flatItem?.map(v => v.label),
      data,
    };
  }
}
export const getStart = time => dayjs(time).startOf('d').valueOf();
export const getEnd = time => dayjs(time).endOf('d').valueOf();
export function fillTime(dates) {
  return [getStart(dates[0]), getEnd(dates[1])];
}

export function getMergeType(dates) {
  return dayjs(dates[1]).isSame(dates[0], 'month')
    ? 'BY_DAY_FIRST'
    : 'BY_MONTH_FIRST';
}

export function transformSelectorList(list: any = [], keyMap: any = {}) {
  return list?.map(v => ({
    ...v,
    id: v[keyMap.id],
    name: v[keyMap.name],
    pId: v[keyMap.pId] ?? null,
  }));
}

export function deptSelectorTransform(list, prefix = 'dept') {
  return transformSelectorList(list, {
    id: `${prefix}Id`,
    name: `${prefix}Name`,
    pId: `${prefix}ParentId`,
  });
}
export function personSelectorTransform(list, prefix = 'user') {
  return transformSelectorList(list, {
    id: `${prefix}Id`,
    name: `${prefix}Name`,
  });
}
export function transformMergeDept(mergeList: any = []) {
  return mergeList.map(v => ({
    mergedDeptId: v.id,
    mergedDeptName: v.name,
    deptIds: v.list,
  }));
}
export function customerTransformTooltip(time, type, unit = '个') {
  return getTooltipConfig({
    transform: (params) => {
      const item = params?.[0];
      return {
        title: dayjs(time).format('YYYY年M月'),
        row: 5,
        items: [
          {
            label: item.name,
            value: item.value + unit + CUSTOMER_DS_MAP[type].name,
          },
        ],
      };
    },
  });
}
export function formatSeconds(second: number = 0) {
  if (!second)
    return `${0}s`;
  const m = ~~(second / 60);
  const s = second % 60;
  return `${m > 0 ? `${m}min` : ''}${s > 0 ? `${s}s` : ''}`;
}

export function asyncRun(fn, delayTime = 0) {
  setTimeout(() => {
    fn?.();
  }, delayTime);
}

/** 判断日期间隔是否一年 */
export function isWholeYear(dates) {
  const diff = dayjs(dates?.[1]).diff(dates?.[0], 'month');
  return diff === 11;
}
