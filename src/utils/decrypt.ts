import AES from 'crypto-js/aes';
import CryptoJSCore from 'crypto-js/core';
import encUtf8 from 'crypto-js/enc-utf8';
import padPkcs7 from 'crypto-js/pad-pkcs7';

export function decrypt(content: string, id: string, key: string) {
  // 处理密钥和IV（与Java行为一致）
  const keyBytes = encUtf8.parse(key);
  const ivBytes = encUtf8.parse(id);

  // 执行AES-CBC解密
  const decrypted = AES.decrypt(
    content,
    keyBytes,
    {
      iv: ivBytes,
      mode: CryptoJSCore.mode.CBC,
      padding: padPkcs7,
    },
  );

  // 返回UTF-8字符串
  return decrypted.toString(encUtf8);
}

/**
 * 金额解密
 * @param content 加密后的金额
 * @returns 解密后的金额
 */
export function moneyDecrypt(content: string | number): number {
  if (typeof content === 'number') {
    return content;
  }
  const id = import.meta.env.VITE_AES_ID;
  const key = import.meta.env.VITE_AES_KEY;

  const decrypted = decrypt(content, id, key);
  if (decrypted) {
    return Number(decrypted.replace(',', ''));
  }
  else {
    return 0;
  }
}
