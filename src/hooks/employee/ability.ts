import type { EmployeePerfStatQueryParams } from '@/schema';
import {
  fetchEmployeeAbility,
  fetchEmployeeAbilityTrend,
  fetchEmployeeBusinessAbilityDetail,
  fetchEmployeeBusinessAbilityTrend,
  fetchEmployeeDevelopmentPotentialDetail,
  fetchEmployeeDevelopmentPotentialTrend,
  fetchEmployeePerformanceEvaluationDetail,
  fetchEmployeePerformanceEvaluationTrend,
  fetchEmployeeProfessionalAbilityDetail,
  fetchEmployeeProfessionalAbilityTrend,
  fetchEmployeeSubjectiveInitiativeDetail,
  fetchEmployeeSubjectiveInitiativeTrend,
} from '@/api';
import { useQuery } from '@tanstack/vue-query';

/**
 * 获取员工当前能力
 * @param osUserId 员工ID
 * @returns 员工当前能力查询结果
 */
export function useEmployeeAbility(osUserId: number) {
  return useQuery({
    queryKey: ['employeeAbility', osUserId],
    queryFn: () => fetchEmployeeAbility(osUserId),
    enabled: !!osUserId,
  });
}

/**
 * 获取员工能力得分趋势
 * @param osUserId 员工ID
 * @returns 员工能力得分趋势查询结果
 */
export function useEmployeeAbilityTrend(osUserId: number) {
  return useQuery({
    queryKey: ['employeeAbilityTrend', osUserId],
    queryFn: () => fetchEmployeeAbilityTrend(osUserId),
    enabled: !!osUserId,
  });
}

// 业务达成能力相关 hooks

/**
 * 获取员工业务达成趋势
 * @param osUserId 员工ID
 * @returns 员工业务达成趋势查询结果
 */
export function useEmployeeBusinessAbilityTrend(osUserId: number) {
  return useQuery({
    queryKey: ['employeeBusinessAbilityTrend', osUserId],
    queryFn: () => fetchEmployeeBusinessAbilityTrend(osUserId),
    enabled: !!osUserId,
  });
}

/**
 * 获取员工业务达成明细
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.pageNumber 页码（可选）
 * @param params.pageSize 每页条数（可选）
 * @returns 员工业务达成明细查询结果
 */
export function useEmployeeBusinessAbilityDetail(params: EmployeePerfStatQueryParams) {
  const { osUserId, pageNumber, pageSize } = toRefs(params);
  return useQuery({
    queryKey: ['employeeBusinessAbilityDetail', osUserId, pageNumber, pageSize],
    queryFn: () => fetchEmployeeBusinessAbilityDetail(params),
    enabled: !!params.osUserId,
  });
}

// 专业能力相关 hooks

/**
 * 获取员工专业能力趋势
 * @param osUserId 员工ID
 * @returns 员工专业能力趋势查询结果
 */
export function useEmployeeProfessionalAbilityTrend(osUserId: number) {
  return useQuery({
    queryKey: ['employeeProfessionalAbilityTrend', osUserId],
    queryFn: () => fetchEmployeeProfessionalAbilityTrend(osUserId),
    enabled: !!osUserId,
  });
}

/**
 * 获取员工专业能力明细
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.pageNumber 页码（可选）
 * @param params.pageSize 每页条数（可选）
 * @returns 员工专业能力明细查询结果
 */
export function useEmployeeProfessionalAbilityDetail(params: EmployeePerfStatQueryParams) {
  return useQuery({
    queryKey: ['employeeProfessionalAbilityDetail', params],
    queryFn: () => fetchEmployeeProfessionalAbilityDetail(params),
    enabled: !!params.osUserId,
  });
}

// 主观能动性相关 hooks

/**
 * 获取员工主观能动性趋势
 * @param osUserId 员工ID
 * @returns 员工主观能动性趋势查询结果
 */
export function useEmployeeSubjectiveInitiativeTrend(osUserId: number) {
  return useQuery({
    queryKey: ['employeeSubjectiveInitiativeTrend', osUserId],
    queryFn: () => fetchEmployeeSubjectiveInitiativeTrend(osUserId),
    enabled: !!osUserId,
  });
}

/**
 * 获取员工主观能动性明细
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.pageNumber 页码（可选）
 * @param params.pageSize 每页条数（可选）
 * @returns 员工主观能动性明细查询结果
 */
export function useEmployeeSubjectiveInitiativeDetail(params: EmployeePerfStatQueryParams) {
  return useQuery({
    queryKey: ['employeeSubjectiveInitiativeDetail', params],
    queryFn: () => fetchEmployeeSubjectiveInitiativeDetail(params),
    enabled: !!params.osUserId,
  });
}

// 绩效考核相关 hooks

/**
 * 获取员工绩效考核趋势
 * @param osUserId 员工ID
 * @returns 员工绩效考核趋势查询结果
 */
export function useEmployeePerformanceEvaluationTrend(osUserId: number) {
  return useQuery({
    queryKey: ['employeePerformanceEvaluationTrend', osUserId],
    queryFn: () => fetchEmployeePerformanceEvaluationTrend(osUserId),
    enabled: !!osUserId,
  });
}

/**
 * 获取员工绩效考核明细
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.pageNumber 页码（可选）
 * @param params.pageSize 每页条数（可选）
 * @returns 员工绩效考核明细查询结果
 */
export function useEmployeePerformanceEvaluationDetail(params: EmployeePerfStatQueryParams) {
  return useQuery({
    queryKey: ['employeePerformanceEvaluationDetail', params],
    queryFn: () => fetchEmployeePerformanceEvaluationDetail(params),
    enabled: !!params.osUserId,
  });
}

// 发展潜力相关 hooks

/**
 * 获取员工发展潜力趋势
 * @param osUserId 员工ID
 * @returns 员工发展潜力趋势查询结果
 */
export function useEmployeeDevelopmentPotentialTrend(osUserId: number) {
  return useQuery({
    queryKey: ['employeeDevelopmentPotentialTrend', osUserId],
    queryFn: () => fetchEmployeeDevelopmentPotentialTrend(osUserId),
    enabled: !!osUserId,
  });
}

/**
 * 获取员工发展潜力明细（培训考试明细）
 * @param params 查询参数
 * @param params.osUserId 员工ID
 * @param params.pageNumber 页码（可选）
 * @param params.pageSize 每页条数（可选）
 * @returns 员工发展潜力明细查询结果
 */
export function useEmployeeDevelopmentPotentialDetail(params: EmployeePerfStatQueryParams) {
  return useQuery({
    queryKey: ['employeeDevelopmentPotentialDetail', params],
    queryFn: () => fetchEmployeeDevelopmentPotentialDetail(params),
    enabled: !!params.osUserId,
  });
}
