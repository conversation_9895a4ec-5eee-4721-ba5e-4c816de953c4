import { useLocalStorage } from '@vueuse/core';
import { createRouter, createWebHashHistory } from 'vue-router';
import { MENU_ROLE_MAP } from '@/constant';
import { ROLES_KEY } from '@/constant/cache';
import Layout from '@/layout/index.vue';
import Customer from '@/pages/Customer/index.vue';
import Hardware from '@/pages/Hardware/index.vue';
import Login from '@/pages/Login/index.vue';
import Market from '@/pages/Market/index.vue';
import MedicalManage from '@/pages/MedicalManage/index.vue';
import MedicalWork from '@/pages/MedicalWork/index.vue';
import Sell from '@/pages/Sell/index.vue';

const Overview = () => import('@/pages/Overview/index.vue');
const Dashboard = () => import('@/pages/Dashboard/index.vue');
const Hospital = () => import('@/pages/Hospital/index.vue');
const Department = () => import('@/pages/Department/index.vue');

const router = createRouter({
  history: createWebHashHistory(),
  scrollBehavior() {
    document.body.scrollTop = 0;
  },
  routes: [
    {
      path: '/',
      name: 'Index',
      redirect: '/login',
    },
    {
      path: '/login',
      name: 'Login',
      meta: {
        ignorePermission: true,
        title: '登录',
      },
      component: Login,
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      meta: {
        title: '哈瑞特医疗数据中心',
        ignorePermission: true,
      },
      component: Dashboard,
    },
    {
      path: '/home',
      name: 'Home',
      meta: {
        title: '首页',
      },
      component: Layout,
      children: [
        {
          path: '/overview',
          name: 'overview',
          meta: {
            title: '总览',
          },
          component: Overview,
        },
        {
          path: '/sell',
          name: 'sell',
          meta: {
            title: '销售',
          },
          component: Sell,
        },
        {
          path: '/market',
          name: 'market',
          meta: {
            title: '市场',
          },
          component: Market,
        },
        {
          path: '/medical-manage',
          name: 'medical-manage',
          meta: {
            title: '管理效果',
          },
          component: MedicalManage,
        },
        {
          path: '/medical-work',
          name: 'medical-work',
          meta: {
            title: '工作情况',
          },
          component: MedicalWork,
        },
        {
          path: '/hardware',
          name: 'hardware',
          meta: {
            title: '硬件',
          },
          component: Hardware,
        },
        {
          path: '/customer',
          name: 'customer',
          meta: {
            title: '客户',
          },
          component: Customer,
        },
        {
          path: '/hospital',
          name: 'hospital',
          meta: {
            title: '医院',
            ignorePermission: true,
          },
          component: Hospital,
        },
        {
          path: '/department',
          name: 'department',
          meta: {
            title: '部门',
            ignorePermission: true,
          },
          component: Department,
        },
      ],
    },
    {
      path: '/:catchAll(.*)',
      redirect: '/login',
    },
  ],
});
router.beforeEach((to, from, next) => {
  const ignorePermission = to.meta.ignorePermission;
  if (ignorePermission) {
    next();
  }
  else {
    const roles: string[] = useLocalStorage(ROLES_KEY, []).value;
    if (roles.includes(MENU_ROLE_MAP[to.path])) {
      next();
    }
    else {
      next({ path: '/login' });
    }
  }
});

export default router;
