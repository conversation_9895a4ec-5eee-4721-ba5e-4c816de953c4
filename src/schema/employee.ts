import dayjs from 'dayjs';
import { z } from 'zod';
import { moneyDecrypt } from '@/utils';

export const SexSchema = z.enum([
  'FEMALE',
  'MALE',
  'UNKNOWN',
]);
export type Sex = z.infer<typeof SexSchema>;

export const JobStatusSchema = z.enum([
  'LEAVE_JOB',
  'ON_JOB',
  'WAIT_ENTRY',
]);
export type JobStatus = z.infer<typeof JobStatusSchema>;

/** 员工子状态 */
export const JobSubStatusSchema = z.enum([
  'NO_STATUS',
  'OFFICIAL',
  'PROBATION',
  'RESIGNED',
  'WAIT_FOR_LEAVE',
]);
export type JobSubStatus = z.infer<typeof JobSubStatusSchema>;
/** 员工能力 */
export const AbilityResultSchema = z.enum([
  'BACKBONE',
  'BUSINESS_CORE',
  'PERF_WEAK_QUA_OK',
  'QUA_CORE',
  'QUA_WEAK_ABI_OK',
  'SUPER_EMP',
  'UNDER_PERFORMER',
]);
export type AbilityResult = z.infer<typeof AbilityResultSchema>;
export const EmployeeInfoSchema = z.object({
  /** 员工姓名 */
  name: z.string().nullable(),
  /** 员工性别 */
  sex: SexSchema.nullable().transform((val) => {
    switch (val) {
      case 'FEMALE':
        return '女';
      case 'MALE':
        return '男';
      case 'UNKNOWN':
        return '未知';
      default:
        return '未知';
    }
  }),
  /** 员工年龄 */
  age: z.number().nullable(),
  /** 出生日期 */
  birthDate: z.number().nullable().transform(val => val ? dayjs(val).format('YYYY-MM-DD') : null),
  /** 手机号 */
  phone: z.string().nullable(),
  /** 邮箱 */
  email: z.email().nullable(),
  /** 银行卡号 */
  bankCardNo: z.string().nullable(),
  /** 银行名称 */
  bankName: z.string().nullable(),
  /** 职级 */
  positionLevel: z.string().nullable(),
  /** 入职日期 */
  workTime: z.number().nullable().transform(val => val ? dayjs(val).format('YYYY-MM-DD') : null),
  /** 工龄 */
  workYear: z.number().nullable(),
  /** 当前月薪 */
  currentSalary: z.union([z.string(), z.number()]).nullable().transform((val) => {
    const num = val ? Number(val) : 0;
    return Number.isNaN(num) ? moneyDecrypt(val ?? '') : num;
  }),
  /** 直接上级 */
  directSuperior: z.string().nullable(),
  /** 头像 */
  avatar: z.string().optional().nullable(),
  /** 能力评估结果 */
  abilityResult: AbilityResultSchema.optional().nullable(),
  /** 员工状态 */
  jobStatus: JobStatusSchema.optional().nullable(),
  /** 员工子状态 */
  jobSubStatus: JobSubStatusSchema.optional().nullable(),
  /** 用户账号角色 */
  userAccountRole: z.number().nullable(),
});

export const AbilityListSchema = z.object({
  /** 能力得分 */
  abilityScore: z.number(),
  /** 能力得分率 */
  abilityScoreRate: z.number(),
  /** 能力类型 */
  abilityType: z.string(),
});

/**
 * 员工业绩统计数据
 */
export const EmployeePerfStatSchema = z.object({
  /** 单日销冠奖（次数） */
  singleDaySalesCrownNum: z.number(),
  /** 月度冠军（次数） */
  monthlyChampionNum: z.number(),
  /** 月度亚军（次数） */
  monthlySecondPlaceNum: z.number(),
  /** 月度季军（次数） */
  monthlyThirdPlaceNum: z.number(),
  /** 非工作日开单奖 */
  nonWorkDayOrderAwardNum: z.number(),
  /** 退单率 */
  refundRate: z.number(),
  /** 订单数 */
  orderNum: z.number(),
  /** 平均达成率 */
  aveAchieveRate: z.number(),
});

/**
 * 条件查询员工业绩统计数据请求参数
 */
export const EmployeePerfStatQueryParamsSchema = z.object({
  /** 员工ID */
  osUserId: z.number(),
  /** 用户ID */
  userId: z.number(),
  /** 部门类型 */
  deptType: z.string(),
  /** 开始时间 */
  startTime: z.number().optional(),
  /** 结束时间 */
  endTime: z.number().optional(),
  /** 页码 */
  pageNumber: z.int().optional(),
  /** 每页条数 */
  pageSize: z.int().optional(),
  /** 汇总方式 */
  mergeMethod: z.string().optional(),
});

export const EmployeeOrderTrendItemSchema = z.object({
  /** 达成率 */
  achieveRate: z.number().nullable().transform(val => val ?? 0),
  /** 开单工作室数 */
  orderGroupNum: z.number().nullable().transform(val => val ?? 0),
  /** 有效订单 */
  orderPaidNum: z.number().nullable().transform(val => val ?? 0),
  /** 指标量 */
  quotaNum: z.number().nullable().transform(val => val ?? 0),
  /** 统计日期 */
  statDate: z.string(),
});

export const EmployeeOrderTrendSchema = z.array(EmployeeOrderTrendItemSchema);

/**
 * 员工负责工作室总计信息
 */
export const EmployeeGroupTotalInfoSchema = z.object({
  /** 总计年PCI手术量 */
  totalSurgicalVolume: z.number(),
  /** 总计医院数 */
  totalHospitalNum: z.number(),
});

/**
 * 员工负责工作室项
 */
export const EmployeeGroupItemSchema = z.object({
  /** 工作室名称 */
  groupName: z.string(),
  /** 医院名称 */
  hospitalName: z.string(),
  /** 年PCI手术量 */
  pciSurgicalVolume: z.number(),
  /** 在管会员数 */
  managePatientNum: z.number(),
  /** 工作室转化率 */
  groupTransformRate: z.number(),
  /** 会员续费率 */
  memberRenewRate: z.number(),
  /** 工作室退费率 */
  groupRefundRate: z.number(),
  /** 工作室状态 */
  groupStatus: z.string(),
});

/**
 * 员工负责工作室列表响应
 */
export const EmployeeGroupListResSchema = z.object({
  /** 工作室列表 */
  contents: z.array(EmployeeGroupItemSchema).nullable(),
  /** 总条数 */
  total: z.number(),
});

export type EmployeeInfo = z.infer<typeof EmployeeInfoSchema>;
export type AbilityList = z.infer<typeof AbilityListSchema>;
export type EmployeePerfStat = z.infer<typeof EmployeePerfStatSchema>;
export type EmployeePerfStatQueryParams = z.infer<typeof EmployeePerfStatQueryParamsSchema>;
/**
 * 员工投入明细项
 */
export const EmployeeInvestItemSchema = z.object({
  /** 投入类型 */
  investType: z.string(),
  /** 投入金额 */
  investAmount: z.union([z.string(), z.number()]).transform((val) => {
    const num = val ? Number(val) : 0;
    return Number.isNaN(num) ? moneyDecrypt(val) : num;
  }),
});

/**
 * 员工投产数据
 */
export const EmployeeInvestmentDataSchema = z.object({
  /** 投入明细 */
  investList: z.array(EmployeeInvestItemSchema),
  /** 产出订单量 */
  outputOrderNum: z.number(),
  /** 产出金额 */
  outputAmount: z.number(),
  /** 投入金额-费用报销 */
  totalFeeReimburse: z.number().nullable().transform(val => val ?? 0),
  /** 投入金额-薪资 */
  totalCopyAmount: z.union([z.string(), z.number()]).nullable().transform((val) => {
    const num = val ? Number(val) : 0;
    return Number.isNaN(num) ? moneyDecrypt(val ?? '') : num;
  }),
  /** 产出pci手术量 */
  pciSurgicalVolume: z.number().nullable().transform(val => val ?? 0),
});

/**
 * 员工产出趋势项
 */
export const EmployeeOutputTrendItemSchema = z.object({
  /** 统计日期 */
  key: z.string(),
  /** 订单量 */
  dataList: z.array(z.object({
    pciSurgicalVolume: z.number().nullable().transform(val => val ?? 0),
    orderNum: z.number().nullable().transform(val => val ?? 0),
    orderAmount: z.number().nullable().transform(val => val ?? 0),
  })).nullable().transform(val => val ?? [{ pciSurgicalVolume: 0, orderNum: 0, orderAmount: 0 }]),
  /** 订单金额 */
  mergeMethod: z.string(),
});

/**
 * 员工产出趋势
 */
export const EmployeeOutputTrendSchema = z.array(EmployeeOutputTrendItemSchema);

/**
 * 员工投入趋势项中的投入详情项
 */
export const EmployeeInvestTrendDetailItemSchema = z.object({
  /** 投入费用类型 */
  investType: z.string(),
  /** 投入金额 */
  investAmount: z.union([z.string(), z.number()]).transform((val) => {
    const num = val ? Number(val) : 0;
    return Number.isNaN(num) ? moneyDecrypt(val) : num;
  }),
});

/**
 * 员工投入趋势项
 */
export const EmployeeInvestTrendItemSchema = z.object({
  /** 统计日期 */
  key: z.string(),
  /** 投入详情 */
  dataList: z.array(EmployeeInvestTrendDetailItemSchema).nullable().transform(val => val ?? []),
  /** 总计 */
  mergeMethod: z.string(),
});

/**
 * 员工投入趋势
 */
export const EmployeeInvestTrendSchema = z.array(EmployeeInvestTrendItemSchema);

/**
 * 员工产出明细项
 */
export const EmployeeOutputDetailItemSchema = z.object({
  /** 产出类型 */
  outputType: z.string().nullable().optional(),
  /** 产出时间 */
  outputTime: z.string().nullable().optional().transform(val => val || null),
  /** 产出金额 */
  outputAmount: z.union([z.string(), z.number()]).nullable().optional().transform((val) => {
    const num = val ? Number(val) : 0;
    return Number.isNaN(num) ? moneyDecrypt(val ?? '') : num;
  }),
  /** 说明 */
  remark: z.string().nullable().optional(),
  /** 市场工作室ID */
  marketGroupId: z.number().nullable().optional(),
  /** 市场工作室名称 */
  marketGroupName: z.string().nullable().optional(),
  /** 工作室创建时间 */
  generateTime: z.union([z.string(), z.number()]).nullable().optional(),
  /** 工作室手术量 */
  operationNum: z.number().nullable().optional(),
});

/**
 * 员工产出明细列表响应
 */
export const EmployeeOutputDetailResSchema = z.object({
  /** 产出明细列表 */
  contents: z.array(EmployeeOutputDetailItemSchema).nullable().transform(val => val ?? []),
  /** 总条数 */
  total: z.number(),
});

/**
 * 员工投入明细项
 */
export const EmployeeInvestDetailItemSchema = z.object({
  /** 投入类型 */
  investType: z.string(),
  /** 投入时间 */
  investTime: z.number(),
  /** 投入金额 */
  investAmount: z.union([z.string(), z.number()]).transform((val) => {
    const num = val ? Number(val) : 0;
    return Number.isNaN(num) ? moneyDecrypt(val) : num;
  }),
  /** 说明 */
  remark: z.string().nullable(),
});

/**
 * 员工投入明细列表响应
 */
export const EmployeeInvestDetailResSchema = z.object({
  /** 投入明细列表 */
  contents: z.array(EmployeeInvestDetailItemSchema).nullable(),
  /** 总条数 */
  total: z.number(),
});

export type EmployeeGroupTotalInfo = z.infer<typeof EmployeeGroupTotalInfoSchema>;
export type EmployeeGroupItem = z.infer<typeof EmployeeGroupItemSchema>;
export type EmployeeGroupListRes = z.infer<typeof EmployeeGroupListResSchema>;
export type EmployeeInvestItem = z.infer<typeof EmployeeInvestItemSchema>;
export type EmployeeInvestmentData = z.infer<typeof EmployeeInvestmentDataSchema>;
export type EmployeeOutputTrendItem = z.infer<typeof EmployeeOutputTrendItemSchema>;
export type EmployeeOutputTrend = z.infer<typeof EmployeeOutputTrendSchema>;
export type EmployeeInvestTrendDetailItem = z.infer<typeof EmployeeInvestTrendDetailItemSchema>;
export type EmployeeInvestTrendItem = z.infer<typeof EmployeeInvestTrendItemSchema>;
export type EmployeeInvestTrend = z.infer<typeof EmployeeInvestTrendSchema>;
export type EmployeeOutputDetailItem = z.infer<typeof EmployeeOutputDetailItemSchema>;
export type EmployeeOutputDetailRes = z.infer<typeof EmployeeOutputDetailResSchema>;
export type EmployeeInvestDetailItem = z.infer<typeof EmployeeInvestDetailItemSchema>;
export type EmployeeInvestDetailRes = z.infer<typeof EmployeeInvestDetailResSchema>;
