import { z } from 'zod';
import { moneyDecrypt } from '@/utils';

/** 总看板请求参数 */
export const GeneralQueryParamsSchema = z.object({
  provinceName: z.string().optional(),
  statEndTime: z.number(),
  statStartTime: z.number(),
  /** 用户ID */
  osUserId: z.union([z.string(), z.number(), z.null()]).optional(),
});
export type GeneralQueryParams = z.infer<typeof GeneralQueryParamsSchema>;

export const DeptMarketGeneralSumStatDtoSchema = z.object({
  /** 手术量 */
  achievedNum: z.number(),
  /** 手术量占比 */
  achievedRate: z.number(),
  /** 部门名称 */
  departmentName: z.string(),
  /** 部门ID */
  deptId: z.number(),
  /** 投入金额——薪资 */
  investCopyAmount: z.union([z.string(), z.number(), z.null()]).transform(val => val ? moneyDecrypt(val) : 0),
  /** 投入金额——报销 */
  investFeeReimburse: z.union([z.string(), z.number(), z.null()]).transform(val => val ? Number(val) : 0),
  /** 市场指标数 */
  marketMetricsNum: z.number(),
  /** 手术量环比 */
  momSurgeryPercent: z.number(),
  /** 手术量同比 */
  yoySurgeryPercent: z.number(),
});
export type DeptMarketGeneralSumStatDto = z.infer<typeof DeptMarketGeneralSumStatDtoSchema>;

export const DeptMarketGeneralStatSchema = z.object({
  achievedRate: z.number(),
  coverRate: z.number(),
  marketGeneralStatList: z.array(DeptMarketGeneralSumStatDtoSchema).nullable(),
  newPurchaseOrderNum: z.number(),
  pciSurgeryCoverNum: z.number(),
  pciSurgeryTargetNum: z.number(),
  pciSurgeryTotalNum: z.number(),
  targetMarketRate: z.number(),
});
export type DeptMarketGeneralStat = z.infer<typeof DeptMarketGeneralStatSchema>;

export const DeptSellerGeneralSumStatDtoSchema = z.object({
  /** 部门名称 */
  departmentName: z.string(),
  /** 产出金额 */
  effectiveOrderAmount: z.number(),
  /** 投入工资 */
  salaryAmount: z.union([z.string(), z.number(), z.null()]).transform(val => val ? moneyDecrypt(val) : 0),
  /** 投入报销 */
  reimburseAmount: z.number(),
  /** 销售指标环比 */
  momQuotaPercent: z.number(),
  /** 指标完成量 */
  quotaCompleteNum: z.number(),
  /** 指标完成率 */
  quotaCompleteRate: z.number(),
  /** 销售指标 */
  sellerQuotaNum: z.number(),
  /** 销售指标同比 */
  yoyQuotaPercent: z.number(),
  /** 投产比 */
  productionRatio: z.number().nullable(),
});
export type DeptSellerGeneralSumStatDto = z.infer<typeof DeptSellerGeneralSumStatDtoSchema>;

/** 总看板销售达成响应 */
export const DeptSellerGeneralResSchema = z.object({
  deptSellers: z.array(DeptSellerGeneralSumStatDtoSchema).nullable(),
  /** 产出金额 */
  effectiveOrderAmount: z.number(),
  /** 投入工资 */
  salaryAmount: z.union([z.string(), z.number(), z.null()]).transform(val => val ? moneyDecrypt(val) : 0),
  /** 投入 报销 */
  reimburseAmount: z.number(),
  /** 销售指标环比 */
  momQuotaPercent: z.number(),
  /** 指标完成量 */
  quotaCompleteNum: z.number(),
  /** 指标完成率 */
  quotaCompleteRate: z.number(),
  /** 销售指标 */
  sellerQuotaNum: z.number(),
  /** 销售指标同比 */
  yoyQuotaPercent: z.number(),
  /** 投产比 */
  productionRatio: z.number().nullable(),
});
export type DeptSellerGeneralRes = z.infer<typeof DeptSellerGeneralResSchema>;

export const CustomerTypeGeneralStatDtoSchema = z.object({
  /** 客户转化价值类型 */
  transformValueType: z.string().describe('客户类型'),
  /** 客户类型数 */
  customerNum: z.number().describe('客户类型数'),
  /** 客户类型数占比 */
  customerPercent: z.number().describe('客户类型数占比'),
});
export type CustomerTypeGeneralStatDto = z.infer<typeof CustomerTypeGeneralStatDtoSchema>;

export const CustomerTypeGeneralResSchema = z.object({
  /** 客户总数 */
  customerTotalNum: z.number(),
  /** 客户转化价值列表 */
  transformValueList: z.array(CustomerTypeGeneralStatDtoSchema),
});
export type CustomerTypeGeneralRes = z.infer<typeof CustomerTypeGeneralResSchema>;

/** 总看板-产出情况、资金投入接口响应Schema */
export const GeneralOrderResSchema = z.object({
  /** 合计产出：有效订单金额总和 */
  effectiveOrderAmount: z.number(),
  /** 专家投入金额总和 */
  expertInvestAmount: z.number(),
  /** 专家劳务费金额 */
  expertServiceAmount: z.number(),
  /** 人力成本金额总和 */
  laborCostsAmount: z.union([z.string(), z.number(), z.null()]).transform(val => val ? moneyDecrypt(val) : 0),
  /** 新购订单金额总和 */
  newPurchaseOrderCount: z.number(),
  /** 续费订单金额总和 */
  renewalOrderCount: z.number(),
  /** 报销金额 */
  reimburseAmount: z.number(),
});

export type GeneralOrderRes = z.infer<typeof GeneralOrderResSchema>;

/** 总看板-业务覆盖、患者分布接口响应Schema */
export const GeneralHospitalResSchema = z.object({
  /** 医生工作室数量 */
  groupSize: z.number(),
  /** 合作医院数量 */
  hospitalSize: z.number(),
  /** 历史患者数量 */
  patientHistorySize: z.number(),
  /** 付费患者数量 */
  patientPaidSize: z.number(),
  /** 科研患者数量 */
  patientScientificSize: z.number(),
  /** 全部在管患者数 */
  patientSize: z.number(),
  /** 省份列表 */
  provinceList: z.array(z.string()),
  /** 省份数量 */
  provinceSize: z.number(),
  /** 地区数量 */
  regionSize: z.number(),
});

export type GeneralHospitalRes = z.infer<typeof GeneralHospitalResSchema>;

export const RenewalDateStatDtoSchema = z.object({
  /** 续费数 */
  renewalNum: z.number(),
  /** 续费率 */
  renewalRate: z.number(),
  /** 统计日期 */
  statDate: z.string(),
});
export type RenewalDateStatDto = z.infer<typeof RenewalDateStatDtoSchema>;

export const PatientDiagnosisGeneralStatDtoSchema = z.object({
  /** 主诊断 */
  diagnosis: z.string(),
  /** 患者数量 */
  patientNums: z.number(),
  /** 占比 */
  rate: z.number(),
});
export type PatientDiagnosisGeneralStatDto = z.infer<typeof PatientDiagnosisGeneralStatDtoSchema>;

export const GeneralPatientResSchema = z.object({
  /** 患者主诊断分布 */
  patientDiagnosisGeneralStatDTOS: z.array(PatientDiagnosisGeneralStatDtoSchema).nullable(),
  /** 按月份统计续费数/续费率 */
  renewalDateStatDTOS: z.array(RenewalDateStatDtoSchema).nullable(),
});
export type GeneralPatientRes = z.infer<typeof GeneralPatientResSchema>;
