import { z } from 'zod';

/**
 * 部门员工查询参数
 */
export const DeptEmployeeQueryParamsSchema = z.object({
  /** 部门id */
  deptId: z.number(),
  /** 是否仅看在职 */
  onlyWork: z.boolean().optional(),
  /** 页码 */
  pageNumber: z.number().optional(),
  /** 每页条数 */
  pageSize: z.number().optional(),
  /** 部门类型 */
  deptType: z.string(),
});
export type DeptEmployeeQueryParams = z.infer<typeof DeptEmployeeQueryParamsSchema>;

/**
 * 员工清单项
 */
export const DeptEmployeeItemSchema = z.object({
  /** osUserId */
  osUserId: z.number(),
  userId: z.number(),
  /** 员工姓名 */
  employeeName: z.string(),
  /** 岗位 */
  position: z.string(),
  /** 职级 */
  positionLevel: z.string(),
  /** 在岗时长 */
  workTime: z.number(),
  /** 所在部门 */
  deptNameList: z.array(z.string()),
  /** 员工状态 */
  employeeStatus: z.string(),
  /** 员工子状态 */
  employeeSubStatus: z.string(),
});
export type DeptEmployeeItem = z.infer<typeof DeptEmployeeItemSchema>;

/**
 * 员工清单响应
 */
export const DeptEmployeeListResSchema = z.object({
  /** 总条数 */
  total: z.number(),
  /** 员工列表 */
  contents: z.array(DeptEmployeeItemSchema).nullable(),
});
export type DeptEmployeeListRes = z.infer<typeof DeptEmployeeListResSchema>;

/**
 * 部门员工分布响应
 */
export const DeptEmployeeDistResSchema = z.object({
  /** 在职员工数 */
  workEmployeeNum: z.number(),
  /** 正式员工数 */
  formalEmployeeNum: z.number(),
  /** 正式员工占比 */
  formalEmployeeRate: z.number(),
  /** 员工转正率 */
  employeeCorrectionRate: z.number(),
  /** 员工离职率 */
  employeeLeaveRate: z.number(),
  /** PIP考核员工数 */
  pipEmployeeNum: z.number(),
  /** 在招岗位数 */
  recruitJobNum: z.number(),
});
export type DeptEmployeeDistRes = z.infer<typeof DeptEmployeeDistResSchema>;

/**
 * 部门职级占比项
 */
export const DeptPositionLevelItemSchema = z.object({
  /** 职位级别 */
  positionLevel: z.string(),
  /** 数量 */
  num: z.number(),
  /** 占比 */
  levelRate: z.number(),
});
export type DeptPositionLevelItem = z.infer<typeof DeptPositionLevelItemSchema>;

/**
 * 部门职级占比响应
 */
export const DeptPositionLevelResSchema = z.array(DeptPositionLevelItemSchema);
export type DeptPositionLevelRes = z.infer<typeof DeptPositionLevelResSchema>;

/**
 * DeptAbilityDistResSchema
 * 部门能力分布响应
 */
export const DeptAbilityDistResSchema = z.object({
  /** 失败者 */
  underPerformerNum: z.number(),
  /** 业务不佳表现尚可 */
  perfWeakQuaOkNum: z.number(),
  /** 表现不佳能力尚可 */
  quaWeakAbiOkNum: z.number(),
  /** 中坚力量 */
  backboneNum: z.number(),
  /** 业务核心骨干 */
  businessCoreNum: z.number(),
  /** 表现核心骨干 */
  quaCoreNum: z.number(),
  /** 超级员工 */
  superEmpNum: z.number(),
});
export type DeptAbilityDistRes = z.infer<typeof DeptAbilityDistResSchema>;

/**
 * 部门架构项
 */
export const DeptHierarchyItemSchema = z.object({
  /** 主键 */
  deptId: z.number(),
  /** 公司ID */
  companyId: z.number(),
  /** 父部门ID */
  parentId: z.number().nullable(),
  /** 部门名称 */
  name: z.string(),
  /** 部门类型 */
  deptType: z.string(),
  /** 部门状态 */
  status: z.string().nullable().optional(),
  /** 部门员工数 */
  deptUserNum: z.number(),
});
export type DeptHierarchyItem = z.infer<typeof DeptHierarchyItemSchema>;

/**
 * 部门架构响应
 */
export const DeptHierarchyResSchema = z.array(DeptHierarchyItemSchema);
export type DeptHierarchyRes = z.infer<typeof DeptHierarchyResSchema>;
