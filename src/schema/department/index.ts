import { z } from 'zod';

export * from './employee';
export * from './investment';
export * from './performance';

export const DepartmentQueryParamsSchema = z.object({
  deptId: z.number(),
  endTime: z.number().optional(),
  startTime: z.union([z.number(), z.null()]).optional(),
});
export type DepartmentQueryParams = z.infer<typeof DepartmentQueryParamsSchema>;

export const DepartmentPerformanceSchema = z.object({
  /** 达成率 */
  achieveRate: z.number(),
  /** 有效订单量 */
  orderPaidNum: z.number(),
  /** 开单工作室数量 */
  paidGroupNum: z.number(),
  /** 指标量 */
  quotaNum: z.number(),
  /** 统计日期 */
  statDate: z.string(),
  /** 手术量 */
  surgeryNum: z.number(),
});

export const DepartmentPerformanceTrendSchema = z.array(DepartmentPerformanceSchema);

export type DepartmentPerformanceTrend = z.infer<typeof DepartmentPerformanceTrendSchema>;

export type DepartmentPerformance = z.infer<typeof DepartmentPerformanceSchema>;
