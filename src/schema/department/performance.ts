import { z } from 'zod';

/**
 * 部门绩效查询参数
 */
export const DeptPerformanceQueryParamsSchema = z.object({
  /** 部门id */
  deptId: z.number(),
  /** 开始时间 */
  startTime: z.number().optional(),
  /** 结束时间 */
  endTime: z.number().optional(),
  /** 部门类型 */
  deptType: z.string().optional(),
});
export type DeptPerformanceQueryParams = z.infer<typeof DeptPerformanceQueryParamsSchema>;

/**
 * 部门业绩走势项
 */
export const DeptPerformanceTrendItemSchema = z.object({
  /** 统计日期 */
  statDate: z.string(),
  /** 指标量 */
  quotaNum: z.number(),
  /** 有效订单数 */
  orderPaidNum: z.number().nullable().transform(val => val ?? 0),
  /** 开单工作室数 */
  paidGroupNum: z.number().nullable().transform(val => val ?? 0),
  /** 开发手术量（市场） */
  surgeryNum: z.number().nullable().transform(val => val ?? 0),
  /** 达成率 */
  achieveRate: z.number(),
});
export type DeptPerformanceTrendItem = z.infer<typeof DeptPerformanceTrendItemSchema>;

/**
 * 部门业绩走势
 */
export const DeptPerformanceTrendSchema = z.array(DeptPerformanceTrendItemSchema);
export type DeptPerformanceTrend = z.infer<typeof DeptPerformanceTrendSchema>;

/**
 * 绩效评分走势项
 */
export const DeptPerfScoreTrendItemSchema = z.object({
  /** 统计日期 */
  month: z.number(),
  /** 绩效人数 */
  staffNum: z.number(),
  /** 平均绩效分数 */
  avgScore: z.number(),
  /** 平均绩点 */
  avgCoefficient: z.number(),
});
export type DeptPerfScoreTrendItem = z.infer<typeof DeptPerfScoreTrendItemSchema>;

/**
 * 绩效评分走势
 */
export const DeptPerfScoreTrendSchema = z.array(DeptPerfScoreTrendItemSchema);
export type DeptPerfScoreTrend = z.infer<typeof DeptPerfScoreTrendSchema>;

/**
 * 主观能动性得分走势项
 */
export const DeptSubjScoreTrendItemSchema = z.object({
  /** 统计日期 */
  month: z.number(),
  /** 分数 */
  avgScore: z.number(),
  /** 人数 */
  staffNum: z.number(),
});
export type DeptSubjScoreTrendItem = z.infer<typeof DeptSubjScoreTrendItemSchema>;

/**
 * 主观能动性得分走势
 */
export const DeptSubjScoreTrendSchema = z.array(DeptSubjScoreTrendItemSchema);
export type DeptSubjScoreTrend = z.infer<typeof DeptSubjScoreTrendSchema>;

/**
 * 考核数据项
 */
export const ExamDataItemSchema = z.object({
  /** 考核类型 */
  examType: z.string(),
  /** 参与考核人数 */
  examEmployeeNum: z.number(),
  /** 日常成绩 */
  dailyScore: z.number(),
  /** 培训时长（分） */
  trainMinuteNum: z.number(),
});
export type ExamDataItem = z.infer<typeof ExamDataItemSchema>;

/**
 * 考核成绩走势项
 */
export const DeptExamScoreTrendItemSchema = z.object({
  /** 统计日期 */
  month: z.number(),
  /** 参与考核人数 */
  staffNum: z.number(),
  /** 培训平均分 */
  avgScore: z.number(),
  /** 大练兵平均分 */
  avgScoreLarge: z.number(),
  /** 其他培训平均分 */
  avgScoreOther: z.number(),
  /** 培训时长（分） */
  duration: z.number(),
});
export type DeptExamScoreTrendItem = z.infer<typeof DeptExamScoreTrendItemSchema>;

/**
 * 考核成绩走势
 */
export const DeptExamScoreTrendSchema = z.array(DeptExamScoreTrendItemSchema);
export type DeptExamScoreTrend = z.infer<typeof DeptExamScoreTrendSchema>;
