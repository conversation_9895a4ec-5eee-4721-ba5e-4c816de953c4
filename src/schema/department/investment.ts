import { z } from 'zod';
import { moneyDecrypt } from '@/utils';

/**
 * 部门投产查询参数
 */
export const DeptInvestmentQueryParamsSchema = z.object({
  /** 部门id */
  deptId: z.number(),
  /** 开始时间 */
  startTime: z.number().optional(),
  /** 结束时间 */
  endTime: z.number().optional(),
  /** 合并方式（按日、按周、按月） */
  mergeMethod: z.string().optional(),
  deptType: z.string().optional(),
});
export type DeptInvestmentQueryParams = z.infer<typeof DeptInvestmentQueryParamsSchema>;

/**
 * 投入详情项
 */
export const InvestmentItemSchema = z.object({
  /** 投入类型 */
  investType: z.string(),
  /** 投入金额 */
  investAmount: z.union([z.string(), z.number()]).transform((val) => {
    const num = val ? Number(val) : 0;
    return Number.isNaN(num) ? moneyDecrypt(val) : num;
  }),
  /** 投入占比 */
  investRate: z.number().optional(),
});
export type InvestmentItem = z.infer<typeof InvestmentItemSchema>;

/**
 * 部门投产总览数据
 */
export const DeptInvestmentOverviewSchema = z.object({
  /** 投入金额-费用报销 */
  totalFeeReimburse: z.number().nullable().transform(val => val ?? 0),
  /** 投入金额-薪资 */
  totalCopyAmount: z.string().nullable().transform(val => val ? moneyDecrypt(val) : 0),
  /** 投入详情 */
  investList: z.array(InvestmentItemSchema),
  /** 产出订单量 */
  totalOrderNum: z.number().nullable().transform(val => val ?? 0),
  /** 产出金额 */
  totalOutputAmount: z.number().nullable().transform(val => val ?? 0),
  /** 产出PCI手术量 */
  pciSurgicalVolume: z.number().nullable().transform(val => val ?? 0),
  /** 千台手术成本 */
  surgicalCostVolume: z.number().nullable().optional().transform(val => val ?? 0),
});
export type DeptInvestmentOverview = z.infer<typeof DeptInvestmentOverviewSchema>;

/**
 * 投入趋势项中的投入详情项
 */
export const InvestmentTrendDetailItemSchema = z.object({
  /** 投入费用类型 */
  investType: z.string(),
  /** 投入金额 */
  investAmount: z.union([z.string(), z.number()]).transform((val) => {
    if (val) {
      const num = Number(val);
      return Number.isNaN(num) ? moneyDecrypt(val) : num;
    }
    else {
      return 0;
    }
  }),
});
export type InvestmentTrendDetailItem = z.infer<typeof InvestmentTrendDetailItemSchema>;

/**
 * 投入趋势项
 */
export const InvestmentTrendItemSchema = z.object({
  /** 统计日期 */
  key: z.string(),
  mergeMethod: z.string(),
  /** 投入详情 */
  dataList: z.array(InvestmentTrendDetailItemSchema).nullable(),
});
export type InvestmentTrendItem = z.infer<typeof InvestmentTrendItemSchema>;

/**
 * 部门投入趋势
 */
export const DeptInvestmentTrendSchema = z.array(InvestmentTrendItemSchema);
export type DeptInvestmentTrend = z.infer<typeof DeptInvestmentTrendSchema>;

/**
 * 产出趋势项
 */
export const OutputTrendItemSchema = z.object({
  /** 统计日期 */
  key: z.string(),
  mergeMethod: z.string(),
  /** 数据列表 */
  dataList: z.array(z.object({
    /** 订单量 */
    orderNum: z.number().nullable().transform(val => val ?? 0),
    /** 订单金额 */
    orderAmount: z.number().nullable().transform(val => val ?? 0),
    pciSurgicalVolume: z.number().nullable().transform(val => val ?? 0),
  })).nullable().transform(val => val ?? [{
    orderNum: 0,
    orderAmount: 0,
    pciSurgicalVolume: 0,
  }]),
});
export type OutputTrendItem = z.infer<typeof OutputTrendItemSchema>;

/**
 * 部门产出趋势
 */
export const DeptOutputTrendSchema = z.array(OutputTrendItemSchema);
export type DeptOutputTrend = z.infer<typeof DeptOutputTrendSchema>;
