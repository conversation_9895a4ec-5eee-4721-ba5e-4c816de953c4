import { z } from 'zod';

export const NotJobUserSchema = z.object({
  academicPost: z.union([z.null(), z.string()]).optional(),
  ageChildren: z.union([z.number(), z.null()]).optional(),
  ageSpouse: z.union([z.number(), z.null()]).optional(),
  bankNo: z.union([z.null(), z.string()]).optional(),
  birthday: z.union([z.number(), z.null()]).optional(),
  briefIntroduction: z.union([z.null(), z.string()]).optional(),
  curriculum: z.union([z.null(), z.string()]).optional(),
  gender: z.union([z.null(), z.string()]).optional(),
  hobby: z.union([z.null(), z.string()]).optional(),
  hobbyChildren: z.union([z.null(), z.string()]).optional(),
  hobbySpouse: z.union([z.null(), z.string()]).optional(),
  id: z.union([z.number(), z.null()]).optional(),
  idCard: z.union([z.null(), z.string()]).optional(),
  isKey: z.union([z.null(), z.string()]).optional(),
  jobSpouse: z.union([z.null(), z.string()]).optional(),
  jobTitle: z.union([z.null(), z.string()]).optional(),
  location: z.union([z.null(), z.string()]).optional(),
  major: z.union([z.null(), z.string()]).optional(),
  name: z.union([z.null(), z.string()]).optional(),
  nameChildren: z.union([z.null(), z.string()]).optional(),
  nameSpouse: z.union([z.null(), z.string()]).optional(),
  openingBank: z.union([z.null(), z.string()]).optional(),
  payPerceive: z.union([z.null(), z.string()]).optional(),
  phone: z.union([z.null(), z.string()]).optional(),
  pushType: z.union([z.null(), z.string()]).optional(),
  school: z.union([z.null(), z.string()]).optional(),
  schoolChildren: z.union([z.null(), z.string()]).optional(),
  scientificPerceive: z.union([z.null(), z.string()]).optional(),
  wxNo: z.union([z.null(), z.string()]).optional(),
});
export type NotJobUser = z.infer<typeof NotJobUserSchema>;

export const DoctorUserSchema = z.object({
  doctorId: z.union([z.number(), z.null()]).optional(),
  doctorName: z.union([z.null(), z.string()]).optional(),
  isKey: z.union([z.null(), z.string()]).optional(),
  pushType: z.union([z.array(z.string()), z.null()]).optional(),
  rightSpeak: z.union([z.null(), z.string()]).optional(),
});
export type DoctorUser = z.infer<typeof DoctorUserSchema>;

export const PositionSchema = z.object({
  deptType: z.union([z.null(), z.string()]).optional(),
  doctorUser: z.union([DoctorUserSchema, z.null()]).optional(),
  name: z.union([z.null(), z.string()]).optional(),
  positionId: z.union([z.number(), z.null()]).optional(),
  type: z.union([z.null(), z.string()]).optional(),
});
export type Position = z.infer<typeof PositionSchema>;

export const FrameworkSchema = z.object({
  bedNum: z.union([z.number(), z.null()]).optional(),
  deptName: z.union([z.null(), z.string()]).optional(),
  deptType: z.union([z.null(), z.string()]).optional(),
  handOver: z.union([z.null(), z.string()]).optional(),
  operationNum: z.union([z.number(), z.null()]).optional(),
  parentId: z.union([z.number(), z.null()]).optional(),
  position: z.union([z.array(PositionSchema), z.null()]).optional(),
  rid: z.union([z.number(), z.null()]).optional(),
  status: z.union([z.null(), z.string()]).optional(),
});
export type Framework = z.infer<typeof FrameworkSchema>;

export const HospitalHierarchySchema = z.object({
  framework: z.union([z.array(FrameworkSchema), z.null()]).optional(),
  notJobUsers: z.union([z.array(NotJobUserSchema), z.null()]).optional(),
});
export type HospitalHierarchy = z.infer<typeof HospitalHierarchySchema>;
