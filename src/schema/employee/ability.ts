import { z } from 'zod';

// 员工能力评估详情
export const AbilityListItemSchema = z.object({
  /** 能力得分 */
  abilityScore: z.number().nullable().transform(val => val ?? 0),
  /** 能力得分率 */
  abilityScoreRate: z.number().nullable().transform(val => val ?? 0),
  /** 能力类型 */
  abilityType: z.string(),
});
export type AbilityListItem = z.infer<typeof AbilityListItemSchema>;

// 员工当前能力
export const EmployeeAbilitySchema = z.object({
  /** 能力列表 */
  abilityList: z.array(AbilityListItemSchema),
  /** 能力评估结果 */
  abilityResult: z.string(),
  /** 能力得分 */
  abilityScore: z.number().nullable().transform(val => val ?? 0),
});
export type EmployeeAbilityData = z.infer<typeof EmployeeAbilitySchema>;

// 员工能力得分趋势项
export const AbilityTrendItemSchema = z.object({
  /** 能力列表 */
  abilityList: z.array(AbilityListItemSchema),
  /** 能力得分 */
  abilityScore: z.number(),
  /** 统计日期 */
  statDate: z.string(),
});
export type AbilityTrendItem = z.infer<typeof AbilityTrendItemSchema>;

// 员工能力得分趋势
export const EmployeeAbilityTrendSchema = z.array(AbilityTrendItemSchema);
export type EmployeeAbilityTrend = z.infer<typeof EmployeeAbilityTrendSchema>;

// 业务达成能力相关

// 业务达成趋势项
export const BusinessAbilityTrendItemSchema = z.object({
  /** 指标 */
  quota: z.number(),
  /** 达成 */
  achieve: z.number(),
  /** 达标率 */
  achieveRate: z.number(),
  /** 退费率 */
  refundRate: z.number(),
  /** 得分 */
  score: z.number(),
  /** 统计日期 */
  statDate: z.string(),
  /** 手术量指标 */
  operatorNumberQuota: z.number().nullable().optional(),
  /** 手术量 */
  operatorNumber: z.number().nullable().optional(),
  /** 手术量达标率 */
  operatorNumberRate: z.number().nullable().optional(),
  /** 月均开发手术量数 */
  operatorNumberMonthlyAverage: z.number().nullable().optional(),
});
export type BusinessAbilityTrendItem = z.infer<typeof BusinessAbilityTrendItemSchema>;

// 业务达成趋势
export const BusinessAbilityTrendDataSchema = z.array(BusinessAbilityTrendItemSchema);
export type BusinessAbilityTrendData = z.infer<typeof BusinessAbilityTrendDataSchema>;

// 业务达成明细项
export const BusinessAbilityDetailItemSchema = z.object({
  /** 统计日期 */
  statDate: z.string(),
  /** 当月指标 */
  quota: z.number(),
  /** 达成数 */
  achieve: z.number(),
  /** 达成率 */
  achieveRate: z.number(),
  /** 退费率 */
  refundRate: z.number(),
  /** 手术量指标 */
  operatorNumberQuota: z.number().nullable().optional(),
  /** 手术量 */
  operatorNumber: z.number().nullable().optional(),
  /** 手术量达标率 */
  operatorNumberRate: z.number().nullable().optional(),
  /** 月均开发手术量数 */
  operatorNumberMonthlyAverage: z.number().nullable().optional(),
});
export type BusinessAbilityDetailItem = z.infer<typeof BusinessAbilityDetailItemSchema>;

// 业务达成明细
export const BusinessAbilityDetailDataSchema = z.object({
  /** 明细列表 */
  contents: z.array(BusinessAbilityDetailItemSchema).nullable(),
  /** 总条数 */
  total: z.number(),
});
export type BusinessAbilityDetailData = z.infer<typeof BusinessAbilityDetailDataSchema>;

// 专业能力相关

// 专业能力趋势项
export const ProfessionalAbilityTrendItemSchema = z.object({
  /** 统计日期 */
  statDate: z.string(),
  /** 手术量 */
  operationNum: z.number(),
  /** 达成 */
  achieve: z.number(),
  /** 手术转化率 */
  operationTransformRate: z.number(),
  /** 得分 */
  score: z.number(),
  /** 出单工作室运转率 */
  orderGroupRate: z.number().nullable().optional(),
  /** 出单工作室数量 */
  orderGroupCount: z.number().nullable().optional(),
  /** 管辖区域工作室数量 */
  allGroupCount: z.number().nullable().optional(),
});
export type ProfessionalAbilityTrendItem = z.infer<typeof ProfessionalAbilityTrendItemSchema>;

// 专业能力趋势
export const ProfessionalAbilityTrendDataSchema = z.array(ProfessionalAbilityTrendItemSchema);
export type ProfessionalAbilityTrendData = z.infer<typeof ProfessionalAbilityTrendDataSchema>;

// 专业能力明细项
export const ProfessionalAbilityDetailItemSchema = z.object({
  /** 统计日期 */
  statDate: z.string(),
  /** 当月手术量 */
  operationNum: z.number(),
  /** 达成数 */
  achieve: z.number(),
  /** 手术转化率 */
  operationTransformRate: z.number(),
  /** 出单工作室运转率 */
  orderGroupRate: z.number().nullable().optional(),
  /** 出单工作室数量 */
  orderGroupCount: z.number().nullable().optional(),
  /** 管辖区域工作室数量 */
  allGroupCount: z.number().nullable().optional(),
});
export type ProfessionalAbilityDetailItem = z.infer<typeof ProfessionalAbilityDetailItemSchema>;

// 专业能力明细
export const ProfessionalAbilityDetailDataSchema = z.object({
  /** 明细列表 */
  contents: z.array(ProfessionalAbilityDetailItemSchema).nullable(),
  /** 总条数 */
  total: z.number(),
});
export type ProfessionalAbilityDetailData = z.infer<typeof ProfessionalAbilityDetailDataSchema>;

// 主观能动性相关

// 主观能动性趋势项
export const SubjectiveInitiativeTrendItemSchema = z.object({
  /** 统计日期 */
  statDate: z.string(),
  /** 得分 */
  score: z.number(),
});
export type SubjectiveInitiativeTrendItem = z.infer<typeof SubjectiveInitiativeTrendItemSchema>;

// 主观能动性趋势
export const SubjectiveInitiativeTrendDataSchema = z.array(SubjectiveInitiativeTrendItemSchema);
export type SubjectiveInitiativeTrendData = z.infer<typeof SubjectiveInitiativeTrendDataSchema>;

// 主观能动性明细项
export const SubjectiveInitiativeDetailItemSchema = z.object({
  /** 日期 */
  statDate: z.string(),
  /** 统计方式 */
  statType: z.string(),
  /** 在岗时长 */
  onDutyDuration: z.number(),
  /** 说明 */
  remark: z.string(),
});
export type SubjectiveInitiativeDetailItem = z.infer<typeof SubjectiveInitiativeDetailItemSchema>;

// 主观能动性明细
export const SubjectiveInitiativeDetailDataSchema = z.object({
  /** 明细列表 */
  contents: z.array(SubjectiveInitiativeDetailItemSchema).nullable(),
  /** 总条数 */
  total: z.number(),
});
export type SubjectiveInitiativeDetailData = z.infer<typeof SubjectiveInitiativeDetailDataSchema>;

// 绩效考核相关

// 绩效考核趋势项
export const PerformanceEvaluationTrendItemSchema = z.object({
  /** 统计日期 */
  statDate: z.string(),
  /** 绩效分 */
  score: z.number(),
});
export type PerformanceEvaluationTrendItem = z.infer<typeof PerformanceEvaluationTrendItemSchema>;

// 绩效考核趋势
export const PerformanceEvaluationTrendDataSchema = z.array(PerformanceEvaluationTrendItemSchema);
export type PerformanceEvaluationTrendData = z.infer<typeof PerformanceEvaluationTrendDataSchema>;

// 绩效考核明细项
export const PerformanceEvaluationDetailItemSchema = z.object({
  /** 统计日期 */
  statDate: z.string(),
  /** 最终评分 */
  score: z.number(),
  /** 系数 */
  coefficient: z.number().nullable().optional(),
  /** 说明 */
  remark: z.string().nullable().optional(),
});
export type PerformanceEvaluationDetailItem = z.infer<typeof PerformanceEvaluationDetailItemSchema>;

// 绩效考核明细
export const PerformanceEvaluationDetailDataSchema = z.object({
  /** 明细列表 */
  contents: z.array(PerformanceEvaluationDetailItemSchema).nullable(),
  /** 总条数 */
  total: z.number(),
});
export type PerformanceEvaluationDetailData = z.infer<typeof PerformanceEvaluationDetailDataSchema>;

// 发展潜力相关

// 发展潜力趋势项
export const DevelopmentPotentialTrendItemSchema = z.object({
  /** 统计日期 */
  statDate: z.string(),
  /** 培训考试次数 */
  dataList: z.array(z.object({
    /** 培训类型 */
    examType: z.string(),
    /** 培训成绩 */
    score: z.number(),
  })),
  /** 专业性评分 */
  aveScore: z.number(),
  /** 培训时长（分） */
  trainMinuteNum: z.number(),
});
export type DevelopmentPotentialTrendItem = z.infer<typeof DevelopmentPotentialTrendItemSchema>;

// 发展潜力趋势
export const DevelopmentPotentialTrendDataSchema = z.array(DevelopmentPotentialTrendItemSchema);
export type DevelopmentPotentialTrendData = z.infer<typeof DevelopmentPotentialTrendDataSchema>;

// 发展潜力明细项（培训考试明细）
export const DevelopmentPotentialDetailItemSchema = z.object({
  /** 培训考试名称 */
  trainExamName: z.string().nullable(),
  /** 开始时间 */
  startTime: z.union([z.string(), z.number()]).nullable(),
  /** 结束时间 */
  endTime: z.union([z.string(), z.number()]).nullable(),
  /** 培训时长 */
  trainMinuteNum: z.number().nullable(),
  /** 是否考试 */
  isExam: z.boolean().nullable(),
  /** 考试成绩 */
  examScore: z.number().nullable(),
  /** 说明 */
  remark: z.string().nullable(),
});
export type DevelopmentPotentialDetailItem = z.infer<typeof DevelopmentPotentialDetailItemSchema>;

// 发展潜力明细
export const DevelopmentPotentialDetailDataSchema = z.object({
  /** 明细列表 */
  contents: z.array(DevelopmentPotentialDetailItemSchema).nullable(),
  /** 总条数 */
  total: z.number(),
});
export type DevelopmentPotentialDetailData = z.infer<typeof DevelopmentPotentialDetailDataSchema>;
