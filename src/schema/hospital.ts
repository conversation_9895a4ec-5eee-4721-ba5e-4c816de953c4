import { z } from 'zod';

export * from './hospital/hierarchy';

export const QueryMarketListSchema = z.object({
  endTime: z.union([z.number(), z.null()]).optional(),
  hospitalId: z.number().nullable(),
  pageNumber: z.union([z.number(), z.null()]).optional(),
  pageSize: z.union([z.number(), z.null()]).optional(),
  startTime: z.union([z.number(), z.null()]).optional(),
  marketHospitalId: z.union([z.number(), z.null()]).optional(),
});

/**
 * 医院市场人员
 */
export const HospitalMarketPeopleSchema = z.object({
  /** 完成手术量 */
  completeSurgicalVolume: z.number().optional(),
  /** 市场ID */
  osUserId: z.number(),
  /** 市场名称 */
  marketName: z.string().optional(),
  /** 岗位名称 */
  postName: z.string().optional(),
  /** 完成率 */
  quotaCompleteRate: z.number().optional(),
  /** 同比 */
  surgicalCompareRate: z.number().optional(),
  /** 环比 */
  surgicalRingRate: z.number().optional(),
  /** 手术量 */
  surgicalVolume: z.number().optional(),
  userName: z.string().optional(),
  userId: z.number().optional().nullable(),
  deptId: z.number().optional().nullable(),
  deptType: z.string().optional().nullable(),
});

/**
 * 医院市场人员响应
 */
export const HospitalMarketPeopleResSchema = z.object({
  /** 医院市场人员 */
  contents: z.array(HospitalMarketPeopleSchema).nullable(),
  /** 总条数 */
  total: z.number(),
});

export type HospitalMarketPeople = z.infer<typeof HospitalMarketPeopleSchema>;
export type HospitalMarketPeopleRes = z.infer<typeof HospitalMarketPeopleResSchema>;
export type QueryMarketList = z.infer<typeof QueryMarketListSchema>;

export const HospitalSchema = z.object({
  /** 城市名称 */
  cityName: z.string().nullable(),
  /** 合作状态 */
  cooperationStage: z.string(),
  /** 医院名称 */
  hospitalName: z.string().nullable(),
  /** PCI手术量 */
  pciSurgicalVolume: z
    .number()
    .nullable()
    .transform(val => val || 0),
  /** 省份名称 */
  provinceName: z.string().nullable(),
  /** 医院ID */
  hospitalId: z.number().nullable(),
  /** 市场医院🆔 */
  marketHospitalId: z.number(),
});
export type Hospital = z.infer<typeof HospitalSchema>;

export const HospitalListResSchema = z.object({
  contents: z.array(HospitalSchema).nullable(),
  /** 医院总数量 */
  total: z.number(),
});
export type HospitalListRes = z.infer<typeof HospitalListResSchema>;

export const HospitalTotalPCISchema = z.int();

export const HospitalQueryParamsSchema = z.object({
  /** 医院ID */
  hospitalId: z.number().nullable(),
  /** 页码 */
  pageNumber: z.number().optional(),
  /** 分页大小 */
  pageSize: z.number().optional(),
  /** 开始时间 */
  startTime: z.number().optional(),
  /** 结束时间 */
  endTime: z.number().optional(),
  /** 市场医院🆔 */
  marketHospitalId: z.number().optional(),
});
export type HospitalQueryParams = z.infer<typeof HospitalQueryParamsSchema>;

export const HospitalGroupSchema = z.object({
  /** 工作室组长 */
  groupLeaderName: z.string(),
  /** 工作室成员名称列表 */
  groupMemberNameList: z.array(z.string()),
  /** 工作室名称 */
  groupName: z.string(),
  /** 工作室退费率 */
  groupRefundRate: z.number(),
  /** 工作室状态 */
  groupStatus: z.string(),
  /** 工作室转换率 */
  groupTransformRate: z.number(),
  /** 工作室在管会员数 */
  managePatientNum: z.number(),
  /** 会员续费率 */
  patientRenewRate: z.number(),
  /** 年PCI手术量 */
  pciSurgicalVolume: z.number(),
});
export type HospitalGroup = z.infer<typeof HospitalGroupSchema>;

export const HospitalGroupListResSchema = z.object({
  contents: z.array(HospitalGroupSchema).nullable(),
  /** 总条数 */
  total: z.number(),
});
export type HospitalGroupListRes = z.infer<typeof HospitalGroupListResSchema>;

/**
 * 投入详情项
 */
export const MarketInvestmentItemSchema = z.object({
  /** 投入类型 */
  investType: z.string(),
  /** 投入金额 */
  investAmount: z.number(),
  /** 投入占比 */
  investRate: z.number(),
});

/**
 * 市场投产数据
 */
export const MarketInvestmentDataSchema = z.object({
  /** 投入总金额 */
  totalInvestAmount: z.number(),
  /** 投入详情列表 */
  investList: z.array(MarketInvestmentItemSchema),
  /** 产出PCI手术量 */
  pciSurgicalVolume: z.number(),
  /** 千台手术成本 */
  surgicalCostVolume: z.number(),
});

export type MarketInvestmentItem = z.infer<typeof MarketInvestmentItemSchema>;
export type MarketInvestmentData = z.infer<typeof MarketInvestmentDataSchema>;

/**
 * 销售投产数据
 */
export const SellerInvestmentDataSchema = z.object({
  /** 投入总金额 */
  totalInvestAmount: z.number(),
  /** 投入详情列表 */
  investList: z.array(MarketInvestmentItemSchema),
  /** 产出订单量 */
  orderNum: z.number(),
  /** 产出金额 */
  orderAmount: z.number(),
  /** 部门投产比 */
  invOutputRate: z.number(),
});

export type SellerInvestmentData = z.infer<typeof SellerInvestmentDataSchema>;

/**
 * 科研投产数据
 */
export const ProjectInvestmentDataSchema = z.object({
  /** 投入总金额 */
  totalInvestAmount: z.number(),
  /** 投入详情列表 */
  investList: z.array(MarketInvestmentItemSchema),
  /** 预期产出 */
  expectOutput: z.number(),
  /** 实际产出 */
  actualOutput: z.number(),
  /** 科研投产比 */
  invOutputRate: z.number(),
});

export type ProjectInvestmentData = z.infer<typeof ProjectInvestmentDataSchema>;

/**
 * 市场开发业绩数据
 */
export const MarketPerformanceDataSchema = z.object({
  /** 市场手术量开发指标 */
  surgicalVolume: z.number(),
  /** 完成开发手术量 */
  completeSurgicalVolume: z.number(),
  /** 同比 */
  compareRate: z.number(),
  /** 环比 */
  ringRate: z.number(),
  /** 市场开发指标完成率 */
  quotaCompleteRate: z.number(),
});

export type MarketPerformanceData = z.infer<typeof MarketPerformanceDataSchema>;

/**
 * 销售达成业绩数据
 */
export const SellerPerformanceDataSchema = z.object({
  /** 销售医院手术量 */
  surgicalVolume: z.number(),
  /** 完成订单量 */
  completeOrderNum: z.number(),
  /** 同比 */
  compareRate: z.number(),
  /** 环比 */
  ringRate: z.number(),
  /** 手术转化率 */
  surgicalTransformRate: z.number(),
});

export type SellerPerformanceData = z.infer<typeof SellerPerformanceDataSchema>;

/**
 * 条件查询业绩数据请求参数
 */
export const PerformanceQueryParamsSchema = z.object({
  /** 医院ID */
  hospitalId: z.number().nullable(),
  /** 开始时间 */
  startTime: z.number().optional(),
  /** 结束时间 */
  endTime: z.number().optional(),
  marketHospitalId: z.number(),
});

export type PerformanceQueryParams = z.infer<typeof PerformanceQueryParamsSchema>;

/**
 * 销售人员
 */
export const HospitalSellerPeopleSchema = z.object({
  /** 销售ID */
  userId: z.number(),
  /** 销售人员名称 */
  userName: z.string(),
  /** 岗位名称 */
  postName: z.string(),
  /** 销售指标 */
  quotaNum: z.number(),
  /** 完成订单量 */
  completeOrderVolume: z.number(),
  /** 完成订单量同比 */
  orderCompareRate: z.number(),
  /** 完成订单量环比 */
  orderRingRate: z.number(),
  /** 销售指标完成率 */
  quotaCompleteRate: z.number(),
  osUserId: z.number().nullable(),
  deptId: z.number().nullable(),
  deptName: z.string().nullable(),
  deptType: z.string().nullable(),
});

/**
 * 销售人员列表响应
 */
export const HospitalSellerPeopleResSchema = z.object({
  /** 销售人员列表 */
  contents: z.array(HospitalSellerPeopleSchema).nullable(),
  /** 总条数 */
  total: z.number(),
});

export type HospitalSellerPeople = z.infer<typeof HospitalSellerPeopleSchema>;
export type HospitalSellerPeopleRes = z.infer<typeof HospitalSellerPeopleResSchema>;
