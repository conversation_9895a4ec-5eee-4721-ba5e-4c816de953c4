<script setup lang="ts">
import { useLocalStorage } from '@vueuse/core';
import { ElPopover } from 'element-plus';
import { ref } from 'vue';
import changePassword from '@/assets/imgs/change-password.png';
import headerLogo from '@/assets/imgs/header_logo.png';
import logout from '@/assets/imgs/login/logout.png';
import userAvatar from '@/assets/imgs/user-avatar.png';
import { ChangePasswordDialog } from '@/components';
import { USER_NAME_KEY } from '@/constant/cache';
import useGlobal from '@/store/useGlobal';

const globalStore = useGlobal();
const userName = useLocalStorage(USER_NAME_KEY, '');
const open = ref<boolean>(false);

/** 退出登录 */
function logoutHandler() {
  globalStore.logout();
}
</script>

<template>
  <div
    class="z-50 flex h-72 shrink-0 items-center justify-between bg-white px-24"
  >
    <div>
      <img :src="headerLogo" class="h-36" alt="哈瑞特医疗LOGO">
    </div>
    <ElPopover
      placement="bottom-end"
      trigger="hover"
      :popper-style="{
        minWidth: '110px',
        boxSizing: 'border-box',
        width: '110px',
      }"
    >
      <template #reference>
        <div class="flex cursor-pointer items-center gap-8">
          <img
            class="size-20 rounded-full"
            :src="userAvatar"
            alt=""
          >
          <div class="flex items-center text-[#3a4762]">
            {{ userName }}
            <div class="ml-8 size-0 [border-left:4px_solid_transparent] [border-right:4px_solid_transparent] [border-top:4px_solid_#3a4762]" />
          </div>
        </div>
      </template>
      <div
        class="flex flex-col items-center justify-between gap-16"
      >
        <div
          class="flex w-full cursor-pointer items-center"
          @click.stop="open = true"
        >
          <img :src="changePassword" alt="lock icon" class="size-16">
          <span class="ml-8 text-sm">
            修改密码
          </span>
        </div>
        <div
          class="flex w-full cursor-pointer items-center"
          @click.stop="logoutHandler"
        >
          <img :src="logout" alt="logout icon" class="size-16">
          <span class="ml-8 text-sm text-[#e63746]">
            退出登录
          </span>
        </div>
      </div>
    </ElPopover>
    <ChangePasswordDialog v-model="open" />
  </div>
</template>
