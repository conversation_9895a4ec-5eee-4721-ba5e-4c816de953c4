<script setup lang="ts">
import Header from '@/layout/Header.vue';
import Sidebar from '@/layout/Sidebar.vue';

defineOptions({
  name: 'Layout',
});
</script>

<template>
  <div class="flex h-screen flex-col overflow-hidden bg-gradient-to-b from-[#cddcff] to-[#f1f4fe]">
    <Header />
    <div class="box-border flex flex-1 gap-16 overflow-hidden py-16">
      <Sidebar />
      <div id="main-content" class="relative z-10 box-border flex-1 overflow-auto bg-transparent pr-16">
        <RouterView />
      </div>
    </div>
  </div>
</template>
