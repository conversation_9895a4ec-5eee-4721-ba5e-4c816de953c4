<script setup lang="ts">
import { ArrowRightBold } from '@element-plus/icons-vue';
import { ElIcon } from 'element-plus';
import { isEmpty } from 'lodash-es';
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { MENU_CONFIG } from '@/constant';
import useGlobal from '@/store/useGlobal';

export interface SidebarProps {
  /** 是否只保持一个子菜单的展开 */
  uniqueOpened?: boolean;
}

const { uniqueOpened = false } = defineProps<SidebarProps>();
const router = useRouter();
const globalStore = useGlobal();
/** 打开的菜单 */
const openedMenu = ref(Object.fromEntries(
  MENU_CONFIG.filter(menu => !isEmpty(menu.children)).map(menu => [
    menu.name,
    true,
  ]),
));

const curPath = computed(() => router.currentRoute.value.path);

const curMenu = computed(() => {
  const roles = globalStore.roles;
  if (roles?.length) {
    return MENU_CONFIG.filter(v => roles.includes(v.role));
  }
  return [];
});

/**
 * 判断是否有子菜单被选中
 * @param item
 */
function isChildrenActive(item: any) {
  if (isEmpty(item.children))
    return false;
  for (const element of item.children) {
    if (element.path === curPath.value) {
      return true;
    }
  }
  return false;
}

function clickHandler(item: any, e?: MouseEvent) {
  globalStore.getSelectData(item.role);
  if (!isEmpty(item.children)) {
    openedMenu.value[item.name] = !openedMenu.value[item.name];
    return;
  }
  if (curPath.value !== item.path) {
    router.replace(item.path);
  }
  ((e?.target as HTMLDivElement)?.parentNode as HTMLDivElement)?.blur();
}
</script>

<template>
  <div
    class="h-full w-130 select-none overflow-hidden rounded-tr-md bg-white py-16 text-sm 3xl:w-146"
  >
    <div v-for="item in curMenu" :key="item.name">
      <div
        class="box-border flex min-h-50 w-full cursor-pointer items-center gap-8 px-24" :class="[
          item.path === curPath
            ? 'border-l-[6px] border-[#2E6BE6] bg-[rgba(46,107,230,0.1)] pl-18 text-[#2E6BE6]'
            : 'text-[#111] hover:bg-[#F5F5F5] hover:text-[#111]',
          isChildrenActive(item)
            ? 'bg-[rgba(46,107,230,0.1)] text-[#2E6BE6]'
            : '',
        ]"
        @click="() => clickHandler(item)"
      >
        <img v-if="item.icon" :src="item.icon" alt="logo" class="size-18">
        <span>{{ item.name }}</span>
        <ElIcon
          v-if="!isEmpty(item.children)"
          :size="10"
          class="ml-auto" :class="[
            {
              'rotate-90': openedMenu[item.name],
            },
          ]"
        >
          <ArrowRightBold />
        </ElIcon>
      </div>
      <div
        v-if="item.children && openedMenu[item.name]"
        :key="item.name" class="flex flex-col"
        :class="[
          isChildrenActive(item) ? 'bg-[rgba(46,107,230,0.1)]' : '',
        ]"
      >
        <div
          v-for="val in item.children"
          :key="val.name"
          class="flex min-h-50 cursor-pointer items-center gap-8 pl-48 3xl:pl-64" :class="[
            val.path === curPath
              ? 'bg-[#2E6BE6] text-white'
              : 'text-[#111] hover:bg-[#F5F5F5]',
          ]"
          @click="() => clickHandler(val)"
        >
          <span>{{ val.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
