import type { DeptType } from '@/constant';
import { defineStore } from 'pinia';
import STORE_NAMES from '@/constant/storeNames';

export const usePeopleStore = defineStore(STORE_NAMES.PEOPLE, () => {
  const userId = ref<number>(-1);
  const deptType = ref<DeptType>('OTHER');
  const deptId = ref<number>(-1);
  const osUserId = ref<number>(-1);
  const userAccountRole = ref<number>(-1);

  const searchParams = computed(() => ({
    userId: userId.value,
    deptType: deptType.value,
    deptId: deptId.value,
    osUserId: osUserId.value,
  }));

  const setSearchParams = (params: {
    userId: number;
    deptType: DeptType;
    deptId: number;
    osUserId: number;
    userAccountRole: number;
  }) => {
    userId.value = params.userId;
    deptType.value = params.deptType;
    deptId.value = params.deptId;
    osUserId.value = params.osUserId;
    userAccountRole.value = params.userAccountRole;
  };

  return {
    userId,
    deptType,
    deptId,
    osUserId,
    searchParams,
    userAccountRole,
    setSearchParams,
  };
});
