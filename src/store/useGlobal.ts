import type { IApiBpUserLoginParams, IApiDpDoctorReviewFollowSelect } from '@/interface/type';
import { getSelect } from '@/api';
import { login, logout } from '@/api/login';
import { MENU_CONFIG } from '@/constant';
import { ROLES_KEY, TOKEN_KEY, USERID_KEY } from '@/constant/cache';
import STORE_NAMES from '@/constant/storeNames';
import router from '@/router';
import { useLocalStorage } from '@vueuse/core';
import { defineStore } from 'pinia';

type Role = 'SELLER' | 'MARKET' | 'DOCTOR' | 'CUSTOMER';
interface IState {
  roles?: string[];
  token?: string;
  osUserId?: number;
  selectData: Record<Role, IApiDpDoctorReviewFollowSelect>;
}
const useGlobal = defineStore(STORE_NAMES.GLOBAL, {
  state: () =>
    ({
      roles: useLocalStorage(ROLES_KEY, [
        'seller',
        'market',
        'medical',
        'customer',
      ]).value,
      token: useLocalStorage(TOKEN_KEY, '').value,
      osUserId: useLocalStorage(USERID_KEY, undefined).value,
      selectData: {},
    }) as IState,
  actions: {
    setRoles(roles = []) {
      this.roles = roles;
    },
    async login(params: IApiBpUserLoginParams) {
      await login(params).then((res) => {
        const { data, code } = res;
        if (code === 'E000000') {
          const { token, osUserId, userPermissions } = data;
          useLocalStorage(TOKEN_KEY, token).value = token;
          useLocalStorage(USERID_KEY, osUserId).value = osUserId;
          useLocalStorage(ROLES_KEY, userPermissions).value = userPermissions;
          this.token = token;
          this.osUserId = osUserId;
          this.roles = userPermissions;
          const menuList = MENU_CONFIG.filter(v =>
            userPermissions?.includes(v.role),
          );
          const curItem = menuList?.[0];
          const curPath = curItem?.children
            ? curItem?.children?.[0]?.path
            : curItem?.path;
          router.push(curPath);
        }
      });
    },
    async logout() {
      await logout();
      this.clearLoginInfo();
    },
    async clearLoginInfo() {
      useLocalStorage(TOKEN_KEY, '').value = '';
      useLocalStorage(USERID_KEY, undefined).value = undefined;
      useLocalStorage(ROLES_KEY, undefined).value = undefined;
      this.token = '';
      this.osUserId = undefined;
      this.roles = undefined;
      router.replace('/login');
      setTimeout(() => {
        window.location.reload();
      }, 100);
    },
    async getSelectData(type) {
      if (this.selectData[type])
        return;
      const res = await getSelect({ userType: type });
      this.selectData[type] = res;
    },
    getCusSelectData(type) {
      return this.selectData[type];
    },
  },
});

export default useGlobal;
