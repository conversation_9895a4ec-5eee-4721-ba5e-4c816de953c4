export function getHeadingInView(ids: string[]): string | null {
  const headingPositions = ids.map((id) => {
    const offsetTop = document.querySelector(`#${id}`)?.offsetTop ?? 0;
    const rect = document.querySelector(`#${id}`)?.getBoundingClientRect() as any;
    return {
      id,
      top: offsetTop,
      betweenTop: document.querySelector('#main-content')?.scrollTop - offsetTop + 80,
      rectHeight: rect.height,
    };
  });
  // Find the first heading that's either at the top or just above the viewport
  const activeHeading = headingPositions.find((heading, index) => {
    const nextHeading = headingPositions[index + 1];
    if (!nextHeading)
      return false;

    return (heading.betweenTop > -32 && heading.betweenTop <= (heading.rectHeight));
  });

  return activeHeading?.id || null;
}
