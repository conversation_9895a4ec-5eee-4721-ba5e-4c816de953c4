interface RGBA {
  r: number;
  g: number;
  b: number;
  a: number;
}

/**
 * 解析 HEX 格式的颜色字符串，返回 RGBA 数据
 * @param hex - HEX 颜色字符串，支持 3 位、4 位、6 位、8 位格式
 * @returns RGBA 对象，包含 r、g、b、a 四个属性
 * @example
 * parseHexColor('#fff')      // { r: 255, g: 255, b: 255, a: 1 }
 * parseHexColor('#ffff')     // { r: 255, g: 255, b: 255, a: 1 }
 * parseHexColor('#ffffff')   // { r: 255, g: 255, b: 255, a: 1 }
 * parseHexColor('#ffffff80') // { r: 255, g: 255, b: 255, a: 0.5 }
 */
export function parseHexColor(hex: string): RGBA {
  // 移除 # 前缀
  hex = hex.replace('#', '');

  // 将 3/4 位颜色转换为 6/8 位
  if (hex.length === 3 || hex.length === 4) {
    hex = hex
      .split('')
      .map(char => char + char)
      .join('');
  }

  // 解析 RGB 值
  const r = Number.parseInt(hex.slice(0, 2), 16);
  const g = Number.parseInt(hex.slice(2, 4), 16);
  const b = Number.parseInt(hex.slice(4, 6), 16);

  // 解析 Alpha 值（如果存在）
  let a = 1;
  if (hex.length === 8) {
    a = Number.parseInt(hex.slice(6, 8), 16) / 255;
  }

  return { r, g, b, a };
}

/**
 * 将 RGBA 对象转换为 CSS rgba() 字符串
 * @param rgba - RGBA 对象
 * @returns CSS rgba() 字符串
 * @example
 * rgbaToString({ r: 255, g: 255, b: 255, a: 0.5 }) // 'rgba(255, 255, 255, 0.5)'
 */
export function rgbaToString(rgba: RGBA): string {
  return `rgba(${rgba.r}, ${rgba.g}, ${rgba.b}, ${rgba.a})`;
}

/**
 * 将 HEX 颜色转换为带透明度的 RGBA 颜色
 * @param hex - HEX 颜色字符串
 * @param alpha - 透明度值（0-1）
 * @returns CSS rgba() 字符串
 * @example
 * hexToRGBA('#fff', 0.5)    // 'rgba(255, 255, 255, 0.5)'
 * hexToRGBA('#000000', 0.8) // 'rgba(0, 0, 0, 0.8)'
 */
export function hexToRGBA(hex: string, alpha: number): string {
  const rgba = parseHexColor(hex);
  rgba.a = alpha;
  return rgbaToString(rgba);
}

/**
 * 调整颜色的亮度
 * @param hex - HEX 颜色字符串
 * @param amount - 亮度调整值（-1 到 1，负值变暗，正值变亮）
 * @returns 新的 HEX 颜色字符串
 * @example
 * adjustBrightness('#ff0000', 0.2)  // 更亮的红色
 * adjustBrightness('#00ff00', -0.2) // 更暗的绿色
 */
export function adjustBrightness(hex: string, amount: number): string {
  const rgba = parseHexColor(hex);
  const adjust = (value: number) => {
    const newValue = Math.round(value + amount * 255);
    return Math.max(0, Math.min(255, newValue));
  };

  rgba.r = adjust(rgba.r);
  rgba.g = adjust(rgba.g);
  rgba.b = adjust(rgba.b);

  const toHex = (n: number) => {
    const hex = n.toString(16);
    return hex.length === 1 ? `0${hex}` : hex;
  };

  return `#${toHex(rgba.r)}${toHex(rgba.g)}${toHex(rgba.b)}`;
}

/**
 * 检查颜色是否为浅色
 * @param hex - HEX 颜色字符串
 * @returns 是否为浅色
 * @example
 * isLightColor('#ffffff') // true
 * isLightColor('#000000') // false
 */
export function isLightColor(hex: string): boolean {
  const rgba = parseHexColor(hex);
  // 使用 YIQ 公式计算亮度
  const yiq = (rgba.r * 299 + rgba.g * 587 + rgba.b * 114) / 1000;
  return yiq >= 128;
}

/**
 * 将 HEX 颜色转换为带透明度的 RGBA 颜色
 * @param hex - HEX 颜色字符串
 * @param alpha - 透明度值（0-1）
 * @returns CSS rgba() 字符串
 * @example
 * adjustHexAlpha('#fff', 0.5)    // 'rgba(255, 255, 255, 0.5)'
 * adjustHexAlpha('#000000', 0.8) // 'rgba(0, 0, 0, 0.8)'
 */
export function adjustHexAlpha(hex: string, alpha: number): string {
  const rgba = parseHexColor(hex);
  rgba.a = alpha;
  return rgbaToString(rgba);
}
