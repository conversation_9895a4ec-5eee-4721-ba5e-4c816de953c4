/**
 * 将数字格式化为千分位格式
 * @param num 要格式化的数字
 * @returns 格式化后的字符串
 * @example
 * formatNumber(1234567) // "1,234,567"
 * formatNumber(1234.56) // "1,234.56"
 */
export function formatNumber(num: number): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * 将数字格式化为指定小数位的字符串
 * @param num 要格式化的数字
 * @param digits 小数位数，默认为0
 * @returns 格式化后的字符串
 * @example
 * formatDecimal(1234.567) // "1235"
 * formatDecimal(1234.567, 2) // "1234.57"
 * formatDecimal(1234.000, 2) // "1234"
 */
export function formatDecimal(num: number, digits: number = 0): string {
  if (digits === 0) {
    return Math.round(num).toString();
  }

  const rounded = Number(num.toFixed(digits));
  // 如果小数部分全为0，则返回整数部分
  if (rounded % 1 === 0) {
    return rounded.toString();
  }
  return rounded.toFixed(digits);
}

/**
 * 将数字格式化为指定小数位的字符串，并去除末尾的0
 * @param num 要格式化的数字
 * @param digits 最大小数位数
 * @example
 * formatDecimalTrim(1234.567, 2) // "1234.57"
 * formatDecimalTrim(1234.500, 2) // "1234.5"
 * formatDecimalTrim(1234.000, 2) // "1234"
 * formatDecimalTrim(1234.560, 2) // "1234.56"
 */
export function formatDecimalTrim(num: number, digits: number = 2): number {
  // 首先将数字四舍五入到指定小数位
  const rounded = Number(num.toFixed(digits));
  return rounded;
}
