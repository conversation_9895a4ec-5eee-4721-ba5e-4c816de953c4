import dayjs from 'dayjs';

/**
 * 获取近一年的开始日期和结束日期
 * @returns [开始日期, 结束日期]
 */
export function getLastYear() {
  const start = dayjs().subtract(1, 'year').add(1, 'month').startOf('month').format('YYYY-MM-DD');
  const end = getCurrentYearEndDate();
  return [start, end];
}

/**
 * 获取本年的开始日期
 * @returns 本年开始日期，格式：YYYY-MM-DD
 */
export function getCurrentYearStartDate() {
  return dayjs().startOf('year').format('YYYY-MM-DD');
}

/**
 * 获取本年的结束日期（今天）
 * @returns 今天的日期，格式：YYYY-MM-DD
 */
export function getCurrentYearEndDate() {
  return dayjs().format('YYYY-MM-DD');
}

export function getCurrentMonthStartDate() {
  return dayjs().startOf('month').format('YYYY-MM-DD');
}

/**
 * 获取本年的开始日期和结束日期（今天）
 * @returns [开始日期, 结束日期]
 */
export function getCurrentYear(): [string, string] {
  const start = getCurrentYearStartDate();
  const end = getCurrentYearEndDate();
  return [start, end];
}

export function getCurrentMonth(): [string, string] {
  const start = getCurrentMonthStartDate();
  const end = getCurrentYearEndDate();
  return [start, end];
}

/**
 * 将分钟数转换为小时和分钟
 * @param min 分钟数
 * @returns 小时和分钟
 */
export function minShow(min: number) {
  return min > 60 ? `${Math.floor(min / 60)}h${min % 60}min` : `${min}min`;
}
