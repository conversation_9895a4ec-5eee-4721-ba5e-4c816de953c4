<script setup lang="ts">
import { VueQueryDevtools } from '@tanstack/vue-query-devtools';
import { ElConfigProvider } from 'element-plus';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import { RouterView } from 'vue-router';
</script>

<template>
  <ElConfigProvider :locale="zhCn">
    <div class="min-w-[1000px]">
      <RouterView />
    </div>
    <VueQueryDevtools />
  </ElConfigProvider>
</template>

<style>
html {
  height: 100vh;
  overflow: hidden;
  font-family:
    PingFangSC,
    PingFang SC,
    'Microsoft Yahei',
    sans-serif;
}
body {
  height: 100%;
  overflow-y: auto;
  width: 100% !important;
}
</style>
