import { VueQueryPlugin } from '@tanstack/vue-query';
import { ElMessage } from 'element-plus';
import { createApp } from 'vue';
import vue3TreeOrg from 'vue3-tree-org';
import { setupStore } from '@/store/store';
import App from './App.vue';
import bus from './lib/bus';
import router from './router';
import 'element-plus/dist/index.css';
import './styles/index.css';
import './styles/tailwind.css';
import 'vue3-tree-org/lib/vue3-tree-org.css';

const app = createApp(App);
app.config.globalProperties.$message = ElMessage;
app.config.globalProperties.$bus = bus;
setupStore(app);
app.use(router).use(VueQueryPlugin).use(vue3TreeOrg);
app.mount('#app');
