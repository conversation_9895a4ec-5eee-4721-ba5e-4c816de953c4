import { setupStore } from '@/store/store';
import { VueQueryPlugin } from '@tanstack/vue-query';
import { ElMessage } from 'element-plus';
import 'element-plus/dist/index.css';
import { createApp } from 'vue';
import App from './App.vue';
import bus from './lib/bus';
import router from './router';
import './styles/index.css';
import './styles/tailwind.css';

const app = createApp(App);
app.config.globalProperties.$message = ElMessage;
setupStore(app);
app.use(router).use(VueQueryPlugin);
app.config.globalProperties.$bus = bus;

app.mount('#app');
