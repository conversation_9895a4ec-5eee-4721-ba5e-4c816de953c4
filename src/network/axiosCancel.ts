import type { AxiosRequestConfig } from 'axios';
import { ZodAny } from 'zod';

// 用于存储每个请求的标识和取消函数
const pendingMap = new Map<string, AbortController>();

/** 自定义请求参数 */
export interface ICustomConfig {
  ignoreRequestParams?: boolean; // 重复请求取消是否忽略参数， 默认 false
  repeatRequestCancel?: boolean; // 是否开启取消重复请求, 默认为 开启
  reductDataFormat?: boolean; // 是否开启简洁数据响应结构, 默认为 开启
  codeMessageShow?: boolean; // 是否开启code不为0时的信息提示, 默认为 开启
}
export interface HttpRequestConfig extends AxiosRequestConfig {
  customConfig?: ICustomConfig;
  responseSchema?: ZodAny;
}
function getPendingUrl(config: HttpRequestConfig) {
  const { url, method, params, customConfig } = config;
  let data = config.data;
  let res: any[] = [];
  if (typeof data === 'string') data = JSON.parse(data);
  if (customConfig?.ignoreRequestParams) {
    res = [url, method];
  } else {
    res = [url, method, JSON.stringify(params), JSON.stringify(data)];
  }
  return res.join('&');
}

export default class AxiosCanceler {
  /**
   * 添加请求
   * @param config 请求配置
   */
  addPending(config: AxiosRequestConfig) {
    this.removePending(config);
    const url = getPendingUrl(config);
    const controller = new AbortController();
    config.signal = config.signal || controller.signal;
    if (!pendingMap.has(url)) pendingMap.set(url, controller);
  }

  /**
   * 清除所有等待中的请求
   */
  removeAllPending() {
    pendingMap.forEach(abortController => {
      if (abortController) abortController.abort();
    });
    this.reset();
  }

  /**
   * 移除请求
   * @param config 请求配置
   */
  removePending(config: AxiosRequestConfig) {
    const url = getPendingUrl(config);
    if (pendingMap.has(url)) {
      // 如果当前请求在等待中，取消它并将其从等待中移除
      const abortController = pendingMap.get(url);
      if (abortController?.signal) abortController.abort();
      pendingMap.delete(url);
    }
  }

  /**
   * 重置
   */
  reset() {
    pendingMap.clear();
  }
}
