import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';
import type { z, ZodSchema } from 'zod';
import type { HttpRequestConfig, ICustomConfig } from './axiosCancel';
import { TOKEN_KEY } from '@/constant/cache';
import useGlobal from '@/store/useGlobal';
import { useLocalStorage } from '@vueuse/core';
import axios from 'axios';
import AxiosCanceler from './axiosCancel';

const axiosCanceler = new AxiosCanceler();

// 接口成功返回code
const RES_SUCCESS_CODE = 'E000000';

/** 默认数据响应结构 */
export interface IResponse<T> {
  code: string;
  message: string;
  data: T;
  requestTime: string;
  requestId: string;
}

/** AxiosRequestConfig */
export interface HttpInternalAxiosRequestConfig extends InternalAxiosRequestConfig {
  customConfig?: ICustomConfig;
}

/** 不开启 简洁响应数据结构时的请求配置 */
interface RequestWithOutReductDataFormat extends AxiosRequestConfig {
  customConfig: ICustomConfig & { reductDataFormat: false };
}

interface RequestWithOutSchema<T extends ZodSchema> extends AxiosRequestConfig {
  schema: T;
}

/** 开启 简洁数据响应结构时返回的数据 */
type ResponseWithReductDataFormat<T> = IResponse<T>['data'];

/** 默认自定义配置 */
const defaultCustomConfig: ICustomConfig = {
  ignoreRequestParams: false,
  repeatRequestCancel: true,
  reductDataFormat: true,
  codeMessageShow: true,
};

/** axios 默认配置 */
const defaultConfig: AxiosRequestConfig = {
  // 请求超时时间
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
  },
  baseURL: import.meta.env.VITE_APP_baseUrl,
};

class Http {
  constructor() {
    this.httpInterceptorsRequest();
    this.httpInterceptorsResponse();
  }

  /** 保存当前Axios实例对象 */
  private static axiosInstance: AxiosInstance = axios.create(defaultConfig);

  /** 请求拦截 */
  private httpInterceptorsRequest(): void {
    Http.axiosInstance.interceptors.request.use(
      (config: HttpInternalAxiosRequestConfig) => {
        const token = useLocalStorage(TOKEN_KEY, '').value;
        const { customConfig } = config;

        if (token && config.headers) {
          config.headers.Authorization = token;
        }

        if (customConfig?.repeatRequestCancel) {
          axiosCanceler.addPending(config);
        }

        return config;
      },
      (error: Error) => {
        return Promise.reject(error);
      },
    );
  }

  /** 响应拦截 */
  private httpInterceptorsResponse(): void {
    Http.axiosInstance.interceptors.response.use(
      async (response: AxiosResponse) => {
        if (response) {
          axiosCanceler.removePending(response.config);
        }
        await this.requestErrorHandler(response);
        return response;
      },
      (error: Error) => {
        let errMsg = error?.message || error;
        if (errMsg === 'canceled') {
          const { url } = (error as any)?.config || {};
          errMsg += `[${url}]`;
        }
        return Promise.reject(`resp.error=${errMsg}`);
      },
    );
  }

  /** 统一错误处理函数 */
  private async requestErrorHandler(response: AxiosResponse) {
    try {
      const { code, message } = response?.data || {};
      if (['E000009', 'E000010', 'E000011'].includes(code)) {
        axiosCanceler.removeAllPending();
        const userStore = useGlobal();
        await userStore.clearLoginInfo();
        return Promise.reject(message);
      }
    }
    catch (err) {
      throw new Error((err as Error).message);
    }
  }

  /** 通用请求工具函数 */
  request<T>(config: RequestWithOutReductDataFormat): Promise<IResponse<T>>;
  request<S extends ZodSchema>(config: RequestWithOutSchema<S>): Promise<z.infer<S>>;
  request<T>(config: HttpRequestConfig): Promise<ResponseWithReductDataFormat<T>>;
  request<T>(config: any): Promise<IResponse<T> | ResponseWithReductDataFormat<T>> {
    const customConfig = {
      ...defaultCustomConfig,
      ...config.customConfig,
    };
    const reqConfig = {
      ...config,
      customConfig,
    };
    const { reductDataFormat, codeMessageShow } = customConfig;

    const handleResponse = (response: AxiosResponse<IResponse<T>>) => {
      const resp = response.data;

      if (codeMessageShow && resp?.code !== RES_SUCCESS_CODE) {
        ElMessage({
          type: 'error',
          message: response.data?.message || '服务器错误',
        });
        throw response.data;
      }

      /** 如果存在响应的Schema定义 */
      if (config.schema) {
        const result = config.schema?.safeParse(resp?.data);
        if (result.success === false) {
          console.error(`${reqConfig.url}响应参数校验失败`, resp?.data, result.error);
          return null;
        }
        else {
          return result.data;
        }
      }

      return reductDataFormat ? resp?.data : resp;
    };

    return new Promise<IResponse<T> | IResponse<T>['data']>((resolve, reject) => {
      Http.axiosInstance
        .request(reqConfig)
        .then((response: AxiosResponse<IResponse<T>>) => {
          resolve(handleResponse(response));
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  get<T>(config: RequestWithOutReductDataFormat): Promise<IResponse<T>>;
  get<T>(config: HttpRequestConfig): Promise<ResponseWithReductDataFormat<T>>;
  get<T>(config: any): Promise<IResponse<T> | ResponseWithReductDataFormat<T>> {
    return this.request<T>({ ...config, method: 'GET' });
  }

  post<T>(config: RequestWithOutReductDataFormat): Promise<IResponse<T>>;
  post<T>(config: HttpRequestConfig): Promise<ResponseWithReductDataFormat<T>>;
  post<S extends ZodSchema>(config: RequestWithOutSchema<S>): Promise<z.infer<S>>;
  post<T>(config: any): Promise<IResponse<T> | ResponseWithReductDataFormat<T>> {
    return this.request({ ...config, method: 'POST' });
  }

  patch<T>(config: RequestWithOutReductDataFormat): Promise<IResponse<T>>;
  patch<T>(config: HttpRequestConfig): Promise<ResponseWithReductDataFormat<T>>;
  patch<T>(config: any): Promise<IResponse<T> | ResponseWithReductDataFormat<T>> {
    return this.request<T>({ ...config, method: 'PATCH' });
  }

  put<T>(config: RequestWithOutReductDataFormat): Promise<IResponse<T>>;
  put<T>(config: HttpRequestConfig): Promise<ResponseWithReductDataFormat<T>>;
  put<T>(config: any): Promise<IResponse<T> | ResponseWithReductDataFormat<T>> {
    return this.request<T>({ ...config, method: 'PUT' });
  }

  delete<T>(config: RequestWithOutReductDataFormat): Promise<IResponse<T>>;
  delete<T>(config: HttpRequestConfig): Promise<ResponseWithReductDataFormat<T>>;
  delete<T>(config: any): Promise<IResponse<T> | ResponseWithReductDataFormat<T>> {
    return this.request<T>({ ...config, method: 'DELETE' });
  }
}

export const http = new Http();
