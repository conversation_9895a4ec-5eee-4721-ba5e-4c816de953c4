<script setup lang="ts">
import { vIntersectionObserver } from '@vueuse/components';

defineOptions({
  name: 'FixedHeader',
});

const root = document.querySelector('#main-content')! as HTMLElement;
const isFixed = shallowRef(false);

function onIntersectionObserver([entry]: IntersectionObserverEntry[]) {
  isFixed.value = !entry?.isIntersecting;
}
</script>

<template>
  <div
    v-intersection-observer="[onIntersectionObserver, { threshold: [1], root }]"
    class="sticky -top-1 z-[3000]"
    :class="{
      'bg-white': isFixed,
    }"
  >
    <div
      class="flex h-64 items-center"
      :class="{
        'px-24 py-0 [border-top:1px_solid_rgba(0,_0,_0,_0.1)] [box-shadow:0px_1px_1px_0px_rgba(0,_0,_0,_0.1)]': isFixed,
      }"
    >
      <slot />
    </div>
  </div>
</template>
