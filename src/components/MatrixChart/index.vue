<script setup lang="ts">
import { CaretRight, CaretTop } from '@element-plus/icons-vue';
import { useQuery } from '@tanstack/vue-query';
import { ElIcon } from 'element-plus';
import { toRefs } from 'vue';
import { fetchAbilityDist } from '@/api';

interface Props {
  departmentId: number;
  deptType: string;
}

defineOptions({
  name: 'MatrixChart',
});

const props = defineProps<Props>();
const { departmentId, deptType } = toRefs(props);

const { data } = useQuery({
  queryKey: ['abilityDist', departmentId, deptType],
  queryFn: () => fetchAbilityDist(departmentId.value, deptType.value),
});
</script>

<template>
  <div class="flex flex-col">
    <!-- 标题和坐标轴标签 -->
    <div class="pb-10 pl-10 text-sm text-[#939CAE]">
      素质
    </div>
    <div class="relative w-full">
      <ElIcon color="#DCDFE6" class="absolute -top-10 left-15">
        <CaretTop />
      </ElIcon>
      <ElIcon color="#DCDFE6" class="absolute bottom-18 right-28">
        <CaretRight />
      </ElIcon>
      <!-- Y轴刻度 -->
      <div class="flex gap-8">
        <div class="flex flex-col pt-12 text-sm text-[#939CAE]">
          <div class="grid h-74 place-content-center">
            高
          </div>
          <div class="grid h-74 place-content-end">
            中
          </div>
        </div>
        <div
          class="flex-1 border-b border-l border-solid border-[#DCDFE6] p-12 pr-4 pt-8"
        >
          <div
            class="grid h-224 grid-cols-3 grid-rows-3 text-sm text-white"
          >
            <div class="row-span-2 flex flex-col items-center justify-center gap-4 bg-[#43A1E6]">
              <div>{{ data?.perfWeakQuaOkNum ?? 0 }}人</div>
              <div>业绩不佳但素质尚可</div>
            </div>
            <div class="flex flex-col items-center justify-center gap-4 bg-[#4EC244]">
              <div>{{ data?.quaCoreNum ?? 0 }}人</div>
              <div>素质核心骨干</div>
            </div>
            <div class="flex flex-col items-center justify-center gap-4 bg-[#5285EB]">
              <div>{{ data?.superEmpNum ?? 0 }}人</div>
              <div>超级员工</div>
            </div>
            <div class="flex flex-col items-center justify-center gap-4 bg-[#E88B48]">
              <div>{{ data?.backboneNum ?? 0 }}人</div>
              <div>中坚力量</div>
            </div>
            <div class="flex flex-col items-center justify-center gap-4 bg-[#4EC244]">
              <div>{{ data?.businessCoreNum ?? 0 }}人</div>
              <div>业务核心骨干</div>
            </div>
            <div class="flex flex-col items-center justify-center gap-4 bg-[#B4CC3B]">
              <div>{{ data?.underPerformerNum ?? 0 }}人</div>
              <div>失败者</div>
            </div>
            <div class="col-span-2 flex flex-col items-center justify-center gap-4 bg-[#43A1E6]">
              <div>{{ data?.quaWeakAbiOkNum ?? 0 }}人</div>
              <div>素质不佳但能力尚可</div>
            </div>
          </div>
        </div>
        <div class="relative w-28 text-sm text-[#939CAE]">
          <div class="absolute -bottom-6 w-28">
            业绩
          </div>
        </div>
      </div>

      <!-- X轴 -->
      <div class="mt-12 grid grid-cols-3 text-sm text-[#939CAE]">
        <div>低</div>
        <div class="grid place-content-center">
          中
        </div>
        <div class="grid place-content-center">
          高
        </div>
      </div>
    </div>
  </div>
</template>
