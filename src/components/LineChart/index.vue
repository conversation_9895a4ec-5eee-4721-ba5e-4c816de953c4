<template>
  <div class="w-full h-full" :style="{ height: (height ?? 234) + 'px' }">
    <Card :title="title" bold :desc="desc">
      <BaseChart type="line" :data-complete="true" :options="lineOptions" />
    </Card>
  </div>
</template>

<script setup lang="ts">
import BaseChart from '@/components/BaseChart/BaseChart.vue';
import Card from '@/components/Card/index.vue';
import { getLineEchartsOptions } from '@/components/BaseChart/options/line';
import {
  getTooltipConfig,
  IChartCommonProps,
} from '@/components/BaseChart/options';
import { COLORS_BASE } from '../BaseChart/options';

interface IProps extends IChartCommonProps {
  title?: string;
  height?: number;
  desc?: string;
}

const props = defineProps<IProps>();
const lineOptions = ref({});

const getOptions = () => {
  const { grid, xData, zoomSize, dataZoomConfig, data } = props;
  const dataZoomEnable = (xData?.length ?? 0) > (zoomSize ?? 12);
  return getLineEchartsOptions({
    tooltip: getTooltipConfig({
      transform: params => {
        const res = {};
        const labelKeys: string[] = [];
        let other: any[] = [];
        const colors: string[] = [];
        const title = params[0].axisValueLabel;
        const isGroup = params.length > 1;
        for (const v of params) {
          colors.push(v.color);
          const item = {
            order: v.data?.order,
            label: v.data.name,
            value: v.data.customSymbol
              ? `${v.value ?? 0}${v.data.customSymbol}`
              : (v.value ?? 0),
          };
          if (v.data.extraList) {
            other.push(...v.data.extraList);
          }
          if (res[v.seriesName]) {
            res[v.seriesName].push(item);
          } else {
            res[v.seriesName] = [item, ...other];
            labelKeys.push(v.seriesName);
            other = [];
          }
        }
        if (!isGroup) {
          return {
            title,
            items: res[params[0].seriesName],
          };
        }
        const groupData = labelKeys.map((v, i) => ({
          subTitle: v,
          color: colors[i],
          items: res[v],
        }));
        return {
          type: 'group',
          rows: Math.min(groupData.length, 5),
          groupItems: groupData,
        };
      },
    }),
    color: props.color ?? COLORS_BASE,
    grid: { ...grid },
    xAxisData: xData ?? [],
    dataZoom: {
      enable: dataZoomEnable,
      config: {
        endValue: 11,
        ...dataZoomConfig,
      },
    },
    yAxis: props.yAxis ?? {
      type: 'value',
      min: 0,
      axisLabel: {
        formatter: '{value}%',
      },
    },
    seriesConfig: data ?? [],
  });
};
lineOptions.value = getOptions();
watch(
  () => props.data,
  () => (lineOptions.value = getOptions())
);
defineOptions({
  name: 'LineChart',
});
</script>

<style scoped lang="less">
// todo
</style>
