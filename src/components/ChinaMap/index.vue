<script setup lang="ts">
import type { EChartsOption } from 'echarts';
import { MapChart } from 'echarts/charts';
import {
  GeoComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components';
import { registerMap, use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { isEmpty } from 'lodash-es';
import { onMounted, toRefs, useTemplateRef } from 'vue';
import VChart from 'vue-echarts';
import chinaMap from '@/assets/json/china.json';
import ProvinceMap from './ProvinceMap.vue';

defineOptions({
  name: 'ChinaMap',
});

const props = defineProps<{
  province?: string;
  /** 可选择的省份列表 */
  provinces?: string[];
  /** 是否开启下钻 */
  drillDown?: boolean;
}>();

const emit = defineEmits<{
  provinceSelected: [province: string];
}>();

const { province, provinces, drillDown } = toRefs(props);

use([CanvasRenderer, GeoComponent, TitleComponent, LegendComponent, TooltipComponent, MapChart]);

registerMap('zh', chinaMap as any);
registerMap('china', chinaMap as any);
const mapRef = useTemplateRef('map');

const option = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'item',
  },
  geo: {
    map: 'china',
    layoutCenter: ['50%', '50%'], // 地图位置
    layoutSize: '122%',
    label: {
      show: false,
    },
    itemStyle: {
      areaColor: 'white',
      borderColor: '#5480A9',
    },
    emphasis: {
      label: {
        show: !!isEmpty(provinces.value),
        color: 'white',
      },
      itemStyle: {
        areaColor: isEmpty(provinces.value) ? '#2E6BE6' : 'white',
      },
    },
    regions: isEmpty(provinces.value)
      ? []
      : provinces.value?.map(name => ({
        name,
        itemStyle: {
          areaColor: '#2E6BE6',
        },
        emphasis: {
          label: {
            show: true,
            color: 'white',
          },
          itemStyle: {
            areaColor: '#2E6BE6',
          },
        },
        select: {
          label: {
            show: true,
            color: 'white',
            textShadowColor: '#000',
            textShadowBlur: 3,
            textBorderWidth: 1,
          },
          itemStyle: {
            areaColor: '#2E6BE6',
          },
        },
      })),
    select: {
      label: {
        show: !!isEmpty(provinces.value),
        color: 'white',
        textShadowColor: '#000',
        textShadowBlur: 3,
        textBorderWidth: 1,
      },
      itemStyle: {
        areaColor: isEmpty(provinces.value) ? '#2E6BE6' : 'white',
      },
    },
    selectedMode: 'single',
    tooltip: {
      show: false,
    },
    left: 0,
    right: 0,
    bottom: 0,
    top: 0,
    z: 5,
  },
  series: [
    {
      type: 'map',
      map: 'zh',
      label: {
        show: false,
        color: 'white',
      },
      itemStyle: {
        areaColor: '#5480A9',
        borderColor: '#89e0fe',
        shadowBlur: 6,
        shadowColor: 'rgba(0,35,198,0.16)',
        shadowOffsetX: 0,
        shadowOffsetY: 7,
      },
      left: 0,
      right: 0,
      bottom: 0,
      top: 0,
      tooltip: {
        show: false,
      },
      layoutCenter: ['49.75%', '50.5%'], // 地图位置
      layoutSize: '122%',
      z: 4,
      emphasis: {
        itemStyle: {
          areaColor: '#89e0fe',
          borderColor: '#89e0fe',
        },
      },
    },
  ],
}));

function handleGeoSelectedChange(data: { allSelected: { name: string[] }[] }) {
  const provinceName = data.allSelected[0].name.join('');
  if (isEmpty(provinces.value)) {
    emit('provinceSelected', provinceName);
  }
  else {
    if (provinces.value?.includes(provinceName) || !provinceName) {
      emit('provinceSelected', provinceName);
    }
    else {
      mapRef.value?.dispatchAction({
        type: 'geoUnSelect',
        name: province.value,
      });
      emit('provinceSelected', '');
    }
  }
}

onMounted(() => {
  setTimeout(() => {
    mapRef.value?.dispatchAction({
      type: 'geoSelect',
      name: province.value,
      isSelected: true,
    });
  }, 200);
});
</script>

<template>
  <ProvinceMap v-if="province && drillDown" :province="province" />
  <VChart
    v-else
    ref="map"
    :option="option"
    autoresize
    style="background-color: transparent; height: 340px"
    @geoselectchanged="handleGeoSelectedChange"
  />
</template>
