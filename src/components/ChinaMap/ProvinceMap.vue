<script setup lang="ts">
import type { EChartsOption } from 'echarts';
import { MapChart } from 'echarts/charts';
import {
  GeoComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components';
import { registerMap, use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { computed, toRefs } from 'vue';
import VChart from 'vue-echarts';
import chinaMap from '@/assets/json/china.json?raw';

defineOptions({
  name: 'ChinaMap',
});

const props = defineProps<{
  province: string;
}>();

const { province } = toRefs(props);

use([CanvasRenderer, GeoComponent, TitleComponent, LegendComponent, TooltipComponent, MapChart]);

const option = computed<EChartsOption>(() => {
  registerMap('province', getMapJson(province.value) as any);
  return ({
    tooltip: {
      trigger: 'item',
    },
    geo: {
      map: 'province',
      layoutCenter: ['50%', '50%'], // 地图位置
      layoutSize: '100%',
      label: {
        show: true,
        color: 'white',
      },
      itemStyle: {
        areaColor: '#2E6BE6',
        borderColor: '#5480A9',
      },
      emphasis: {
        label: {
          show: true,
          color: 'white',
        },
        itemStyle: {
          areaColor: '#2E6BE6',
        },
      },
      select: {
        label: {
          show: true,
          color: 'white',
          textShadowColor: '#000',
          textShadowBlur: 3,
          textBorderWidth: 1,
        },
        itemStyle: {
          areaColor: '#2E6BE6',
        },
      },
      selectedMode: 'single',
      tooltip: {
        show: false,
      },
      left: 0,
      right: 0,
      bottom: 0,
      top: 0,
      z: 5,
    },
    series: [
      {
        type: 'map',
        map: 'province',
        label: {
          show: false,
          color: 'white',
        },
        itemStyle: {
          areaColor: '#5480A9',
          borderColor: '#89e0fe',
          shadowBlur: 6,
          shadowColor: 'rgba(0,35,198,0.16)',
          shadowOffsetX: 0,
          shadowOffsetY: 7,
        },
        left: 0,
        right: 0,
        bottom: 0,
        top: 0,
        tooltip: {
          show: false,
        },
        layoutCenter: ['49.5%', '50.5%'], // 地图位置
        layoutSize: '100%',
        z: 4,
        emphasis: {
          itemStyle: {
            areaColor: '#89e0fe',
            borderColor: '#89e0fe',
          },
        },
      },
    ],
  });
});

function getMapJson(provinceName: string) {
  const provinceData = JSON.parse(chinaMap).features.find(item => item.properties.name === provinceName);
  return JSON.stringify({
    type: 'FeatureCollection',
    features: [
      provinceData ?? {},
    ],
    UTF8Encoding: true,
  });
}
</script>

<template>
  <VChart
    :option="option"
    autoresize
    style="background-color: transparent; height: 340px"
  />
</template>
