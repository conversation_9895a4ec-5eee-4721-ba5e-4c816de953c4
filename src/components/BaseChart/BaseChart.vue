<template>
  <div class="relative clear-both h-full w-full">
    <div ref="baseChartRef" class="h-full w-full"></div>
    <div
      v-show="isEmptyData || !dataComplete"
      v-loading="!dataComplete"
      :element-loading-text="loadingText"
      class="flex-c absolute left-0 top-0 h-full w-full text-[24px] text-[#B8BECC]"
      :style="{ background: type === 'pie' ? '' : '#fff' }"
    >
      {{ !dataComplete || type === 'pie' ? '' : emptyText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { Options } from '@/components/BaseChart/type';
import { isArray } from 'lodash-es';
import { PropType } from 'vue';
import useCharts, { ChartType, IDefaultSize } from './useCharts';

const props = defineProps({
  type: {
    type: String as PropType<ChartType>,
    required: true,
  },
  loadingText: {
    type: String,
    default: '数据加载中...',
  },
  emptyText: {
    type: String,
    default: '暂无数据',
  },
  dataComplete: Boolean,
  options: {
    type: Object as PropType<Options>,
    default: () => ({}),
  },
  // 切换tab或者页面(未销毁)后且产生缩放时chart图片的默认尺寸
  defaultSize: {
    type: Object as PropType<IDefaultSize>,
    default: () => ({
      width: 606,
      height: 360,
    }),
    required: false,
  },
  enableClickListener: {
    type: Boolean,
    default: () => false,
  },
});

const emit = defineEmits(['click-item']);
const { type, options, dataComplete, defaultSize } = toRefs(props);

const baseChartRef = shallowRef<HTMLElement | null>(null);

const { charts, setOptions, initChat } = useCharts({
  el: baseChartRef,
  type,
  defaultSize: defaultSize?.value,
});

const isEmptyData = computed(() => {
  const { options } = props;
  const { series } = options;
  const hasSeriesData = (series as any)?.find(
    (item: any) => item?.data?.length
  );
  return !hasSeriesData;
  // return ['pie', 'funnel', 'scatter'].includes(type)
  //   ? !hasSeriesData
  //   : !(hasXAxisData || hasSeriesData);
});

watch(
  options,
  () => {
    setOptions(options.value);
  },
  {
    deep: true,
  }
);
const initClickEventListener = () => {
  setTimeout(() => {
    charts.value?.on('click', params => {
      emit('click-item', params);
    });
  }, 100);
};
onMounted(() => {
  nextTick(async () => {
    await initChat();
    setOptions(options.value);
    if (props.enableClickListener) {
      initClickEventListener();
    }
  });
});

defineExpose({
  baseChartRef: baseChartRef,
  $charts: charts,
});
</script>
