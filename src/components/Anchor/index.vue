<script setup lang="ts">
import { useScroll, watchThrottled } from '@vueuse/core';
import { getHeadingInView } from '@/lib';

interface IProps {
  data: { name: string; id: string }[];
}

defineOptions({
  name: 'Anchor',
});
const props = defineProps<IProps>();
const { y } = useScroll(document.getElementById('main-content')!);
const active = ref(props.data[0].id);

watchThrottled(y, () => {
  const activeId = getHeadingInView(props.data.map(v => v.id));
  if (activeId) {
    active.value = activeId;
  }
}, { throttle: 200 });
function clickHandler(id: string) {
  active.value = id;
  const offsetTop = document.getElementById(id)?.offsetTop ?? 0;
  document.getElementById('main-content')!.scrollTo({ top: offsetTop - 80 });
}
</script>

<template>
  <div class="wrap">
    <div
      v-for="item in data"
      :key="item.id"
      class="item"
      :class="{ active: active === item.id }"
      @click="() => clickHandler(item.id)"
    >
      {{ item.name }}
    </div>
  </div>
</template>

<style scoped lang="less">
.wrap {
  padding: 4px 12px;
  height: 32px;
  font-size: 14px;
  color: #939cae;
  display: flex;
  border-radius: 4px;
  background: #fff;
  .item {
    margin-right: 32px;
    position: relative;
    cursor: pointer;
    &:last-child {
      margin-right: 0;
    }
    &:not(:last-child):after {
      position: absolute;
      content: '/';
      font-size: 0;
      top: 5px;
      right: -15px;
      height: 12px;
      border-left: 1px solid #ccc;
    }
  }
  .active {
    color: #15233f;
    font-weight: bold;
  }
}
</style>
