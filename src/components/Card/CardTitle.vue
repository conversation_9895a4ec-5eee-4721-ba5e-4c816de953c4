<script setup lang="ts">
import type { CSSProperties } from 'vue';
import questionIcon from '@/assets/icon/question.png';
import { ArrowRightBold } from '@element-plus/icons-vue';
import { ElIcon, ElTooltip } from 'element-plus';
import { useAttrs } from 'vue';

export interface CardTitleProps {
  /** 标题 */
  title: string;
  /** 副标题 */
  subTitle?: string;
  action?: string;
  tips?: string;
}

defineProps<CardTitleProps>();

const emit = defineEmits<{
  click: [];
}>();

const attrs = useAttrs() as { class: string; style: CSSProperties };

function handleAction() {
  emit('click');
}
</script>

<template>
  <div class="flex items-center justify-between">
    <div class="flex items-center">
      <div class="mr-12 h-16 w-6 bg-[#2E6BE6]" />
      <div
        class="cursor-pointer text-base font-medium leading-snug text-[#15233F]"
        :class="attrs.class"
        :style="attrs.style"
      >
        {{ title }}
      </div>
      <ElTooltip v-if="tips" placement="top">
        <img svg class="ml-6 size-12 cursor-pointer" :src="questionIcon">
        <template #content>
          <slot name="tipContent">
            {{ tips }}
          </slot>
        </template>
      </ElTooltip>
      <div v-if="subTitle" class="ml-6 text-xs text-[#7A8599]">
        {{ subTitle }}
      </div>
    </div>
    <div
      v-if="action"
      class="cursor-pointer text-sm text-[#2E6BE6]"
      @click="handleAction"
    >
      {{ action }}
      <ElIcon size="9">
        <ArrowRightBold />
      </ElIcon>
    </div>
  </div>
</template>
