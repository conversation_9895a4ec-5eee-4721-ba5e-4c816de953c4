<script setup lang="ts">
import CardTitle from '@/components/Card/CardTitle.vue';

export type SourceCardProps = {
  title?: string;
  subTitle?: string;
  actionText?: string;
};

defineProps<SourceCardProps>();
const emit = defineEmits<{ action: [] }>();
</script>

<template>
  <div class="flex flex-col gap-12 rounded-md bg-white px-16 py-12">
    <CardTitle
      v-if="title"
      :title="title"
      :sub-title="subTitle"
      :action="actionText"
      @click="() => emit('action')"
    />
    <slot />
  </div>
</template>
