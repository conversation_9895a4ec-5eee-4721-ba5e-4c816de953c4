<script setup lang="ts">
import { isEmpty } from 'lodash-es';
import { computed, useSlots, watch } from 'vue';

export interface TabsProps {
  contentClass?: string;
}

defineOptions({
  name: 'Tabs',
});

const { contentClass } = defineProps<TabsProps>();
const slots = useSlots();

// 获取所有的 TabPane 组件
const panes = computed(() => {
  const defaultSlot = slots.default?.() || [];
  return defaultSlot
    .filter(node => (node.type as any)?.name === 'TabPane')
    .map(node => ({
      label: node.props?.label || '',
      name: node.props?.name || '',
      disabled: node.props?.disabled || false,
      content: node,
    }));
});

const active = defineModel<string>();

const activeTab = computed(() => {
  return panes.value.find(pane => pane.name === active.value);
});

watch(
  active,
  (val) => {
    if (!val && !isEmpty(panes.value)) {
      active.value = panes.value[0].name;
    }
  },
  { immediate: true },
);

function handleClick(name: string, disabled: boolean) {
  if (disabled)
    return;
  active.value = name;
}
</script>

<template>
  <div class="flex h-full flex-col">
    <ul class="z-10 flex flex-wrap text-center">
      <li
        v-for="(pane, index) in panes"
        :key="pane.name"
        class="me-0"
        @click="handleClick(pane.name, pane.disabled)"
      >
        <span
          aria-current="page"
          class="grid h-40 cursor-pointer place-content-center border border-[#DCDFE6] bg-[#F7F8FA] px-24 py-10 text-[#3A4762]"
          :class="{
            'border-l-0': index !== 0,
            'rounded-tl-md': index === 0,
            'rounded-tr-md': index === panes.length - 1,
            'border-b-white bg-white': active === pane.name,
            'cursor-not-allowed opacity-60': pane.disabled,
          }"
        >
          {{ pane.label }}
        </span>
      </li>
    </ul>

    <div class="min-h-0 flex-1 overflow-y-hidden bg-white py-16" :class="contentClass" :style="{ height: 'calc(100% - 32px)' }">
      <div class="relative h-full overflow-y-scroll">
        <keep-alive>
          <component :is="activeTab?.content" :key="activeTab?.name" />
        </keep-alive>
      </div>
    </div>
  </div>
</template>
