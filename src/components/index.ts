import BarLineChart from '@/components/BarLineChart/index.vue';
import BasicLineChart from '@/components/BasicLineChart/index.vue';
import CardTitle from '@/components/Card/CardTitle.vue';
import SourceCard from '@/components/Card/SourceCard.vue';
import ChangePasswordDialog from '@/components/ChangePasswordDialog/index.vue';
import ChinaMap from '@/components/ChinaMap/index.vue';
import Comparison from '@/components/Comparission/index.vue';
import DoughnutChart from '@/components/DoughnutChart/index.vue';
import LineChart from '@/components/LineChart/index.vue';
import MatrixChart from '@/components/MatrixChart/index.vue';
import DepartmentTree from '@/components/OrganizationTree/DepartmentTree.vue';
import HospitalTree from '@/components/OrganizationTree/HospitalTree.vue';
import PeopleDrawer from '@/components/PeopleDrawer/index.vue';
import RadioGroup from '@/components/RadioGroup/index.vue';
import StackedAreaChart from '@/components/StackedAreaChart/index.vue';
import StatisticText from '@/components/StatisticText/index.vue';
import Table from '@/components/Table/index.vue';
import InfiniteTable from '@/components/Table/InfiniteTable.vue';
import Tabs from '@/components/Tabs/index.vue';
import TabPane from '@/components/Tabs/TabPane.vue';
import DatePicker from '@/components/Times/DatePicker.vue';
import Times from '@/components/Times/index.vue';
import HrtVirtualizedTable from '@/components/VirtualizedTable/index.vue';

export {
  BarLineChart,
  BasicLineChart,
  CardTitle,
  ChangePasswordDialog,
  ChinaMap,
  /** 同比环比 */
  Comparison,
  DatePicker,
  DepartmentTree,
  DoughnutChart,
  HospitalTree,
  HrtVirtualizedTable,
  InfiniteTable,
  LineChart,
  MatrixChart,
  PeopleDrawer,
  RadioGroup,
  SourceCard,
  StackedAreaChart,
  StatisticText,
  Table,
  TabPane,
  Tabs,
  Times,
};
