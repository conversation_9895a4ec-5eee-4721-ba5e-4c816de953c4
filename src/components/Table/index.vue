<script setup lang="ts">
import { ElTable } from 'element-plus';

export interface TableProps {
  data?: Record<string, unknown>[];
}

const { data = [] } = defineProps<TableProps>();
const emit = defineEmits<{
  cellClick: [row: Record<string, unknown>];
}>();

function handleCellClick(event) {
  emit('cellClick', event);
}
</script>

<template>
  <ElTable
    :data="data"
    header-row-class-name="bg-[#F7F8FA] text-left"
    header-cell-class-name="text-sm bg-[#F7F8FA] text-[#15233F] px-12 py-16"
    row-class-name="text-sm text-[#3A4762] border-t border-[#E1E5ED] cursor-pointer overflow-x-auto"
    cell-class-name="px-12 py-16"
    class="overflow-x-hidden"
    @cell-click="handleCellClick"
  >
    <slot />
  </ElTable>
</template>

<style lang="css">
.el-table th.el-table__cell,
.el-table__header-wrapper tr th.el-table-fixed-column--left,
.el-table__header-wrapper tr th.el-table-fixed-column--right,
.el-table.is-scrolling-left th.el-table-fixed-column--left,
.el-table.is-scrolling-right th.el-table-fixed-column--right {
  background-color: #f7f8fa !important;
}
</style>
