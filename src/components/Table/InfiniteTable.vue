<script setup lang="ts">
import type { TableProps } from 'element-plus';
import { useInfiniteScroll } from '@vueuse/core';
import { ElTable } from 'element-plus';
import { useTemplateRef } from 'vue';

export interface InfiniteTableProps {
  /** 触发加载的距离阈值，单位为px */
  infiniteScrollDistance?: number;
  height?: number;
  /** 数据获取函数 */
  queryFn?: (params: { pageNumber: number; pageSize: number }) => Promise<{ total: number; contents: Array<any> | null }>;
  rowClassName?: TableProps<any>['rowClassName'];
}

const {
  infiniteScrollDistance = 10,
  height = 300,
  queryFn,
  rowClassName,
} = defineProps<InfiniteTableProps>();
const emit = defineEmits<{
  cellClick: [row: any];
  /** 加载更多数据 */
  load: [];
}>();

const tableRef = useTemplateRef<HTMLElement>('table');
const data = defineModel<any[]>({ default: [] });
const page = defineModel<number>('page', { default: 1 });
const pageSize = defineModel<number>('pageSize', { default: 10 });
const total = defineModel<number>('total', { default: 0 });
const hasMore = ref<boolean>(true);

const { reset } = useInfiniteScroll(tableRef, load, {
  distance: infiniteScrollDistance,
  canLoadMore: () => {
    return hasMore.value;
  },
  interval: 500,
});

function handleCellClick(row: any) {
  emit('cellClick', row);
}

/**
 * 加载更多数据
 */
function load() {
  if (data.value.length >= total.value && total.value > 0) {
    return;
  }
  // 加载更多数据的逻辑
  queryFn?.({ pageNumber: page.value, pageSize: pageSize.value }).then((res) => {
    if (res) {
      data.value = data.value.concat(res.contents || []);
      page.value += 1;
      total.value = res.total;
      hasMore.value = res.total > 0 && data.value.length <= res.total;
    }
  }).catch(() => {
    hasMore.value = false;
  });
}

/** 清空表格数据 */
function clear() {
  data.value = [];
  page.value = 1;
  total.value = 0;
  hasMore.value = true;
  reset();
}

defineExpose({
  clear,
});
</script>

<template>
  <div ref="table" class="overflow-y-scroll" :style="{ height: `${height}px` }">
    <ElTable
      :data="data"
      :row-class-name="rowClassName ?? 'text-sm text-[#3A4762] border-t border-[#E1E5ED] cursor-pointer overflow-x-auto'"
      cell-class-name="px-12 py-16"
      class="overflow-x-hidden"
      header-cell-class-name="text-sm bg-[#F7F8FA] text-[#15233F] px-12 py-16"
      header-row-class-name="bg-[#F7F8FA] text-left"
      @cell-click="handleCellClick"
    >
      <slot />
    </ElTable>
  </div>
</template>

<style lang="css">
.el-table th.el-table__cell,
.el-table__header-wrapper tr th.el-table-fixed-column--left,
.el-table__header-wrapper tr th.el-table-fixed-column--right,
.el-table.is-scrolling-left th.el-table-fixed-column--left,
.el-table.is-scrolling-right th.el-table-fixed-column--right {
  background-color: #f7f8fa !important;
}
</style>
