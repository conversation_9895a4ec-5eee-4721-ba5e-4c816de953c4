<script setup lang="ts">
import { useTransition } from '@vueuse/core';
import { computed, ref } from 'vue';

export interface StatisticTextProps {
  title: string;
  value: number | string;
  /** 是否开启动画 */
  animation?: boolean;
  size?: 'small' | 'default' | 'large';
}
defineOptions({
  name: 'StatisticText',
});
const {
  title,
  value,
  animation = false,
  size = 'default',
} = defineProps<StatisticTextProps>();
const source = ref(0);
const output = useTransition(source, {
  duration: 1500,
});
if (typeof value === 'number') {
  source.value = value;
}

const outputValue = computed(() => output.value.toFixed(0));
/** 是否展示动画效果 */
const showAnimation = computed(() => animation && typeof value === 'number');
</script>

<template>
  <div class="flex flex-col text-[#3A4762]">
    <div class="mb-6 text-sm font-normal">
      {{ title }}
    </div>
    <div
      class="font-[DIN-Bold] leading-1"
      :class="{
        'text-[26px]': size === 'default',
        'text-[32px]': size === 'large',
        'text-lg': size === 'small',
      }"
    >
      <slot name="prefix" />
      <span>{{ showAnimation ? outputValue : value }}</span>
      <slot name="suffix" />
    </div>
  </div>
</template>
