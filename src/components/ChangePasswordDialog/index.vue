<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus';
import { useLocalStorage } from '@vueuse/core';
import { ElButton, ElDialog, ElForm, ElFormItem, ElInput, ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { editPassword } from '@/api';

defineOptions({
  name: 'ChangePasswordDialog',
});
const router = useRouter();

interface RuleForm {
  oldPassword: string;
  newPassword: string;
  sureNewPassword: string;
}
const ruleFormRef = ref<FormInstance>();
// 修改密码数据展示
const form = reactive<RuleForm>({
  oldPassword: '',
  newPassword: '',
  sureNewPassword: '',
});

const osUserId = useLocalStorage('DATA_PLATFORM_USERID', null, {
  listenToStorageChanges: true,
  serializer: {
    read: v => v ? Number(v) : null,
    write: v => v ? String(v) : '',
  },
});

const open = defineModel({ default: false });
const rules = reactive<FormRules<RuleForm>>({
  oldPassword: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!value) {
          callback(new Error('请输入新密码'));
        }
        else if (!validatePassword(value)) {
          callback(new Error('密码格式错误，正确应为：8-15位数的大小写英文、数字及特殊符号任意三种！'));
        }
        else if (value === form.oldPassword) {
          callback(new Error('新密码和旧密码不能一样！'));
        }
        else {
          callback();
        }
      },
      trigger: 'blur',
    },
  ],
  sureNewPassword: [
    { required: true, message: '请输入确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!value) {
          callback(new Error('请输入确认新密码'));
        }
        else if (value !== form.newPassword) {
          callback(new Error('两次新密码不一致！'));
        }
        else {
          callback();
        }
      },
      trigger: 'blur',
    },
  ],
});

// 提醒消息
function warnMsg(msg: string, style: string) {
  const obj = {
    showClose: true,
    message: msg || '请检查是否完成！',
    type: style || 'error',
  };
  const newObj = obj as any;
  ElMessage(newObj);
};

function validatePassword(password: string) {
  const regex = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[\s\S]{8,15}$/;
  return regex.test(password);
};

// 修改密码
function editOldPassword() {
  editPassword({
    oldPwd: form.oldPassword,
    newPwd: form.newPassword,
    osUserId: osUserId.value ?? 0,
  }).then((res: any) => {
    if (res) {
      warnMsg('修改密码成功,即将退出重新登录！', 'success');
      localStorage.clear();
      sessionStorage.clear();
      resetForm(ruleFormRef.value);
      setTimeout(() => {
        router.push('/login');
      }, 1000);
    }
    else {
      warnMsg(res.message, '');
    }
  });
};

async function submitForm(formEl: FormInstance | undefined) {
  if (!formEl)
    return;
  await formEl.validate((valid) => {
    if (valid) {
      editOldPassword();
    }
  });
}

function resetForm(formEl: FormInstance | undefined) {
  if (!formEl)
    return;
  formEl.resetFields();
  open.value = false;
}
</script>

<template>
  <ElDialog
    v-model="open"
    width="400px"
    title="修改密码"
    @close="resetForm(ruleFormRef)"
  >
    <ElForm ref="ruleFormRef" :model="form" :label-width="95" :rules="rules">
      <ElFormItem label="旧密码" prop="oldPassword">
        <ElInput
          v-model.number="form.oldPassword"
          placeholder="请输入旧密码"
          clearable
          minlength="8"
        />
      </ElFormItem>
      <ElFormItem label="新密码" prop="newPassword">
        <ElInput
          v-model.number="form.newPassword"
          placeholder="请输入新密码"
          clearable oninput="value = value.replace(/\s+/g,'')"
          minlength="8"
        />
      </ElFormItem>
      <ElFormItem label="确认新密码" prop="sureNewPassword">
        <ElInput
          v-model.number="form.sureNewPassword"
          placeholder="请再次输入新密码"
          clearable oninput="value = value.replace(/\s+/g,'')"
          minlength="8"
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="flex items-center justify-end">
        <ElButton @click="resetForm(ruleFormRef)">
          取消
        </ElButton>
        <ElButton
          type="primary"
          @click="submitForm(ruleFormRef)"
        >
          确定
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>
