<template>
  <div class="flex h-full w-full">
    <Card
      :title="title"
      :vertical="vertical"
      :bold="bold"
      :more-text="moreText"
      :mt="mt"
      @click-more="clickMoreHandler"
    >
      <div
        class="shrink-0"
        :style="{
          width: chartWidth + 'px',
          height: chartWidth + 'px',
        }"
      >
        <BaseChart type="pie" :data-complete="true" :options="pieOptions" />
      </div>
      <el-scrollbar v-if="legendListData?.length > 0" :height="legendHeight">
        <div class="legend" :style="{ marginLeft: legendMarginLeft + 'px' }">
          <div
            v-for="(item, index) in legendListData"
            :key="index + '_' + item.name + '_' + item.value"
            class="legend-item"
            :style="{ width: legendItemWidth + (legendLabelWidth - 86) + 'px' }"
          >
            <span
              class="circle"
              :style="{ background: allColors[index] }"
            ></span>
            <div class="flex flex-1">
              <span class="label" :style="{ maxWidth: legendLabelWidth + 'px' }"
                ><Text>{{ item.name }}</Text>
              </span>
              <span class="value">（{{ item.value }}） </span>
            </div>
            <span class="percent">{{ item.percent?.toFixed(2) }}%</span>
          </div>
        </div>
      </el-scrollbar>
      <div v-else class="empty">暂无数据</div>
    </Card>
  </div>
</template>

<script setup lang="ts">
import BaseChart from '@/components/BaseChart/BaseChart.vue';
import Card from '@/components/Card/index.vue';
import Text from '@/components/Text/index.vue';

import {
  COLORS_BASE,
  COLORS_MORE,
  getTooltipConfig,
  IChartCommonProps,
} from '@/components/BaseChart/options';
import { getPieEchartsOptions } from '@/components/BaseChart/options/pie';

interface IProps extends IChartCommonProps {
  title: string;
  bold?: boolean;
  chartWidth?: number;
  innerText?: string | number;
  mt?: string;
  vertical?: boolean;
  legendHeight?: number;
  legendLabelWidth?: number;
  legendMarginLeft?: number;
  legendItemWidth?: number;
  textTop?: string;
  moreText?: string;
}
const emit = defineEmits(['click-more']);
const props = withDefaults(defineProps<IProps>(), {
  chartWidth: 160,
  innerText: '',
  bold: true,
  vertical: false,
  mt: '24px',
  textTop: '45%',
  legendLabelWidth: 98,
  legendMarginLeft: 30,
  legendItemWidth: 240,
  moreText: '',
  legendHeight: 0,
});
const allColors = ([...COLORS_BASE, ...COLORS_MORE].join(' ') + ' ')
  .repeat(3)
  .slice(0, -1)
  .split(' ');
const pieOptions = ref<any>({});

const legendListData = computed(() => pieOptions.value.series?.[0]?.data ?? []);

const getOptions = () => {
  return getPieEchartsOptions({
    innerText: props.innerText,
    textTop: props.textTop,
    tooltip: {
      ...getTooltipConfig({
        transform: params => {
          return {
            items: [{ label: params.name, value: params.value }],
          };
        },
      }),
      trigger: 'item',
      position: function (pos) {
        return [pos[0], pos[1] - 60];
      },
    },
    seriesConfig: [
      {
        data: props.data,
        radius: ['60%', '90%'],
      },
    ],
  });
};

watch(
  () => props.data,
  () => {
    pieOptions.value = getOptions();
  },
  { immediate: true }
);
const clickMoreHandler = () => {
  emit('click-more');
};
defineOptions({
  name: 'PieChart',
});
</script>

<style scoped lang="less">
.left {
  width: 50%;
  height: 100%;
}
.legend {
  width: calc(100% - 30px);
  margin-left: 30px;
  margin-top: 4px;
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  font-weight: normal;
  color: #3a4762;
  .legend-item {
    display: flex;
    align-items: center;
    width: 240px;
    margin-bottom: 8px;
    // &:nth-child(odd) {
    margin-right: 24px;
    // }
    .label {
      max-width: 86px;
    }
    .value {
      width: 60px;
      margin: 0 6px 0 2px;
    }
    .percent {
      flex-shrink: 0;
    }
  }
  .circle {
    display: inline-block;
    width: 10px;
    height: 10px;
    background: #ccc;
    border-radius: 100%;
    margin-right: 6px;
  }
}
.empty {
  color: #b8becc;
  font-size: 20px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
</style>
