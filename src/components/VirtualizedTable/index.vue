<script setup lang="ts">
import type { Column } from 'element-plus';
import { useQuery } from '@tanstack/vue-query';
import { ElAutoResizer, ElTableV2 } from 'element-plus';
import { isEmpty } from 'lodash-es';
import { computed, ref, useId, watch } from 'vue';
import { z } from 'zod';

defineOptions({
  name: 'HrtVirtualizedTable',
});

const { height = 300, columns, queryFn, pageSize = 10 } = defineProps<HrtVirtualizedTableProps>();

const emit = defineEmits<{
  /** 第一次获取到数据时抛出 */
  firstDataChange: [data: any[]];
}>();

export interface HrtVirtualizedTableProps {
  /** 虚拟表格高度（默认为300px） */
  height?: number;
  /** 表格列配置 */
  columns: Column[];
  /** 数据获取函数 */
  queryFn: (params: { current: number; pageSize: number }) => Promise<Res>;
  /** 分页大小 */
  pageSize?: number;
}

const ResSchema = z.object({
  success: z.boolean(),
  total: z.int(),
  data: z.array(z.any()),
});

type Res = z.infer<typeof ResSchema>;

const page = ref<number>(1);
const id = useId();
/** 请求是否可用 */
const enabled = computed(() => !!queryFn);
/** 表格数据 */
const tableData = ref<any[]>([]);

const { data } = useQuery({
  queryKey: ['virtualized-table', id, page],
  queryFn: () => queryFn?.({
    current: page.value,
    pageSize,
  }),
  initialData: { data: [], success: true, total: 0 },
  enabled,
});

watch(data, (newData) => {
  const result = ResSchema.safeParse(newData);
  if (result.success && result.data.success) {
    /** 判断数据获取成功并且之前表格没有数据，则将第一次获取到的数据抛出 */
    if (!isEmpty(result.data.data) && isEmpty(tableData.value)) {
      emit('firstDataChange', result.data.data);
    }
    tableData.value = tableData.value.concat(newData?.data ?? []);
    // 当前数据的条数小于总条数页码加1
    if (tableData.value.length < result.data.total) {
      page.value += 1;
    }
  }
});

/**
 * 清除表格数据📊
 */
function clear() {
  tableData.value = [];
  page.value = 1;
}

defineExpose({
  clear,
});
</script>

<template>
  <ElAutoResizer :style="{ height: `${height}px` }">
    <template #default="{ height: wrapperHeight, width }">
      <ElTableV2 :data="tableData ?? []" :columns="columns" :width="width" :height="wrapperHeight" />
    </template>
  </ElAutoResizer>
</template>

<style scoped lang="css">
:deep(.el-table-v2) {
  .el-table-v2__header-cell {
    background-color: #f7f8fa;
    color: #15233f;
    font-size: 14px;
  }
}
</style>
