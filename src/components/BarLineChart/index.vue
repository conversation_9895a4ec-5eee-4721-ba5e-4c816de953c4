<script setup lang="ts">
import type {
  BarSeriesOption,
  EChartsOption,
  GridComponentOption,
  LineSeriesOption,
  XAXisComponentOption,
  YAXisComponentOption,
} from 'echarts';
import { Bar<PERSON><PERSON>, LineChart } from 'echarts/charts';
import {
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { isEmpty } from 'lodash-es';
import { computed, useAttrs } from 'vue';
import VChart from 'vue-echarts';

defineOptions({
  name: 'BarL<PERSON><PERSON><PERSON>',
});

const {
  series = [],
  colors = ['#5285EB', '#9FBCF5', '#2FB324', '#E37221'],
  xAxis = {},
  yAxis = {},
  title = '',
  grid = {},
  showLegend = true,
  height,
  subTitle,
  tooltip = {},
} = defineProps<IProps>();

use([
  Can<PERSON><PERSON><PERSON><PERSON>,
  LineChart,
  Bar<PERSON>hart,
  GridComponent,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
]);

interface IProps {
  /** 图表名称 */
  title?: string;
  /** 图表副标题 */
  subTitle?: string;
  /** 图表高度 */
  height?: number;
  /** 是否展示图例 */
  showLegend?: boolean;
  series?: Array<LineSeriesOption | BarSeriesOption>;
  colors?: string[];
  xAxis?: XAXisComponentOption;
  yAxis?: YAXisComponentOption | YAXisComponentOption[];
  grid?: GridComponentOption;
  tooltip?: EChartsOption['tooltip'];
}

const attrs = useAttrs();

// 类型定义
interface ChartDataItem {
  month: string;
  value: number;
}

export interface LineChartProps {
  data?: ChartDataItem[];
}

// 默认配置
const DEFAULT_CHART_CONFIG = {
  grid: {
    top: 15,
    left: 30,
    right: 30,
    bottom: 30,
    containLabel: true,
  },
};

const isEmptyChart = computed(() => {
  return series.every(item => isEmpty(item.data));
});

// 图表配置
const options = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    ...tooltip,
  },
  grid: {
    ...DEFAULT_CHART_CONFIG.grid,
    ...grid,
  },
  xAxis: {
    type: 'category',
    axisLine: {
      lineStyle: {
        color: '#E1E5ED',
      },
    },
    axisLabel: {
      color: '#7A8599',
      fontSize: 12,
    },
    axisTick: {
      show: false,
    },
    ...xAxis,
  },
  yAxis: Array.isArray(yAxis)
    ? yAxis.map(item => ({
        type: 'value',
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#E1E5ED',
          },
        },
        axisLabel: {
          color: '#7A8599',
          fontSize: 12,
        },
        ...item,
      }))
    : {
        type: 'value',
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#E1E5ED',
          },
        },
        axisLabel: {
          color: '#7A8599',
          fontSize: 12,
        },
        ...yAxis,
      },
  series: series.map((item, index) => {
    return {
      color: colors[index] ?? '#9FBCF5',
      symbol: 'none',
      ...item,
    };
  }),
}));
</script>

<template>
  <div v-if="title" class="mb-16 flex items-center gap-24">
    <span class="text-base font-medium text-[#15233F]">{{ title }}</span>
    <span v-if="subTitle" class="-ml-4 text-sm text-[#939CAE]">
      {{ subTitle }}
    </span>
    <div v-if="showLegend" class="flex items-center gap-24">
      <div
        v-for="(item, index) in series"
        :key="item.name"
        class="inline-flex items-center gap-8 text-sm text-[#939CAE]"
      >
        <div
          class="size-12 rounded-sm"
          :style="{
            backgroundColor: typeof item.color === 'string' ? item.color : colors[index],
          }"
        />
        {{ item.name }}
      </div>
    </div>
  </div>
  <div
    v-if="isEmptyChart"
    class="grid place-items-center"
    :class="attrs.class"
    :style="{
      width: '100%',
      height: height ? `${height}px` : '100%',
      minHeight: '120px',
    }"
  >
    <p class="text-[#939CAE]">
      暂无数据
    </p>
  </div>
  <VChart
    v-else
    :option="options"
    :style="{
      width: '100%',
      height: height ? `${height}px` : '100%',
      minHeight: '120px',
    }"
    autoresize
    :class="attrs.class"
  />
</template>
