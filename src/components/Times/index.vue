<template>
  <div class="wrap">
    <div class="main item-center flex" :class="{ hasSlot: hasSlot }">
      <div v-if="label" class="label" :class="{ bold: bold }">{{ label }}</div>
      <template v-for="item in curConfigs" :key="item.key">
        <span
          v-if="item.type !== 'custom'"
          :class="['item', activeKey === item.key ? 'active' : '']"
          @click="() => clickHandler(item)"
          >{{ item.label }}</span
        >
        <div v-else>
          <el-date-picker
            v-if="item.subType === 'datePicker'"
            v-model="dateMap[item.key]"
            :clearable="false"
            :type="item.dateType"
            :value-format="item.valueFormat ?? 'YYYY-MM-DD'"
            :format="item.format ?? 'YYYY-MM'"
            :disabled-date="val => disableDateHand<PERSON>(item, val)"
            @change="item.getValue"
            @visible-change="visibleChangeHandler"
          />
          <el-select
            v-if="item.subType === 'select'"
            v-model="dateMap[item.key]"
            placeholder="请选择"
            style="width: 240px"
            @change="item.getValue"
          >
            <el-option
              v-for="v in item.options"
              :key="v.id"
              :label="v.name"
              :value="v.id"
            />
          </el-select>
        </div>
      </template>
    </div>
    <div class="extra">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCurrentMonth, getCurrentYear, getLastYear } from '@/lib';
import dayjs from 'dayjs';

interface IProps {
  label?: string;
  bold?: boolean;
  enableKeys?: string[];
}

const props = withDefaults(defineProps<IProps>(), {
  label: () => '',
  enableKeys: () => ['latestYear', 'year', 'month', 'monthrange'],
});
const formatString = 'YYYY-MM-DD';
const getDateRange = ({ type, val = [], now = false }) => {
  const start = dayjs(val?.[0]).startOf(type).format(formatString);
  const end = now
    ? dayjs(val?.[1]).format(formatString)
    : dayjs(val?.[1]).endOf(type).format(formatString);
  return [start, end];
};

/**
 * 时间范围的快捷选择项
 */
const shortcuts = [
  {
    label: '近一年',
    key: 'latestYear',
    getValue: () => ({ key: 'monthrange', val: getLastYear() }),
  },
  {
    label: '本年',
    key: 'year',
    getValue: () => ({
      key: 'monthrange',
      val: getCurrentYear(),
    }),
  },
  {
    label: '本月',
    key: 'month',
    getValue: (key: string) => {
      return {
        key: key ?? 'monthrange',
        val:
          key === 'monthrange' ? getCurrentMonth() : dayjs().format('YYYY-MM'),
      };
    },
  },
  {
    label: '今日',
    key: 'today',
    getValue: () => ({
      key: 'date',
      val: dayjs().format(formatString),
    }),
  },
  {
    label: '一年内',
    key: 'duration-year',
    getValue: () => ({
      key: 'custom-year',
      val: 1,
    }),
  },
];

const configs = [
  ...shortcuts,
  {
    type: 'custom',
    subType: 'datePicker',
    dateType: 'monthrange',
    label: 'none',
    key: 'monthrange',
    getValue: val => {
      activeKey.value = 'monthrange';
      const isNowMonth = dayjs().isSame(val[1], 'month');
      const _val = isNowMonth ? [val[0], Date.now()] : val;
      const res = getDateRange({ type: 'month', val: _val, now: isNowMonth });
      const diff = dayjs(res[1]).diff(res[0], 'month');
      if (diff >= 12) {
        ElMessage.warning('日期范围不能超过一年！');
        dateMap.value.monthrange = temp.value;
      } else {
        dateMap.value.monthrange = res;
        emit('date-change', res);
      }
    },
  },
  {
    type: 'custom',
    subType: 'datePicker',
    dateType: 'month',
    format: 'YYYY-MM',
    valueFormat: 'YYYY-MM',
    label: 'none',
    key: 'customMonth',
    getValue: val => {
      activeKey.value = 'customMonth';
      dateMap.value.customMonth = val;
      emit('date-change', val);
    },
  },
  {
    type: 'custom',
    subType: 'datePicker',
    dateType: 'date',
    label: 'none',
    key: 'date',
    format: 'YYYY-MM-DD',
    valueFormat: 'YYYY-MM-DD',
    getValue: val => {
      activeKey.value = 'date';
      dateMap.value.date = val;
      emit('date-change', val);
    },
  },
  {
    type: 'custom',
    subType: 'select',
    label: 'none',
    key: 'custom-year',
    options: Array(10)
      .fill(0)
      .map((_, i) => ({ id: i + 1, name: i + 1 + '年内' })),
    getValue: val => {
      activeKey.value = 'custom-year';
      dateMap.value.customyear = val;
      emit('date-change', val);
    },
  },
];

const curConfigs = computed(() => {
  return configs.filter(v => props.enableKeys.includes(v.key));
});
const hasSlot = computed(() => {
  const slots = useSlots();
  return !!slots?.default;
});
const emit = defineEmits(['date-change']);
const activeKey = ref(
  curConfigs.value?.[2] ? curConfigs.value[2].key : curConfigs.value[0].key
);
const dateMap = ref({
  monthrange: getLastYear(),
  customyear: 1,
  customMonth: dayjs().format('YYYY-MM'),
  date: dayjs().format('YYYY-MM-DD'),
});
const temp = ref();

const disableDateHandler = (item, val) => {
  const { dateType } = item;
  if (['monthrange'].includes(item.dateType)) {
    return val > dayjs().endOf('month').valueOf();
  } else if (['date'].includes(dateType)) {
    return val > dayjs().endOf('d').valueOf();
  }
};
const visibleChangeHandler = val => {
  if (val) {
    temp.value = dateMap.value.monthrange;
  }
};
const clickHandler = item => {
  activeKey.value = item.key;
  let relativeKey = 'monthrange';
  if (props.enableKeys.includes('customMonth')) {
    relativeKey = 'customMonth';
  }
  const { key, val } = item.getValue(relativeKey);
  dateMap.value[key] = val;
  emit('date-change', val);
};

onMounted(() => {
  const item = curConfigs.value.find(v => v.key === activeKey.value);
  clickHandler(item);
});

defineOptions({
  name: 'Times',
});
</script>

<style scoped lang="less">
.wrap {
  display: flex;
  .item {
    cursor: pointer;
    min-width: 66px;
    height: 32px;
    line-height: 32px;
    padding: 0 12px;
    text-align: center;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #dcdfe6;
    font-size: 14px;
    color: #3a4762;
    &.active {
      color: #2e6be6;
      border-color: #2e6be6;
      background-color: #e6eeff;
    }
  }
  .main {
    > * {
      margin-right: 16px;
    }
  }
  .hasSlot {
    position: relative;
    margin-right: 80px;
    &:after {
      position: absolute;
      content: '';
      right: -40px;
      top: 4px;
      border-right: 1px solid #b8becc;
      height: 24px;
    }
  }
}
.label {
  color: #3a4762;
  line-height: 32px;
}
.bold {
  font-weight: bold;
}
</style>
