<script setup lang="ts">
import dayjs from 'dayjs';
import { getCurrentMonth, getCurrentYear, getLastYear } from '@/lib';

interface DatePickerProps {
  enableKeys?: string[];
  defaultDate?: string;
}

defineOptions({
  name: 'DatePicker',
});

const {
  enableKeys = ['latestYear', 'year', 'month'],
  defaultDate = 'year',
} = defineProps<DatePickerProps>();

const emit = defineEmits<{
  dateChange: [value: [string, string]];
}>();

const startTime = defineModel<number>('startTime');

const endTime = defineModel<number>('endTime');

const date = ref<[string, string]>();

const formatString = 'YYYY-MM-DD';

function getDateRange({ type, val = [], now = false }) {
  const start = dayjs(val?.[0]).startOf(type).format(formatString);
  const end = now
    ? dayjs(val?.[1]).format(formatString)
    : dayjs(val?.[1]).endOf(type).format(formatString);
  return [start, end];
}

/**
 * 时间范围的快捷选择项
 */
const shortcuts = [
  {
    label: '近一年',
    key: 'latestYear',
    getValue: () => ({ key: 'monthrange', val: getLastYear() }),
    start: dayjs().subtract(11, 'month').startOf('month').format('YYYY-MM-DD HH:mm:ss'),
    end: dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    label: '本年',
    key: 'year',
    getValue: () => ({
      key: 'monthrange',
      val: getCurrentYear(),
    }),
    start: dayjs().startOf('year').format('YYYY-MM-DD HH:mm:ss'),
    end: dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    label: '本月',
    key: 'month',
    getValue: (key: string) => {
      return {
        key: key ?? 'monthrange',
        val: getCurrentMonth(),
      };
    },
    start: dayjs().startOf('month').valueOf(),
    end: dayjs().endOf('month').valueOf(),
  },
  {
    label: '今日',
    key: 'today',
    getValue: () => ({
      key: 'date',
      val: dayjs().format(formatString),
    }),
    start: dayjs().startOf('day').valueOf(),
    end: dayjs().endOf('day').valueOf(),
  },
];

const curConfigs = computed(() => {
  return shortcuts.filter(v => enableKeys.includes(v.key));
});
const hasSlot = computed(() => {
  const slots = useSlots();
  return !!slots?.default;
});
const activeKey = ref(
  curConfigs.value?.[2] ? curConfigs.value[2].key : curConfigs.value[0].key,
);
const dateMap = ref({
  monthrange: getLastYear(),
  customyear: 1,
  customMonth: dayjs().format('YYYY-MM'),
  date: dayjs().format('YYYY-MM-DD'),
});
const temp = ref();

function clickHandler(item) {
  activeKey.value = item.key;
  let relativeKey = 'monthrange';
  if (enableKeys.includes('customMonth')) {
    relativeKey = 'customMonth';
  }
  const { key, val } = item.getValue(relativeKey);
  dateMap.value[key] = val;
  date.value = val as [string, string];
  emit('dateChange', val as [string, string]);
}

function handleDateChange(val: [string, string]) {
  const isNowMonth = dayjs().isSame(val[1], 'month');
  const _val = isNowMonth ? [val[0], Date.now()] : val;
  const res = getDateRange({ type: 'month', val: _val, now: isNowMonth });
  const diff = dayjs(res[1]).diff(res[0], 'month');
  if (diff >= 12) {
    ElMessage.warning('日期范围不能超过一年！');
    dateMap.value.monthrange = temp.value;
  }
  else {
    dateMap.value.monthrange = res;
    emit('dateChange', res as [string, string]);
  }
}

watch(date, (val) => {
  if (val) {
    startTime.value = dayjs(val?.[0]).startOf('month').valueOf();
    endTime.value = dayjs(val?.[1]).endOf('month').valueOf();
    emit('dateChange', val as [string, string]);
  }
}, { immediate: true });

onMounted(() => {
  switch (defaultDate) {
    case 'year':
      date.value = getCurrentYear();
      break;
    case 'month':
      date.value = getCurrentMonth();
      break;
    default:
      date.value = getCurrentYear();
      break;
  }
});
</script>

<template>
  <div class="wrap">
    <div class="main item-center flex" :class="{ hasSlot }">
      <template v-for="item in curConfigs" :key="item.key">
        <span
          class="item"
          :data-start="item.start"
          :data-end="item.end"
          :class="{
            active: dayjs(startTime).isSame(dayjs(item.start), 'month') && dayjs(endTime).isSame(dayjs(item.end), 'month'),
          }"
          @click="() => clickHandler(item)"
        >{{ item.label }}</span>
      </template>
      <el-date-picker
        v-model="date"
        :clearable="false"
        type="monthrange"
        value-format="YYYY-MM-DD"
        format="YYYY-MM"
        @change="handleDateChange"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.wrap {
  display: flex;
  .item {
    cursor: pointer;
    min-width: 66px;
    height: 32px;
    line-height: 32px;
    padding: 0 12px;
    text-align: center;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #dcdfe6;
    font-size: 14px;
    color: #3a4762;
    &.active {
      color: #2e6be6;
      border-color: #2e6be6;
      background-color: #e6eeff;
    }
  }
  .main {
    > * {
      margin-right: 16px;
    }
  }
  .hasSlot {
    position: relative;
    margin-right: 80px;
    &:after {
      position: absolute;
      content: '';
      right: -40px;
      top: 4px;
      border-right: 1px solid #b8becc;
      height: 24px;
    }
  }
}
.label {
  color: #3a4762;
  line-height: 32px;
}
.bold {
  font-weight: bold;
}
</style>
