<script setup lang="ts">
import type { EChartsOption } from 'echarts';
import { PieChart } from 'echarts/charts';
import { GridComponent, LegendComponent, TitleComponent } from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { ElTooltip } from 'element-plus';
import { computed } from 'vue';
import VChart from 'vue-echarts';
import { ECHARTS_COLORS } from '@/constant';
import { formatDecimal, formatNumber, generateRandomColor } from '@/lib';

const {
  data = [],
  totalTitle,
  innerLabel = true,
  size = 100,
  radius,
  cols = 2,
  disableInteraction = false,
} = defineProps<DoughnutChartProps>();

// 注册必需的组件
use([CanvasRenderer, PieChart, LegendComponent, GridComponent, TitleComponent]);

// 类型定义
interface ChartDataItem {
  name: string;
  value: number;
}

export interface DoughnutChartProps {
  /** 圆环中心的标题文案 */
  totalTitle?: string;
  /** 环形图数据 */
  data?: ChartDataItem[];
  /** 是否显示内环标签 */
  innerLabel?: boolean;
  /** 圆环大小（直径） */
  size?: number;
  /** 圆环半径 */
  radius?: [string, string];
  /** 圆环列数 */
  cols?: 1 | 2;
  /** 是否禁用交互 */
  disableInteraction?: boolean;
}

// 默认配置
const DEFAULT_CHART_CONFIG = {
  radius: ['40%', '70%'],
  textStyle: {
    fontSize: {
      value: 10,
      title: 8,
    },
    color: '#203549',
    lineHeight: {
      value: 14,
      title: 11,
    },
  },
};

// 计算总数
const totalNum = computed(() => (data || []).reduce((acc, curr) => acc + curr.value, 0));

// 生成标题样式配置
function getTitleStyle() {
  return {
    rich: {
      value: {
        fontSize: DEFAULT_CHART_CONFIG.textStyle.fontSize.value,
        color: DEFAULT_CHART_CONFIG.textStyle.color,
        lineHeight: DEFAULT_CHART_CONFIG.textStyle.lineHeight.value,
      },
      title: {
        fontSize: DEFAULT_CHART_CONFIG.textStyle.fontSize.title,
        color: DEFAULT_CHART_CONFIG.textStyle.color,
        lineHeight: DEFAULT_CHART_CONFIG.textStyle.lineHeight.title,
      },
    },
  };
}

// 图表配置
const options = computed<EChartsOption>(() => ({
  color: ECHARTS_COLORS,
  title: {
    text: [`{value|${formatNumber(totalNum.value)}}`, `{title|${totalTitle}}`].join('\n'),
    left: 'center',
    top: 'center',
    textStyle: getTitleStyle(),
    show: !!totalTitle,
  },

  series: [
    {
      name: 'Access From',
      type: 'pie',
      radius: radius || DEFAULT_CHART_CONFIG.radius,
      data: data || [],
      label: {
        show: innerLabel,
        formatter: (params: any) => {
          // 计算百分比
          const percentage = formatDecimal((params.value / (totalNum.value || 1)) * 100, 1);
          return `${params.name}（${params.value}）${percentage}%`;
        },
        fontSize: 12,
        color: DEFAULT_CHART_CONFIG.textStyle.color,
        lineHeight: 16,
        showMinAngle: 0,
        position: 'outer',
        alignTo: 'labelLine',
      },
      labelLayout: {
        hideOverlap: true,
      },
      labelLine: {
        show: true,
        length: 10,
        length2: 10,
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
      minAngle: 0,
      silent: disableInteraction,
    },
  ],
}));
</script>

<template>
  <div v-if="totalNum === 0" class="flex items-center justify-center">
    <p class="text-base text-[#3A4762]">
      暂无数据
    </p>
  </div>
  <div
    v-else
    class="flex items-center gap-16"
    :class="{
      'justify-center': innerLabel,
      'overflow-hidden': !innerLabel,
    }"
  >
    <VChart
      :option="options"
      :style="{
        width: !innerLabel ? `${size}px` : '100%',
        height: `${size}px`,
        minHeight: '60px',
      }"
    />
    <div v-if="!innerLabel" class="flex max-h-full min-h-60 overflow-y-auto">
      <div
        class="ml-8 grid gap-8 gap-x-24"
        :class="{
          'grid-cols-2': cols === 2,
          'grid-cols-1': cols === 1,
        }"
      >
        <div
          v-for="(item, index) in data"
          :key="item.name"
          class="flex h-14 items-center gap-8 overflow-hidden text-sm text-[#3A4762]"
        >
          <div
            class="size-10 shrink-0 rounded-md leading-1"
            :style="{ background: ECHARTS_COLORS[index] || generateRandomColor() }"
          />
          <div class="flex items-center overflow-hidden">
            <ElTooltip :content="item.name">
              <p class="flex-1 cursor-pointer truncate">
                {{ item.name }}
              </p>
            </ElTooltip>
          </div>
          <span class="shrink-0">({{ item.value }})</span>
          <span class="shrink-0">{{ formatDecimal((item.value / (totalNum || 1)) * 100, 1) }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>
