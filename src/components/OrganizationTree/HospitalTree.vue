<script setup lang="ts">
import { computed, ref, toRefs } from 'vue';

const props = defineProps<{
  deptList: any[];
}>();

const { deptList } = toRefs(props);

interface TreeNode {
  id: number;
  pid?: number;
  label: string;
  data?: any;
  style?: {
    color: string;
    background: string;
  };
  disabled?: boolean;
  noDragging?: boolean;
  children?: TreeNode[];
}

interface DeptPosition {
  positionId: number;
  name: string;
  type: string | null;
  deptType: string;
  doctorUser: {
    doctorId: number | null;
    doctorName: string | null;
    isKey: string | null;
    rightSpeak: string | null;
    pushType: string[];
  };
}

interface DeptItem {
  deptName: string;
  parentId: number | null;
  deptType: string;
  status: string;
  handOver: any;
  bedNum: number | null;
  operationNum: number | null;
  position: DeptPosition[];
  rid: number;
}

/**
 * 将部门列表转换为树形结构
 * @param list 部门列表
 * @returns 树形结构数据
 */
function convertToTree(list: DeptItem[]): TreeNode {
  // 创建一个映射表，用于快速查找节点
  const nodeMap = new Map<number, TreeNode>();

  // 首先创建所有节点
  list.forEach((dept) => {
    nodeMap.set(dept.rid, {
      id: dept.rid,
      pid: dept.parentId,
      label: dept.deptName,
      data: dept, // 保存原始数据
      children: [],
    });
  });

  // 构建树形结构
  const rootNodes: TreeNode[] = [];

  list.forEach((dept) => {
    const node = nodeMap.get(dept.rid)!;
    if (dept.parentId === null) {
      // 如果是根节点
      rootNodes.push(node);
    }
    else {
      // 如果不是根节点，找到父节点并添加到其children中
      const parentNode = nodeMap.get(dept.parentId);
      if (parentNode) {
        if (!parentNode.children) {
          parentNode.children = [];
        }
        parentNode.children.push(node);
      }
    }
  });

  // 如果只有一个根节点，直接返回该节点
  if (rootNodes.length === 1) {
    return rootNodes[0];
  }

  // 如果有多个根节点，创建一个虚拟根节点
  return {
    id: 0,
    label: '组织架构',
    children: rootNodes,
  };
}

// 使用计算属性来生成树形数据
const data = computed(() => convertToTree(deptList.value));

const horizontal = ref(false);
const collapsable = ref(true);
const onlyOneNode = ref(true);
const cloneNodeDrag = ref(true);
const style = ref({
  background: '#fff',
  color: '#5e6d82',
});

/**
 * 获取部门职位
 * @param deptId 部门id
 */
function getDeptPosition(deptId: number) {
  return deptList.value.find(dept => dept.rid === deptId)?.position || [];
}
</script>

<template>
  <div style="height: 400px;" class="mb-16 overflow-hidden rounded bg-[#F7F8FA]">
    <vue3-tree-org
      v-if="deptList.length > 0"
      :center="true"
      :data="data"
      :horizontal="horizontal"
      :collapsable="collapsable"
      :label-style="style"
      :node-draggable="true"
      :scalable="false"
      :tool-bar="false"
      :only-one-node="onlyOneNode"
      :default-expand-level="1"
      :clone-node-drag="cloneNodeDrag"
      :define-menus="[]"
    >
      <!-- 自定义节点内容 -->
      <template #default="{ node }">
        <div class="min-w-200 p-0 text-start">
          <div class="bg-[#2E6BE6] px-12 py-8 text-sm font-medium text-white">
            {{ node.label }}
          </div>
          <div class="flex max-h-50 flex-col gap-8 overflow-y-scroll px-12 py-8 text-sm">
            <div v-for="position in getDeptPosition(node.id)" :key="position.positionId" class="flex items-center">
              <div class="text-[#7A8599]">
                {{ position.name }}：
              </div>
              <div class="text-[#15233F]">
                {{ position.doctorUser?.doctorName }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </vue3-tree-org>
  </div>
</template>

<style scoped>
:deep(.zm-tree-org) {
  background-color: #f7f8fa;
  padding: 0;

  .tree-org-node__inner {
    border-radius: 6px;
    overflow: hidden;
  }
}
</style>
