<script setup lang="ts">
import type { DeptHierarchyItem } from '@/schema';
import { useQuery } from '@tanstack/vue-query';
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { fetchDeptHierarchy } from '@/api';

const router = useRouter();

const { data: deptData } = useQuery({
  queryKey: ['fetchDeptHierarchy'],
  queryFn: () => fetchDeptHierarchy(),
  gcTime: 6 * 1000,
});

const departmentList = computed(() => {
  const deptIds = deptData.value?.map(item => item.deptId) || [];
  return deptData.value?.filter(item => item.parentId === null || deptIds.includes(item.parentId));
});

interface TreeNode {
  id: number;
  pid?: number;
  label: string;
  data?: any;
  style?: {
    color: string;
    background: string;
  };
  disabled?: boolean;
  noDragging?: boolean;
  children?: TreeNode[];
}

interface DeptPosition {
  positionId: number;
  name: string;
  type: string | null;
  deptType: string;
  doctorUser: {
    doctorId: number | null;
    doctorName: string | null;
    isKey: string | null;
    rightSpeak: string | null;
    pushType: string[];
  };
}

/**
 * 将部门列表转换为树形结构
 * @param list 部门列表
 * @returns 树形结构数据
 */
function convertToTree(list: DeptHierarchyItem[]): TreeNode {
  // 创建一个映射表，用于快速查找节点
  const nodeMap = new Map<number, TreeNode>();

  // 首先创建所有节点
  list.forEach((dept) => {
    nodeMap.set(dept.deptId, {
      id: dept.deptId,
      pid: dept.parentId,
      label: dept.name,
      data: dept, // 保存原始数据
      children: [],
    });
  });

  // 构建树形结构
  const rootNodes: TreeNode[] = [];

  list.forEach((dept) => {
    const node = nodeMap.get(dept.deptId)!;
    if (dept.parentId === null) {
      // 如果是根节点
      rootNodes.push(node);
    }
    else {
      // 如果不是根节点，找到父节点并添加到其children中
      const parentNode = nodeMap.get(dept.parentId);
      if (parentNode) {
        if (!parentNode.children) {
          parentNode.children = [];
        }
        parentNode.children.push(node);
      }
    }
  });

  // 如果只有一个根节点，直接返回该节点
  if (rootNodes.length === 1) {
    return rootNodes[0];
  }

  // 如果有多个根节点，创建一个虚拟根节点
  return {
    id: 0,
    label: '组织架构',
    children: rootNodes,
  };
}

// 使用计算属性来生成树形数据
const data = computed(() => convertToTree(departmentList.value || []));

const horizontal = ref(false);
const collapsable = ref(true);
const onlyOneNode = ref(true);
const cloneNodeDrag = ref(true);
const style = ref({
  background: '#fff',
  color: '#5e6d82',
});

/**
 * 获取部门职位
 * @param deptId 部门id
 */
function getDeptUserNum(deptId: number) {
  return departmentList.value?.find(dept => dept.deptId === deptId)?.deptUserNum || 0;
}

/**
 * 节点点击事件
 * @param _ 事件对象
 * @param node 节点数据
 */
function onNodeClick(_: unknown, node: TreeNode) {
  const nodeId = node.id;
  const nodeData = departmentList.value?.find(dept => dept.deptId === nodeId);
  // 如果部门类型为空，则不进行跳转(最上层是公司，没有部门类型)
  if (nodeData?.deptType) {
    router.push({
      path: '/department',
      query: {
        rootDeptId: nodeId,
        id: nodeData?.deptId,
        type: nodeData?.deptType,
      },
    });
  }
}
</script>

<template>
  <div class="h-full overflow-hidden rounded bg-[#F7F8FA]">
    <vue3-tree-org
      :center="true"
      :data="data"
      :horizontal="horizontal"
      :collapsable="collapsable"
      :label-style="style"
      :node-draggable="true"
      :scalable="false"
      :tool-bar="false"
      :only-one-node="onlyOneNode"
      :define-menus="[]"
      :default-expand-level="1"
      :clone-node-drag="cloneNodeDrag"
      @on-node-click="onNodeClick"
    >
      <!-- 自定义节点内容 -->
      <template #default="{ node }">
        <div class="min-w-80 cursor-pointer bg-[#2E6BE6] px-16 py-8 text-center text-xs text-white">
          <div class="mb-4 text-nowrap font-medium">
            {{ node.label }}
          </div>
          <div>
            ({{ getDeptUserNum(node.id) }})
          </div>
        </div>
      </template>
    </vue3-tree-org>
  </div>
</template>

<style scoped>
:deep(.zm-tree-org) {
  background-color: #f7f8fa;
  padding: 0;

  .tree-org-node__inner {
    border-radius: 6px;
    overflow: hidden;
  }
}
</style>
