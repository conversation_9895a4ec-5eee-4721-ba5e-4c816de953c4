<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { computed, toRefs } from 'vue';
import { fetchEmployeeSubjectiveInitiativeTrend } from '@/api';
import { BasicLineChart } from '@/components';
import { usePeopleStore } from '@/store/usePeopleStore';

const props = defineProps<{
  /** 用户🆔 */
  osUserId: number;
}>();
const { osUserId } = toRefs(props);

const params = usePeopleStore();

const queryEnabled = computed(() => !!osUserId.value);

// 获取业务达成趋势
const { data: businessTrend, isLoading: trendLoading } = useQuery({
  queryKey: ['employeeSubjectiveInitiativeTrend', params.searchParams],
  queryFn: () => fetchEmployeeSubjectiveInitiativeTrend(params.searchParams),
  enabled: queryEnabled,
});

// 计算图表数据
const chartData = computed(() => {
  if (!businessTrend.value) {
    return { xData: [], yData: [] };
  }

  return {
    xData: businessTrend.value.map(item => item.statDate || ''),
    yData: businessTrend.value.map(item => item.score || 0),
  };
});
</script>

<template>
  <h3 class="text-base font-medium text-[#15233F]">
    主观能动性评分趋势
  </h3>
  <div v-if="trendLoading" class="flex h-300 items-center justify-center">
    <div class="text-gray-500">
      加载中...
    </div>
  </div>
  <BasicLineChart
    v-else
    :x-axis-data="{
      type: 'category',
      data: chartData.xData,
    }"
    :series-data="chartData.yData"
    style="height: 300px"
    line-color="#2FB324"
    :tooltip="{
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
      formatter: (params) => {
        const index = params[0].dataIndex;
        const data = businessTrend?.[index];
        return `<strong>${data?.statDate}</strong><br>
          主观能动性评分：${data?.score || 0}<br>
        `;
      },
    }"
  />
</template>
