<script setup lang="ts">
import type { DeptType } from '@/constant';
import { useQuery } from '@tanstack/vue-query';
import { ElDrawer } from 'element-plus';
import { computed, provide, readonly, ref, watch } from 'vue';
import { fetchEmployeeInfo } from '@/api';
import { TabPane, Tabs } from '@/components';
import { DEPT_TYPE } from '@/constant';
import { usePeopleStore } from '@/store/usePeopleStore';
import Ability from './Ability.vue';
import Basic from './Basic.vue';
import { PeopleProvideKey } from './constants';
import PeopleInfo from './PeopleInfo.vue';
import Performance from './Performance.vue';
import Production from './Production.vue';

export interface PeopleDrawerProps {
  /** 人员🆔 */
  userId: number;
  /** 部门类型 */
  deptType: DeptType;
  /** 员工ID */
  osUserId: number;
  /** 部门🆔 */
  deptId: number;
}

const props = defineProps<PeopleDrawerProps>();
const { userId, deptType, osUserId, deptId } = toRefs(props);
const model = defineModel<boolean>({ default: false });
const activeTab = ref('basic');
const peopleStore = usePeopleStore();

const queryEnabled = computed(() => !!osUserId.value && !!deptType.value);
const searchParams = computed(() => ({
  osUserId: osUserId.value,
  deptId: deptId.value,
  deptType: deptType.value,
  userId: userId.value,
}));

provide(PeopleProvideKey, readonly(searchParams));

const { data: employeeInfo } = useQuery({
  queryKey: ['员工基础信息', osUserId, deptType, deptId],
  queryFn: () => fetchEmployeeInfo(osUserId.value, deptType.value, deptId.value),
  enabled: queryEnabled,
});

/** 是否显示业绩情况（业绩仅健康顾问展示） */
const showPerformance = computed(() => {
  return employeeInfo.value?.userAccountRole === 1 && deptType.value === DEPT_TYPE.SALES;
});

/** 是否显示人员投产（仅销售和市场角色展示展示） */
const showProduction = computed(() => {
  return deptType.value === DEPT_TYPE.SALES || deptType.value === DEPT_TYPE.MARKET;
});

const showAbility = computed(() => {
  /** 销售顾问、区域经理 */
  const sale = deptType.value === DEPT_TYPE.SALES && [1, 3].includes(employeeInfo.value?.userAccountRole ?? 0);
  /** 市场经理 */
  const marketManager = deptType.value === DEPT_TYPE.MARKET && employeeInfo.value?.userAccountRole === 2;
  return sale || marketManager;
});

watch([userId, deptType, osUserId, deptId, employeeInfo], () => {
  peopleStore.setSearchParams({
    userId: userId.value,
    deptType: deptType.value,
    deptId: deptId.value,
    osUserId: osUserId.value,
    userAccountRole: employeeInfo?.value?.userAccountRole ?? 0,
  });
});

function setActiveTab() {
  if (showPerformance.value) {
    activeTab.value = 'performance';
  }
  else if (showProduction.value) {
    activeTab.value = 'production';
  }
  else if (showAbility.value) {
    activeTab.value = 'ability';
  }
  else {
    activeTab.value = 'basic';
  }
}

watch([model, showPerformance, showProduction, showAbility], () => {
  setActiveTab();
});

onMounted(() => {
  setActiveTab();
});
</script>

<template>
  <ElDrawer
    v-model="model"
    title="人员详情"
    header-class="border-b border-[#E9E8EB] py-16 px-24 text-[#101B25] font-medium mb-0"
    body-class="p-24 overflow-y-hidden flex flex-col gap-32"
    :size="1200"
    destroy-on-close
  >
    <PeopleInfo :info="employeeInfo" />
    <div class="flex-1 overflow-hidden">
      <Tabs v-model="activeTab">
        <TabPane v-if="showPerformance" label="业绩情况" name="performance">
          <Performance :os-user-id="osUserId" :dept-type="deptType" :user-id="userId" />
        </TabPane>
        <TabPane v-if="showProduction" label="人员投产" name="production">
          <Production />
        </TabPane>
        <TabPane v-if="showAbility" label="能力评分" name="ability">
          <Ability
            :os-user-id="osUserId"
            :dept-type="deptType"
            :user-id="userId"
          />
        </TabPane>
        <TabPane label="基础信息" name="basic">
          <Basic v-if="employeeInfo" :employee-info="employeeInfo" />
        </TabPane>
      </Tabs>
    </div>
  </ElDrawer>
</template>
