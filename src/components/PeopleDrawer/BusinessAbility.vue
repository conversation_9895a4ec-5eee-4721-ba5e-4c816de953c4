<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { ElTableColumn } from 'element-plus';
import { computed } from 'vue';
import { fetchEmployeeBusinessAbilityDetail, fetchEmployeeBusinessAbilityTrend } from '@/api';
import { BasicLineChart, InfiniteTable } from '@/components';
import { usePeopleStore } from '@/store/usePeopleStore';

const peopleStore = usePeopleStore();
// 获取业务达成趋势
const { data: businessTrend, isLoading: trendLoading } = useQuery({
  queryKey: ['员工业务达成趋势', peopleStore.searchParams],
  queryFn: () => fetchEmployeeBusinessAbilityTrend({
    ...peopleStore.searchParams,
  }),
  enabled: !!peopleStore.searchParams,
});

// 计算图表数据
const chartData = computed(() => {
  if (!businessTrend.value) {
    return { xData: [], yData: [] };
  }

  return {
    xData: businessTrend.value.map(item => item.statDate || ''),
    yData: businessTrend.value.map(item => item.score || 0),
  };
});

function tableFetch(params: { pageNumber: number; pageSize: number }) {
  return fetchEmployeeBusinessAbilityDetail({
    ...peopleStore.searchParams,
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
  });
}
</script>

<template>
  <h3 class="text-base font-medium text-[#15233F]">
    业务达成评分趋势
  </h3>
  <div v-if="trendLoading" class="flex h-300 items-center justify-center">
    <div class="text-gray-500">
      加载中...
    </div>
  </div>
  <div v-else-if="chartData.xData.length === 0" class="grid h-300 place-items-center">
    <p>暂无数据</p>
  </div>
  <BasicLineChart
    v-else
    :x-axis-data="{
      type: 'category',
      data: chartData.xData,
    }"
    :series-data="chartData.yData"
    style="height: 300px"
    line-color="#2FB324"
    :tooltip="{
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
      formatter: (params) => {
        const index = params[0].dataIndex;
        const data = businessTrend?.[index]
        /** 是否为销售部门 */
        const isSaleDept = peopleStore.searchParams.deptType === 'SALES'
        const refund = isSaleDept ? `退费率：${data?.refundRate || 0}%<br>` : ''
        const operatorNumberMonthlyAverage = !isSaleDept ? `月均开发手术量数：${data?.operatorNumberMonthlyAverage || 0}<br>` : ''
        return `<strong>${data?.statDate}</strong><br>
          指标：${(isSaleDept ? data?.quota : data?.operatorNumberQuota) || 0}<br>
          达成：${(isSaleDept ? data?.achieve : data?.operatorNumber) || 0}<br>
          达标率：${(isSaleDept ? data?.achieveRate : data?.operatorNumberRate) || 0}%<br>
          ${operatorNumberMonthlyAverage}
          ${refund}
          得分：${data?.score || 0}
        `;
      },
    }"
  />
  <h3 class="mb-16 mt-32 text-base font-medium text-[#15233F]">
    业绩明细
  </h3>
  <InfiniteTable :query-fn="tableFetch" class="h-300">
    <ElTableColumn label="所属月份" prop="statDate" />
    <ElTableColumn label="当月指标" prop="quota">
      <template #default="{ row }">
        {{ row.quota ?? '-' }}
      </template>
    </ElTableColumn>
    <ElTableColumn label="达成数" prop="achieve">
      <template #default="{ row }">
        {{ row.achieve ?? '-' }}
      </template>
    </ElTableColumn>
    <ElTableColumn label="达成率" prop="achieveRate">
      <template #default="{ row }">
        {{ row.achieveRate !== null ? `${row.achieveRate}%` : '-' }}
      </template>
    </ElTableColumn>
    <ElTableColumn v-if="peopleStore.searchParams.deptType === 'SALES'" label="退费率" prop="refundRate">
      <template #default="{ row }">
        {{ row.refundRate !== null ? `${row.refundRate}%` : '-' }}
      </template>
    </ElTableColumn>
    <ElTableColumn
      v-if="peopleStore.searchParams.deptType === 'MARKET'"
      label="月均开发手术量数"
      prop="operatorNumberMonthlyAverage"
    />
  </InfiniteTable>
</template>
