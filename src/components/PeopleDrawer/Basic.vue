<script setup lang="ts">
import type { EmployeeInfo } from '@/schema';
import { ElDescriptions, ElDescriptionsItem } from 'element-plus';

const { employeeInfo } = defineProps<{
  employeeInfo: EmployeeInfo;
}>();

/**
 * 将长数字字符串格式化为“前4位 + 两组**** + 后4位”格式
 * @param str 原始字符串
 * @returns 格式化后的字符串
 */
function formatSecretString(str: string | null): string {
  if (!str || str.length < 8)
    return str ?? '--';
  const start = str.slice(0, 4);
  const end = str.slice(-4);
  return `${start} **** **** ${end}`;
}
</script>

<template>
  <div class="flex flex-col gap-24">
    <ElDescriptions :column="2" border title="个人信息" :label-width="200">
      <ElDescriptionsItem label="姓名：">
        {{ employeeInfo.name || '--' }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="性别：">
        {{ employeeInfo.sex || '--' }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="年龄：">
        {{ employeeInfo.age || '--' }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="出生年月：">
        {{ employeeInfo.birthDate || '--' }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="电话号码：">
        {{ employeeInfo.phone || '--' }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="邮箱：">
        {{ employeeInfo.email || '--' }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="银行卡号：">
        {{ formatSecretString(employeeInfo.bankCardNo) }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="银行：">
        {{ employeeInfo.bankName || '--' }}
      </ElDescriptionsItem>
    </ElDescriptions>

    <ElDescriptions :column="2" border title="岗位信息" :label-width="200">
      <ElDescriptionsItem label="职级：">
        {{ employeeInfo.positionLevel || '--' }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="入职日期：">
        {{ employeeInfo.workTime || '--' }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="工龄：">
        {{ employeeInfo.workYear ? `${employeeInfo.workYear}年` : '--' }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="当前月薪：">
        {{ employeeInfo.currentSalary || '--' }}
      </ElDescriptionsItem>
      <ElDescriptionsItem label="直接上级：">
        {{ employeeInfo.directSuperior || '--' }}
      </ElDescriptionsItem>
    </ElDescriptions>
  </div>
</template>

<style scoped>
:deep(.el-descriptions__table) {
  .el-descriptions__label {
    @apply text-sm font-normal text-[#7A8599];
  }
}
</style>
