<script setup lang="ts">
import type { EChartsOption } from 'echarts';
import { useQuery } from '@tanstack/vue-query';
import { RadarChart } from 'echarts/charts';
import {
  GraphicComponent,
  GridComponent,
  LegendComponent,
  MarkLineComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
} from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { computed, useAttrs } from 'vue';
import VChart from 'vue-echarts';
import { fetchEmployeeAbility } from '@/api';
import { ABILITY_TYPE } from '@/constant/employee';
import { usePeopleStore } from '@/store/usePeopleStore';

defineOptions({
  name: 'CurrentAbility',
});

const peopleStore = usePeopleStore();
use([
  CanvasRenderer,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  ToolboxComponent,
  Grid<PERSON>omponent,
  <PERSON><PERSON><PERSON><PERSON>omponent,
  <PERSON><PERSON><PERSON>,
  GraphicComponent,
]);

const attrs = useAttrs();

const { data: ability } = useQuery({
  queryKey: ['能力得分', peopleStore.searchParams],
  queryFn: () => fetchEmployeeAbility(peopleStore.searchParams),
  enabled: !!peopleStore.searchParams,
});

const option = computed<EChartsOption>(() => {
  if (!ability?.value?.abilityList)
    return {};
  return {
    grid: {
      left: '16px',
      right: '16px',
      bottom: '0px',
      top: '16px',
      containLabel: false,
    },
    tooltip: {
      trigger: 'item',
      formatter: () => {
        const list = ability?.value?.abilityList?.map((item) => {
          return `${ABILITY_TYPE[item.abilityType as keyof typeof ABILITY_TYPE]}: ${item.abilityScore}分`;
        }).join('<br/>');
        return `<strong>当前能力 ${ability.value.abilityScore}分</strong><br/>${list}`;
      },
    },
    radar: [
      {
        indicator: ability?.value?.abilityList.map(item => ({
          name: item.abilityType,
          max: item.abilityScoreRate,
        })),
        center: ['50%', '50%'],
        radius: '70px',
        splitArea: {
          show: false, // 关闭间隔色块
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
        axisName: {
          color: '#203549',
          fontSize: 12,
          formatter: (name) => {
            const item = ability?.value?.abilityList?.find(
              item => item.abilityType === name,
            );
            const itemIndex = ability?.value?.abilityList?.findIndex(
              item => item.abilityType === name,
            );
            if (
              itemIndex === undefined
              || itemIndex === -1
              || item === undefined
            ) {
              return '{value}';
            }
            else {
              return `{value|${ABILITY_TYPE[name as keyof typeof ABILITY_TYPE]}}\n{c|${item.abilityScoreRate}%}`;
            }
          },
          textAlign: 'center',
          rich: {
            hr: {
              color: '#203549',
              fontSize: 12,
              width: '100%',
              textAlign: 'center',
            },
            c: {
              color: '#2E6BE6',
              fontSize: 12,
              align: 'center',
              padding: [4, 0, 0, 0],
            },
          },
        },
      },
    ],
    series: [
      {
        type: 'radar',
        smooth: true,
        symbol: 'circle',
        symbolSize: 5,
        showSymbol: true,
        lineStyle: {
          width: 2,
          color: '#2E6BE6',
        },
        areaStyle: {
          opacity: 0.3,
        },
        data: [
          {
            value: ability?.value?.abilityList.map(item => (item.abilityScore / 100) * item.abilityScoreRate),
            name: '当前能力',
          },
        ],
        tooltip: {
          trigger: 'item',
        },
      },
    ] as EChartsOption['series'],
    graphic: [{
      type: 'text',
      left: 'center',
      top: 'center',
      z: 100,
      style: {
        text: `${ability?.value?.abilityResult}\n${ability?.value?.abilityScore ?? ''}`,
        fontSize: 12,
        fontWeight: 'bold',
        fill: '#203549',
        textAlign: 'center',
        textBaseline: 'middle',
        lineHeight: 20,
      },
    }],
  };
});
</script>

<template>
  <VChart
    v-if="ability?.abilityList"
    class="h-240" :class="[attrs.class]"
    :option="option"
    :autoresize="true"
    :style="attrs.style"
  />
  <div v-else class="grid h-240 place-content-center text-[#7A8599]">
    <p>暂无数据</p>
  </div>
</template>
