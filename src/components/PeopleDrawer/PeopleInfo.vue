<script setup lang="ts">
import type { EmployeeInfo } from '@/schema';
import { ElAvatar } from 'element-plus';
import { ABILITY_RESULT_TEXT, JOB_SUB_STATUS_TEXT } from '@/constant';

export interface PeopleDrawerProps {
  /** 员工信息 */
  info?: EmployeeInfo;
}
const { info } = defineProps<PeopleDrawerProps>();
</script>

<template>
  <div class="flex items-center gap-24">
    <ElAvatar :size="80" :src="info?.avatar || undefined">
      <p class="text-4xl font-medium">
        {{ info?.name?.slice(0, 1) || '-' }}
      </p>
    </ElAvatar>
    <div class="space-y-8">
      <div class="flex items-center gap-16 font-medium">
        <p class="text-lg text-[#3A4762]">
          {{ info?.name || '--' }}
        </p>
        <div class="space-x-8 text-xs text-white">
          <span v-if="info?.jobSubStatus" class="rounded bg-[linear-gradient(138deg,_#eaba62_0%,_#d68c1b_100%)] px-8 py-2">
            {{ JOB_SUB_STATUS_TEXT[info.jobSubStatus] }}
          </span>
          <span v-if="info?.abilityResult" class="rounded bg-[#5285EB] px-8 py-2">
            {{ ABILITY_RESULT_TEXT[info.abilityResult] }}
          </span>
        </div>
      </div>
      <div class="flex items-center gap-16 text-sm leading-5 text-[#939CAE]">
        <p>
          <span>职级：</span>
          <span class="text-[#3A4762]">{{ info?.positionLevel || '--' }}</span>
        </p>
        <div class="h-14 w-1 bg-[#E1E5ED]" />
        <p>
          <span>工龄：</span>
          <span class="text-[#3A4762]">在职{{ info?.workYear || '--' }}年</span>
        </p>
      </div>
    </div>
  </div>
</template>
