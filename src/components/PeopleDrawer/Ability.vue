<script setup lang="ts">
import type { DeptType } from '@/constant';
import { useQuery } from '@tanstack/vue-query';
import dayjs from 'dayjs';
import { ElTabPane, ElTabs } from 'element-plus';
import { ref, toRefs } from 'vue';
import { fetchEmployeeAbilityTrend } from '@/api';
import { BasicLineChart, CardTitle } from '@/components';
import { ABILITY_TYPE } from '@/constant/employee';
import { usePeopleStore } from '@/store/usePeopleStore';
import BusinessAbility from './BusinessAbility.vue';
import CurrentAbility from './CurrentAbility.vue';
import DevelopmentPotentialAbility from './DevelopmentPotentialAbility.vue';
import PerformanceEvaluationAbility from './PerformanceEvaluationAbility.vue';
import ProfessionalAbility from './ProfessionalAbility.vue';
import SubjectiveInitiativeAbility from './SubjectiveInitiativeAbility.vue';

const props = defineProps<{
  /** 用户🆔 */
  osUserId: number;
  userId: number;
  deptType: DeptType;
}>();

const peopleStore = usePeopleStore();

const { osUserId } = toRefs(props);

const activeTab = ref('item1');

const { data: abilityTrend } = useQuery({
  queryKey: ['能力得分趋势', peopleStore.searchParams],
  queryFn: () => fetchEmployeeAbilityTrend(peopleStore.searchParams),
});
</script>

<template>
  <div>
    <CardTitle title="能力评分" sub-title="百分制" />
    <div class="mb-24 mt-16 grid grid-cols-2 gap-16">
      <div class="flex flex-col gap-12">
        <div class="text-base font-medium text-[#101B25]">
          当前能力
        </div>
        <div class="bg-[#F7F8FA]">
          <CurrentAbility />
        </div>
      </div>
      <div class="flex flex-col gap-12">
        <div class="text-base font-medium text-[#101B25]">
          能力得分趋势
        </div>
        <div class="bg-[#F7F8FA]">
          <BasicLineChart
            :x-axis-data="{
              type: 'category',
              data: abilityTrend?.map(item => item.statDate) ?? [],
            }"
            :series-data="abilityTrend?.map(item => item.abilityScore) ?? []"
            style="height: 240px"
            :tooltip="{
              trigger: 'axis',
              formatter: (params: any) => {
                const { name, value } = params[0];
                const abilityList = abilityTrend?.find(item => item.statDate === name)?.abilityList ?? [];
                const abilityListHtml = abilityList.map(item => `<div>${ABILITY_TYPE[item.abilityType]}：${item.abilityScore}</div>`).join('');
                return `<strong>${dayjs(name).format('YYYY年MM月')}  ${value}分</strong><br/>${abilityListHtml}`;
              },
            }"
          />
        </div>
      </div>
    </div>
    <ElTabs v-model="activeTab">
      <ElTabPane label="业务达成评分" name="item1">
        <BusinessAbility />
      </ElTabPane>
      <ElTabPane label="主观能动性评分" name="item2" lazy>
        <SubjectiveInitiativeAbility :os-user-id="osUserId" />
      </ElTabPane>
      <ElTabPane label="专业能力评分" name="item3" lazy>
        <ProfessionalAbility />
      </ElTabPane>
      <ElTabPane label="绩效考核评分" name="item4" lazy>
        <PerformanceEvaluationAbility />
      </ElTabPane>
      <ElTabPane label="发展潜力评分" name="item5" lazy>
        <DevelopmentPotentialAbility />
      </ElTabPane>
    </ElTabs>
  </div>
</template>
