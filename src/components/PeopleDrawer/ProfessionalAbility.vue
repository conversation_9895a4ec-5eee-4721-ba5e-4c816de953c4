<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { ElTableColumn } from 'element-plus';
import { computed } from 'vue';
import {
  fetchEmployeeProfessionalAbilityDetail,
  fetchEmployeeProfessionalAbilityTrend,
} from '@/api';
import { BasicLineChart, InfiniteTable } from '@/components';
import { usePeopleStore } from '@/store/usePeopleStore';

const { searchParams, userAccountRole } = usePeopleStore();

// 获取专业能力评分趋势
const { data: businessTrend, isLoading: trendLoading } = useQuery({
  queryKey: ['employeeProfessionalAbilityTrend', searchParams],
  queryFn: () => fetchEmployeeProfessionalAbilityTrend(searchParams),
  enabled: !!searchParams,
});

// 计算图表数据
const chartData = computed(() => {
  if (!businessTrend.value) {
    return { xData: [], yData: [] };
  }

  return {
    xData: businessTrend.value.map(item => item.statDate || ''),
    yData: businessTrend.value.map(item => item.score || 0),
  };
});

function tableFetch(params: { pageNumber: number; pageSize: number }) {
  if (!searchParams) {
    return Promise.resolve({
      contents: [],
      total: 0,
    });
  }
  return fetchEmployeeProfessionalAbilityDetail({
    ...searchParams,
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
  });
}
</script>

<template>
  <h3 class="text-base font-medium text-[#15233F]">
    专业能力评分趋势
  </h3>
  <div v-if="trendLoading" class="flex h-300 items-center justify-center">
    <div class="text-gray-500">
      加载中...
    </div>
  </div>
  <BasicLineChart
    v-else
    :x-axis-data="{
      type: 'category',
      data: chartData.xData,
    }"
    :series-data="chartData.yData"
    style="height: 300px"
    line-color="#2FB324"
    :tooltip="{
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
      formatter: (params) => {
        const index = params[0].dataIndex;
        const data = businessTrend?.[index]
        let abilityStr = `手术转换率：${data?.operationTransformRate || 0}%<br>`
        // 市场经理
        if (searchParams.deptType === 'MARKET' && userAccountRole === 2){
          abilityStr = `
          当月出单工作室数总数：${data?.orderGroupCount || 0}<br>
          管辖区域工作室：${data?.allGroupCount || 0}<br>
          已建立工作室运转率：${data?.orderGroupRate || 0}%<br>`
        }
        else if (searchParams.deptType === 'SALES' && userAccountRole === 3){ // 区域经理
          abilityStr = `
           团队手术转换率：${data?.operationTransformRate || 0}%<br>
           当月出单工作室数总数：${data?.orderGroupCount || 0}<br>
           管辖区域工作室：${data?.allGroupCount || 0}<br>
           已建立工作室运转率：${data?.orderGroupRate || 0}%<br>
        `;
        }
        return `<strong>${data?.statDate}</strong><br>
           手术量：${data?.operationNum || 0}<br>
           达成：${data?.achieve || 0}<br>
           ${abilityStr}
           得分：${data?.score || 0}
        `;
      },
    }"
  />
  <h3 class="mb-16 mt-32 text-base font-medium text-[#15233F]">
    业绩明细
  </h3>
  <InfiniteTable :query-fn="tableFetch" class="h-300">
    <ElTableColumn label="所属月份" prop="statDate" />
    <ElTableColumn label="当月手术量" prop="operationNum">
      <template #default="{ row }">
        {{ row.operationNum ?? '-' }}
      </template>
    </ElTableColumn>
    <ElTableColumn label="达成数" prop="achieve">
      <template #default="{ row }">
        {{ row.achieve ?? '-' }}
      </template>
    </ElTableColumn>
    <!-- 健康顾问 -->
    <ElTableColumn
      v-if="searchParams.deptType === 'SALES' && userAccountRole === 1"
      label="手术转换率"
      prop="operationTransformRate"
    >
      <template #default="{ row }">
        {{ row.operationTransformRate !== null ? `${row.operationTransformRate}%` : '-' }}
      </template>
    </ElTableColumn>
    <!-- 区域经理 -->
    <ElTableColumn
      v-if="searchParams.deptType === 'SALES' && userAccountRole === 3"
      label="团队手术转化率"
      prop="operationTransformRate"
    >
      <template #default="{ row }">
        {{ row.operationTransformRate !== null ? `${row.operationTransformRate}%` : '-' }}
      </template>
    </ElTableColumn>
    <ElTableColumn
      v-if="searchParams.deptType === 'SALES' && userAccountRole === 3
        || searchParams.deptType === 'MARKET' && userAccountRole === 2"
      label="当月出单工作室数总数"
      prop="orderGroupCount"
    >
      <template #default="{ row }">
        {{ row.orderGroupCount !== null ? `${row.orderGroupCount}` : '-' }}
      </template>
    </ElTableColumn>
    <ElTableColumn
      v-if="searchParams.deptType === 'SALES' && userAccountRole === 3
        || searchParams.deptType === 'MARKET' && userAccountRole === 2"
      label="管辖区域工作室"
      prop="allGroupCount"
    >
      <template #default="{ row }">
        {{ row.allGroupCount !== null ? `${row.allGroupCount}` : '-' }}
      </template>
    </ElTableColumn>
    <ElTableColumn
      v-if="searchParams.deptType === 'SALES' && userAccountRole === 3
        || searchParams.deptType === 'MARKET' && userAccountRole === 2"
      label="已建立工作室运转率"
      prop="orderGroupRate"
    >
      <template #default="{ row }">
        {{ row.orderGroupRate !== null ? `${row.orderGroupRate}%` : '-' }}
      </template>
    </ElTableColumn>
  </InfiniteTable>
</template>
