<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import { ElTableColumn } from 'element-plus';
import { computed } from 'vue';
import {
  fetchEmployeePerformanceEvaluationDetail,
  fetchEmployeePerformanceEvaluationTrend,
} from '@/api';
import { BasicLineChart, InfiniteTable } from '@/components';
import { usePeopleStore } from '@/store/usePeopleStore';

const { searchParams } = usePeopleStore();

// 获取业务达成趋势
const { data: businessTrend, isLoading: trendLoading } = useQuery({
  queryKey: ['employeePerformanceEvaluationTrend', searchParams],
  queryFn: () => fetchEmployeePerformanceEvaluationTrend(searchParams),
  enabled: !!searchParams,
});

// 计算图表数据
const chartData = computed(() => {
  if (!businessTrend.value) {
    return { xData: [], yData: [] };
  }

  return {
    xData: businessTrend.value.map(item => item.statDate || ''),
    yData: businessTrend.value.map(item => item.score || 0),
  };
});

function tableFetch(params: { pageNumber: number; pageSize: number }) {
  if (!searchParams) {
    return Promise.resolve({
      contents: [],
      total: 0,
    });
  }
  return fetchEmployeePerformanceEvaluationDetail({
    ...searchParams,
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
  });
}
</script>

<template>
  <h3 class="text-base font-medium text-[#15233F]">
    绩效考核评分趋势
  </h3>
  <div v-if="trendLoading" class="flex h-300 items-center justify-center">
    <div class="text-gray-500">
      加载中...
    </div>
  </div>
  <BasicLineChart
    v-else
    :x-axis-data="{
      type: 'category',
      data: chartData.xData,
    }"
    :series-data="chartData.yData"
    style="height: 300px"
    line-color="#2FB324"
    :tooltip="{
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
      formatter: (params) => {
        const index = params[0].dataIndex;
        const data = businessTrend?.[index]
        return `<strong>${data?.statDate}</strong><br>
           最终绩效分：${data?.score || 0}
        `;
      },
    }"
  />
  <h3 class="mb-16 mt-32 text-base font-medium text-[#15233F]">
    绩效考核明细
  </h3>
  <InfiniteTable :query-fn="tableFetch" class="h-300">
    <ElTableColumn label="考核月份" prop="statDate" />
    <ElTableColumn label="系数" prop="coefficient" />
    <ElTableColumn label="最终评分" prop="score" />
    <ElTableColumn label="说明" prop="remark">
      <template #default="{ row }">
        <span v-if="row.remark">{{ row.remark }}</span>
        <span v-else>--</span>
      </template>
    </ElTableColumn>
  </InfiniteTable>
</template>
