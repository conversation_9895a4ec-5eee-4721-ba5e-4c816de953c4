<script setup lang="ts">
import type { EChartsOption } from 'echarts';
import { useQuery } from '@tanstack/vue-query';
import dayjs from 'dayjs';
import { BarChart, LineChart } from 'echarts/charts';
import { GridComponent, TooltipComponent } from 'echarts/components';
import { use } from 'echarts/core';
import { ElTableColumn, ElTabPane, ElTabs } from 'element-plus';
import { computed, ref, useTemplateRef, watch } from 'vue';
import VChart from 'vue-echarts';
import {
  fetchEmployeeInvestDetail,
  fetchEmployeeInvestmentData,
  fetchEmployeeInvestTrend,
  fetchEmployeeOutputDetail,
  fetchEmployeeOutputTrend,
} from '@/api';
import {
  CardTitle,
  DoughnutChart,
  InfiniteTable,
  RadioGroup,
  StatisticText,
  Times,
} from '@/components';
import { MERGE_METHOD } from '@/constant';
import { formatDecimal } from '@/lib';
import { usePeopleStore } from '@/store/usePeopleStore';

const mergeMethod = ref(MERGE_METHOD[2].id);
const { searchParams } = usePeopleStore();

use([BarChart, LineChart, GridComponent, TooltipComponent]);

const activeName = ref('first');
const startTime = ref<number | undefined>();
const endTime = ref<number | undefined>();
const investDetailTableRef = useTemplateRef('investDetailTable');
const outputDetailTableRef = useTemplateRef('outputDetailTable');

watch(
  () => ({
    searchParams,
    startTime: startTime.value,
  }),
  () => {
    investDetailTableRef.value?.clear();
    outputDetailTableRef.value?.clear();
  },
  { deep: true },
);

// 处理时间选择变化
function handleDateChange([start, end]: [string, string]) {
  startTime.value = start ? dayjs(start).startOf('day').valueOf() : undefined;
  endTime.value = end ? dayjs(end).endOf('day').valueOf() : undefined;
}

// 查询条件是否可用
const queryEnabled = computed(() => {
  return !!searchParams && !!startTime.value && !!endTime.value && !!mergeMethod.value;
});

// 获取员工投产数据
const { data: investmentData, isLoading: investmentLoading } = useQuery({
  queryKey: ['员工投产数据', searchParams, startTime, endTime, mergeMethod],
  queryFn: () => fetchEmployeeInvestmentData({
    ...searchParams,
    startTime: startTime.value,
    endTime: endTime.value,
    mergeMethod: mergeMethod.value,
  }),
  enabled: queryEnabled,
});

// 获取员工投入趋势
const { data: investTrend, isLoading: investTrendLoading } = useQuery({
  queryKey: ['员工投入趋势', searchParams, startTime, endTime, mergeMethod],
  queryFn: () => fetchEmployeeInvestTrend({
    ...searchParams,
    startTime: startTime.value,
    endTime: endTime.value,
    mergeMethod: mergeMethod.value,
  }),
  enabled: queryEnabled,
});

// 获取员工产出趋势
const { data: outputTrend, isLoading: outputTrendLoading } = useQuery({
  queryKey: ['员工产出趋势', searchParams, startTime, endTime, mergeMethod],
  queryFn: () => fetchEmployeeOutputTrend({
    ...searchParams,
    startTime: startTime.value,
    endTime: endTime.value,
    mergeMethod: mergeMethod.value,
  }),
  enabled: queryEnabled,
});

// 投入明细列表
async function fetchInvestDetailList(params: { pageNumber: number; pageSize: number }) {
  return await fetchEmployeeInvestDetail({
    ...searchParams,
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
    startTime: startTime.value,
    endTime: endTime.value,
    mergeMethod: mergeMethod.value,
  });
}

// 产出明细列表
async function fetchOutputDetailList(params: { pageNumber: number; pageSize: number }) {
  return await fetchEmployeeOutputDetail({
    ...searchParams,
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
    startTime: startTime.value,
    endTime: endTime.value,
    mergeMethod: mergeMethod.value,
  });
}

// 格式化投入占比数据为饼图所需格式
const investChartData = computed(() => {
  return investmentData.value?.investList?.map(item => ({
    name: item.investType,
    value: item.investAmount,
  })) ?? [];
});

// 投入趋势图表选项
const investTrendOptions = computed(() => {
  if (!investTrend.value || investTrend.value.length === 0) {
    return {};
  }

  // 获取所有投入类型
  const investTypes = new Set<string>();
  investTrend.value.forEach((item) => {
    item.dataList?.forEach((invest) => {
      investTypes.add(invest.investType);
    });
  });

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter(params) {
        const dataIndex = params[0].dataIndex;
        const data = investTrend.value[dataIndex];
        return `<strong>${data.key}</strong><br>
      ${data.dataList?.map(item => `${item.investType}：${item.investAmount}`).join('<br>')}<br>
      总计：${data.dataList?.reduce((a, b) => a + b.investAmount, 0)}
        `;
      },
    },
    grid: {
      left: 15,
      right: 0,
      bottom: 10,
      top: 10,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: investTrend.value.map(item => item.key),
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#ECEEF2',
        },
      },
      axisLabel: {
        color: '#7A8599',
        fontSize: 12,
      },
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '总计',
        type: 'line',
        data: investTrend.value.map(item => item.dataList?.reduce((a, b) => a + b.investAmount, 0) ?? 0),
        lineStyle: {
          width: 2,
        },
        symbol: 'none',
        symbolSize: 8,
      },
    ],
  } satisfies EChartsOption;
});

// 产出趋势图表选项
const outputTrendOptions = computed(() => {
  if (!outputTrend.value || outputTrend.value.length === 0) {
    return {};
  }

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter(params) {
        const dataIndex = params[0].dataIndex;
        const data = outputTrend.value[dataIndex];
        const deptType = searchParams?.deptType;
        const showData = deptType === 'SALES'
          ? `
        订单量：${data.dataList.map(item => item.orderNum).join('<br>')}<br>
        订单金额：${data.dataList.map(item => item.orderAmount).join('<br>')}<br>
        `
          : `开发手术量：${data.dataList.map(item => item.pciSurgicalVolume).join('<br>')}<br>`;
        return `<strong>${data.key}</strong><br>
        ${showData}
        `;
      },
    },
    grid: {
      left: 15,
      right: 0,
      bottom: 10,
      top: 10,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: outputTrend.value.map(item => item.key),
    },
    yAxis: [
      {
        type: 'value',
        name: '金额',
      },
    ],
    series: [
      {
        name: '订单金额',
        type: 'line',
        data: outputTrend.value.map((item) => {
          if (searchParams.deptType === 'SALES') {
            return item.dataList.map(item => item.orderAmount).reduce((a, b) => a + b, 0);
          }
          return item.dataList.map(item => item.pciSurgicalVolume).reduce((a, b) => a + b, 0);
        }),
      },
    ],
  } satisfies EChartsOption;
});

/** 总投入 */
const investAmount = computed(() => {
  return (investmentData.value?.totalCopyAmount || 0) + (investmentData.value?.totalFeeReimburse || 0);
});

const investOutputRate = computed(() => {
  return formatDecimal((investmentData.value?.outputAmount || 0) / (investAmount.value || 1), 2);
});
</script>

<template>
  <div class="space-y-16">
    <div class="flex items-center justify-between">
      <Times
        class="sticky top-0 z-10 bg-white"
        default-active-key="latestYear"
        @date-change="handleDateChange"
      />
      <RadioGroup
        v-model="mergeMethod"
        label="汇总方式"
        :data="MERGE_METHOD"
        size="mini"
      />
    </div>
    <CardTitle title="投产比" sub-title="单位：元" />
    <div v-loading="investmentLoading" class="grid grid-cols-3 gap-16">
      <div class="col-span-2 grid grid-cols-4 rounded bg-[#F7F8FA] p-16">
        <StatisticText
          title="投入金额"
          :value="(investmentData?.totalCopyAmount || 0) + (investmentData?.totalFeeReimburse || 0)"
        />
        <div class="col-span-3">
          <p class="mb-16">
            投入占比
          </p>
          <DoughnutChart
            :size="72"
            :radius="['70%', '100%']"
            :data="investChartData"
            :inner-label="false"
            :disable-interaction="true"
          />
        </div>
      </div>
      <div v-if="searchParams.deptType === 'SALES'" class="flex justify-between rounded bg-[#F7F8FA] p-16">
        <StatisticText
          title="产出订单量（单）/产出金额"
          :value="`${investmentData?.outputOrderNum || 0}/${investmentData?.outputAmount || 0}`"
        />
        <StatisticText
          title="人员投产比"
          :value="investOutputRate || 0"
        />
      </div>
      <div v-else class="flex justify-between rounded bg-[#F7F8FA] p-16">
        <StatisticText
          title="产出PCI手术量"
          :value="investmentData?.pciSurgicalVolume || 0"
        />
        <StatisticText
          title="千台手术成本"
          :value="formatDecimal(investAmount / (investmentData?.pciSurgicalVolume || 1) * 1000, 2)"
        />
      </div>
    </div>
  </div>
  <ElTabs v-model="activeName" class="mt-12">
    <ElTabPane label="投入分析" name="first" lazy>
      <div>
        <div class="mb-16 flex items-center gap-16">
          <h3 class="text-base font-medium leading-5 text-[#15233F]">
            投入趋势
          </h3>
          <span class="text-sm text-[#7A8599]">单位: 元</span>
        </div>
        <VChart
          v-loading="investTrendLoading"
          :option="investTrendOptions"
          class="h-300"
          autoresize
        />
      </div>
      <p class="my-16 mt-12 flex items-center gap-16 text-sm text-[#7A8599]">
        <span class="text-base font-medium text-[#15233F]">投入明细</span>
        <span>单位：元</span>
      </p>

      <InfiniteTable ref="investDetailTable" class="h-300" :query-fn="fetchInvestDetailList">
        <ElTableColumn label="投入类型" prop="investType" />
        <ElTableColumn label="投入时间" prop="investTime">
          <template #default="{ row }">
            {{ dayjs(row.investTime).format('YYYY-MM-DD') }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="投入金额" prop="investAmount" />
        <ElTableColumn label="说明" prop="remark" />
      </InfiniteTable>
    </ElTabPane>
    <ElTabPane label="产出分析" name="second" lazy>
      <div>
        <div class="mb-16 flex items-center gap-16">
          <h3 class="text-base font-medium leading-5 text-[#15233F]">
            产出趋势
          </h3>
          <span class="text-sm text-[#7A8599]">
            单位: {{ searchParams.deptType === 'SALES' ? '元' : '台' }}
          </span>
        </div>
        <VChart
          v-loading="outputTrendLoading"
          :option="outputTrendOptions"
          class="h-300"
          autoresize
        />
      </div>
      <p class="my-16 mt-12 flex items-center gap-16 text-sm text-[#7A8599]">
        <span class="text-base font-medium text-[#15233F]">产出明细</span>
        <span>单位：{{ searchParams.deptType === 'SALES' ? '元' : '台' }}</span>
      </p>

      <InfiniteTable v-if="searchParams.deptType === 'SALES'" ref="outputDetailTable" class="h-300" :query-fn="fetchOutputDetailList">
        <ElTableColumn label="产出类型" prop="outputType" />
        <ElTableColumn label="产出时间" prop="outputTime">
          <template #default="{ row }">
            {{ dayjs(row.outputTime).format('YYYY-MM-DD') }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="产出金额" prop="outputAmount" />
        <ElTableColumn label="说明" prop="remark" />
      </InfiniteTable>
      <InfiniteTable v-else ref="outputDetailTable" class="h-300" :query-fn="fetchOutputDetailList">
        <ElTableColumn label="工作室名称" prop="marketGroupName" />
        <ElTableColumn label="工作室创建时间" prop="generateTime">
          <template #default="{ row }">
            {{ dayjs(row.generateTime).format('YYYY-MM-DD') }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="工作室手术量" prop="operationNum" />
      </InfiniteTable>
    </ElTabPane>
  </ElTabs>
</template>
