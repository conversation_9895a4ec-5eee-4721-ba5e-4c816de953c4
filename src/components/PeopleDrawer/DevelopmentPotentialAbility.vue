<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import dayjs from 'dayjs';
import { ElTableColumn } from 'element-plus';
import { computed } from 'vue';
import {
  fetchEmployeeDevelopmentPotentialDetail,
  fetchEmployeeDevelopmentPotentialTrend,
} from '@/api';
import { BasicLineChart, InfiniteTable } from '@/components';
import { usePeopleStore } from '@/store/usePeopleStore';

const { searchParams } = usePeopleStore();

// 获取发展潜力趋势
const { data: businessTrend, isLoading: trendLoading } = useQuery({
  queryKey: ['DevelopmentPotentialTrend', searchParams],
  queryFn: () => fetchEmployeeDevelopmentPotentialTrend(searchParams),
  enabled: !!searchParams,
});

// 计算图表数据
const chartData = computed(() => {
  if (!businessTrend.value) {
    return { xData: [], yData: [] };
  }

  return {
    xData: businessTrend.value.map(item => item.statDate || ''),
    yData: businessTrend.value.map(item => item.aveScore || 0),
  };
});

function tableFetch(params: { pageNumber: number; pageSize: number }) {
  if (!searchParams) {
    return Promise.resolve({
      contents: [],
      total: 0,
    });
  }
  return fetchEmployeeDevelopmentPotentialDetail({
    ...searchParams,
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
  });
}

/**
 * 格式化时间
 * @param minutes 分钟
 */
function timeFormat(minutes: number | null) {
  if (!minutes) {
    return '--';
  }
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  if (hours === 0) {
    return `${remainingMinutes}min`;
  }
  return `${hours}h${remainingMinutes}min`;
}
</script>

<template>
  <h3 class="text-base font-medium text-[#15233F]">
    发展潜力
  </h3>
  <div v-if="trendLoading" class="flex h-300 items-center justify-center">
    <div class="text-gray-500">
      加载中...
    </div>
  </div>
  <BasicLineChart
    v-else
    :x-axis-data="{
      type: 'category',
      data: chartData.xData,
    }"
    :series-data="chartData.yData"
    style="height: 300px"
    line-color="#2FB324"
    :tooltip="{
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
      formatter: (params) => {
        const index = params[0].dataIndex;
        const data = businessTrend?.[index]
        return `<strong>${data?.statDate}</strong><br>
          ${data?.dataList?.map(item => `${item.examType}：${item.score}`).join('<br>')}<br>
          当月参训时长：${timeFormat(data?.trainMinuteNum || 0)}<br>
          专业性评分：${data?.aveScore || 0}
        `;
      },
    }"
  />
  <h3 class="mb-16 mt-32 text-base font-medium text-[#15233F]">
    培训考试明细
  </h3>
  <InfiniteTable :query-fn="tableFetch" class="h-300">
    <ElTableColumn label="培训考试名称" prop="trainExamName" />
    <ElTableColumn label="开始时间" prop="startTime">
      <template #default="{ row }">
        {{ row.startTime ? dayjs(row.startTime).format('YYYY-MM-DD HH:mm') : '--' }}
      </template>
    </ElTableColumn>
    <ElTableColumn label="结束时间" prop="endTime">
      <template #default="{ row }">
        {{ row.endTime ? dayjs(row.endTime).format('YYYY-MM-DD HH:mm') : '--' }}
      </template>
    </ElTableColumn>
    <ElTableColumn label="培训时长" prop="trainMinuteNum" width="100">
      <template #default="{ row }">
        {{ timeFormat(row.trainMinuteNum) }}
      </template>
    </ElTableColumn>
    <ElTableColumn label="是否考试" prop="isExam" width="100">
      <template #default="{ row }">
        {{ row.isExam ? '是' : '否' }}
      </template>
    </ElTableColumn>
    <ElTableColumn label="考试成绩" prop="examScore" width="100" />
    <ElTableColumn label="说明" prop="remark" />
  </InfiniteTable>
</template>
