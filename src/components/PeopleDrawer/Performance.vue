<script setup lang="tsx">
import type { EChartsOption } from 'echarts';
import type { DeptType } from '@/constant';
import { useQuery } from '@tanstack/vue-query';
import dayjs from 'dayjs';
import { BarChart, LineChart } from 'echarts/charts';
import { GridComponent, TooltipComponent } from 'echarts/components';
import { use } from 'echarts/core';
import {
  ElDescriptions,
  ElDescriptionsItem,
  ElTableColumn,
  ElTabPane,
  ElTabs,
  ElTooltip,
} from 'element-plus';
import { computed, ref, toRefs, useTemplateRef, watch } from 'vue';
import VChart from 'vue-echarts';
import {
  fetchEmployeeGroupList,
  fetchEmployeeGroupTotalInfo,
  fetchEmployeeOrderTrend,
  fetchEmployeePerfStat,
} from '@/api';
import question from '@/assets/icon/question.png';
import { InfiniteTable, Times } from '@/components';

const props = defineProps<{
  osUserId: number;
  deptType: DeptType;
  userId: number;
}>();

const { osUserId, deptType, userId } = toRefs(props);

use([BarChart, LineChart, GridComponent, TooltipComponent]);

const activeName = ref('first');
const startTime = ref<number | undefined>();
const endTime = ref<number | undefined>();
const tableRef = useTemplateRef('table');

const queryEnabled = computed(() => {
  return !!startTime.value && !!endTime.value && !!osUserId.value && !!deptType.value && !!userId.value;
});

watch([osUserId, startTime, endTime], () => {
  tableRef.value?.clear();
});

const { data: employeePerfStat } = useQuery({
  queryKey: ['employeePerfStat', osUserId, startTime, endTime],
  queryFn: () =>
    fetchEmployeePerfStat({
      startTime: startTime.value,
      endTime: endTime.value,
      osUserId: osUserId.value,
      deptType: deptType.value,
      userId: userId.value,
    }),
  enabled: queryEnabled,
});

const { data: employeeOrderTrend } = useQuery({
  queryKey: ['employeeOrderTrend', osUserId, startTime, endTime],
  queryFn: () =>
    fetchEmployeeOrderTrend({
      startTime: startTime.value,
      endTime: endTime.value,
      osUserId: osUserId.value,
      deptType: deptType.value,
      userId: userId.value,
    }),
  enabled: queryEnabled,
});

// 获取员工负责工作室总计信息
const { data: groupTotalInfo } = useQuery({
  queryKey: ['employeeGroupTotalInfo', osUserId],
  queryFn: () => fetchEmployeeGroupTotalInfo({
    osUserId: osUserId.value,
    deptType: deptType.value,
    userId: userId.value,
  }),
  enabled: computed(() => !!osUserId.value),
});

// 查询工作室列表的函数
async function fetchGroupList(params: { pageNumber: number; pageSize: number }) {
  return await fetchEmployeeGroupList({
    osUserId: osUserId.value,
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
    deptType: deptType.value,
    userId: userId.value,
  });
}

// 获取员工负责工作室列表（用于显示总计信息）
const { data: groupList } = useQuery({
  queryKey: ['employeeGroupList', osUserId],
  queryFn: () => fetchEmployeeGroupList({
    osUserId: osUserId.value,
    pageNumber: 1,
    pageSize: 1,
    deptType: deptType.value,
    userId: userId.value,
  }),
  enabled: computed(() => !!osUserId.value),
});

const options = computed(() => {
  const option: EChartsOption = {
    tooltip: {
      trigger: 'axis',
      formatter(params) {
        const dataIndex = params[0].dataIndex;
        const data = employeeOrderTrend.value?.[dataIndex];
        return `<strong>${params[0].axisValue}</strong><br/>
        开单工作室：${data?.orderGroupNum}<br/>
        指标量：${data?.quotaNum}<br/>
        有效订单：${data?.orderPaidNum}<br/>
        达成率：${data?.achieveRate}%`;
      },
    },
    grid: {
      left: '60px',
      top: '20px',
      right: '60px',
    },
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: '#7A8599',
          fontSize: 14,
        },
      },
      {
        type: 'value',
        min: 0,
        axisLabel: {
          formatter: '{value}%',
        },
        splitLine: {
          show: false,
        },
      },
    ],
    xAxis: [
      {
        type: 'category',
        data: employeeOrderTrend.value?.map(item => item.statDate) ?? [],
        axisLabel: {
          color: '#7A8599',
          fontSize: 14,
        },
      },
    ],
    series: [
      {
        name: '有效订单',
        type: 'bar',
        data: employeeOrderTrend.value?.map(item => item.orderPaidNum) ?? [],
        color: '#5285EB',
      },
      {
        name: '指标量',
        type: 'bar',
        data: employeeOrderTrend.value?.map(item => item.quotaNum) ?? [],
        color: '#9FBCF5',
      },
      {
        name: '达成率',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {
          valueFormatter(value) {
            return `${value}%`;
          },
        },
        data: employeeOrderTrend.value?.map(item => item.achieveRate) ?? [],
        color: '#2FB324',
      },
    ],
  };
  return option;
});

function handleDataChange([start, end]: [string, string]) {
  startTime.value = start ? dayjs(start).startOf('day').valueOf() : undefined;
  endTime.value = end ? dayjs(end).endOf('day').valueOf() : undefined;
}
</script>

<template>
  <Times class="sticky top-0 z-10 bg-white" default-active-key="latestYear" @date-change="handleDataChange" />
  <ElTabs v-model="activeName" class="mt-12">
    <ElTabPane label="业绩分析" name="first">
      <div>
        <ElDescriptions :column="2" border title="业绩统计" :label-width="200">
          <ElDescriptionsItem label="单日销冠奖（次数）：">
            {{ employeePerfStat?.singleDaySalesCrownNum ?? 0 }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="月度冠军（次数）：">
            {{ employeePerfStat?.monthlyChampionNum ?? 0 }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="月度亚军（次数）：">
            {{ employeePerfStat?.monthlySecondPlaceNum ?? 0 }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="月度季军（次数）：">
            {{ employeePerfStat?.monthlyThirdPlaceNum ?? 0 }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="非工作日开单奖：">
            {{ employeePerfStat?.nonWorkDayOrderAwardNum ?? 0 }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="退单率：">
            {{ employeePerfStat?.refundRate ?? 0 }}%
          </ElDescriptionsItem>
          <ElDescriptionsItem label="订单数：">
            {{ employeePerfStat?.orderNum ?? 0 }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="平均达成率：">
            {{ employeePerfStat?.aveAchieveRate ?? 0 }}%
          </ElDescriptionsItem>
        </ElDescriptions>
        <div class="mb-16 mt-32 flex items-center gap-24">
          <h3 class="text-base font-medium leading-5 text-[#15233F]">
            新购订单走势
          </h3>
          <div class="flex items-center gap-24">
            <div class="flex items-center gap-8">
              <span class="block size-12 rounded-sm bg-[#9FBCF5]" />
              <span class="text-sm text-[#939CAE]">指标量</span>
            </div>
            <div class="flex items-center gap-8">
              <span class="block size-12 rounded-sm bg-[#5285EB]" />
              <span class="text-sm text-[#939CAE]">有效订单</span>
            </div>
          </div>
          <ElTooltip placement="top" effect="dark">
            <img :src="question" class="size-16">
            <template #content>
              <div>
                <p class="mt-4 text-base">
                  月指标
                </p>
                <p class="mb-12 mt-8 text-sm">
                  当月销售目标
                </p>
                <p class="text-base">
                  有效订单
                </p>
                <p class="mb-4 mt-8 text-sm">
                  当月成交，且未在40天内退款的订单
                </p>
              </div>
            </template>
          </ElTooltip>
        </div>
        <VChart :option="options" class="h-300" autoresize />
      </div>
    </ElTabPane>
    <ElTabPane label="负责工作室" name="second">
      <p class="my-16 mt-12 flex items-center gap-16 text-sm text-[#7A8599]">
        <span class="text-base font-medium text-[#15233F]">负责工作室</span>
        <span>{{ groupTotalInfo?.totalHospitalNum || 0 }}家医院，{{ groupList?.total || 0 }}个工作室，{{ groupTotalInfo?.totalSurgicalVolume || 0 }}台年PCI手术量</span>
      </p>
      <InfiniteTable ref="table" :height="400" :query-fn="fetchGroupList">
        <ElTableColumn
          prop="groupName"
          label="工作室名称"
        />
        <ElTableColumn
          prop="hospitalName"
          label="所属医院"
        />
        <ElTableColumn
          prop="pciSurgicalVolume"
          label="年PCI手术量"
          width="120"
        />
        <ElTableColumn
          prop="managePatientNum"
          label="在管会员数"
          width="120"
        />
        <ElTableColumn
          prop="groupTransformRate"
          label="工作室转换率"
          width="120"
        >
          <template #default="{ row }">
            <span>{{ row.groupTransformRate }}%</span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="memberRenewRate"
          label="会员续费率"
          width="120"
        >
          <template #default="{ row }">
            <span>{{ row.memberRenewRate }}%</span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="groupRefundRate"
          label="工作室退费率"
          width="120"
        >
          <template #default="{ row }">
            <span>{{ row.groupRefundRate }}%</span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="groupStatus"
          label="状态"
          fixed="right"
          width="100"
        >
          <template #default="{ row }">
            <span>{{ row.groupStatus === 'ENABLE' ? '启用' : '禁用' }}</span>
          </template>
        </ElTableColumn>
      </InfiniteTable>
    </ElTabPane>
  </ElTabs>
</template>

<style lang="less" scoped>
:deep(.el-descriptions__table) {
  .el-descriptions__label {
    @apply text-sm font-normal text-[#7A8599];
  }
}
</style>
