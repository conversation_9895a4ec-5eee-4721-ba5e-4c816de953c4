<template>
  <div class="h-full w-full">
    <Card :title="title" bold :sub-data="subData" :desc="desc" :tips="tips">
      <template #operation>
        <slot name="operation"></slot>
      </template>
      <BaseChart
        type="bar"
        :data-complete="true"
        :options="barOptions"
        enable-click-listener
        @click-item="clickItemHandler"
      />
    </Card>
  </div>
</template>

<script setup lang="ts">
import BaseChart from '@/components/BaseChart/BaseChart.vue';
import { COLORS_BASE, IChartCommonProps } from '@/components/BaseChart/options';
import { getBarEchartsOptions } from '@/components/BaseChart/options/bar';
import Card from '@/components/Card/index.vue';

interface IProps extends IChartCommonProps {
  title?: string;
  bold?: boolean;
  tips?: string;
  desc?: string;
  size?: 'mini' | 'default';
}
const emit = defineEmits(['click-item']);
const props = defineProps<IProps>();
const barOptions = ref({});
const clickItemHandler = item => {
  emit('click-item', item);
};
const getOptions = () => {
  const {
    size,
    color,
    xData,
    yAxis,
    zoomSize,
    labelNoWrap,
    grid,
    data,
    dataZoomConfig,
  } = props;
  const curZoomSize = zoomSize ?? (size === 'mini' ? 8 : 12);
  const dataZoomEnable = (xData?.length ?? 0) > curZoomSize;
  return getBarEchartsOptions({
    legend: props.hideLegend ? null : { icon: 'roundRect', bottom: 0 },
    grid: { bottom: dataZoomEnable ? 70 : 50, ...grid },
    color: color ?? COLORS_BASE,
    xAxisData: xData,
    dataZoom: {
      enable: dataZoomEnable,
      config: {
        endValue: size === 'mini' ? 5 : 7,
        height: size === 'mini' ? 14 : 20,
        ...dataZoomConfig,
      },
    },
    xAxis: labelNoWrap
      ? {
          axisLabel: {
            color: '#7A8599',
            interval: 0,
          },
        }
      : undefined,
    yAxis: yAxis ?? [
      {
        type: 'value',
        min: 0,
        minInterval: 1,
        axisLabel: {
          formatter: '{value}',
        },
      },
      {
        type: 'value',
        min: 0,
        splitLine: { show: false },
        axisLabel: {
          formatter: '{value} %',
        },
      },
    ],
    seriesConfig: data ? data.map(v => ({ ...v })) : [],
  });
};
barOptions.value = getOptions();

watch(
  () => props.data,
  () => (barOptions.value = getOptions())
);
defineOptions({
  name: 'StackBarChart',
});
</script>

<style lang="less">
.echart-custom-tooltip {
  > div > div > div {
    &:first-child {
      position: relative;
      padding-bottom: 16px;
      &:after {
        position: absolute;
        content: '/';
        font-size: 0;
        border-bottom: 1px solid #ccc;
        bottom: 4px;
        width: 100%;
        left: 0;
      }
    }
  }
}
</style>
