<script setup lang="ts">
import { INDICATORS } from '@/constant';
import { computed } from 'vue';

interface RadioGroupProps {
  /** 标签 */
  label?: string;
  /** 数据 */
  data?: {
    id: string;
    name: string;
  }[];
  /** 值 */
  value?: string;
  /** 颜色 */
  color?: string;
  /** 是否加粗 */
  bold?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否显示分割线 */
  hasLine?: boolean;
  /** 尺寸 */
  size?: 'mini' | 'default';
  /** 间距 */
  gap?: number;
}

defineOptions({
  name: 'RadioGroup',
});

const props = withDefaults(defineProps<RadioGroupProps>(), {
  gap: 0,
  color: '#3A4762',
  bold: false,
  size: 'default',
  value: undefined,
  data: () => [...INDICATORS],
  label: undefined,
});

const emit = defineEmits<{
  onChange: [value: string];
}>();
const checkValue = defineModel<string>();
const list = [...INDICATORS];
const curList = computed(() => props.data || list);

function changeHandler(val: string) {
  if (props.disabled)
    return;
  checkValue.value = val;
  emit('onChange', val);
}
</script>

<template>
  <div
    class="flex"
    :class="{
      'border-x-0 border-b border-t-0 border-solid border-[#E9E8EB] pb-16':
        !!hasLine,
    }"
  >
    <span
      v-if="label"
      class="w-85 shrink-0 pr-20 pt-4 leading-8"
      :class="{ 'font-bold': bold }"
      :style="{ color }"
    >
      {{ label }}
    </span>
    <div class="flex flex-1 flex-wrap items-center" :style="{ gap: `${props.gap}px` }">
      <button
        v-for="(item, index) in curList"
        :key="item.id"
        type="button"
        class="box-border cursor-pointer border border-solid px-12 py-5 text-center font-normal leading-5 transition-all delay-200 disabled:cursor-not-allowed"
        :class="{
          'border-l-0': gap === 0 && index !== 0 && checkValue !== item.id,
          'border-r-0': gap === 0 && checkValue === curList[index + 1]?.id && !!checkValue,
          'rounded-l-sm': index === 0 && gap === 0,
          'rounded-r-sm': index === curList.length - 1 && gap === 0,
          'border-l border-[#2e6be6] bg-[#e6eeff] text-[#2e6be6]': checkValue === item.id,
          'border-[#dcdfe6] bg-white text-[#939cae]': checkValue !== item.id,
          'min-w-66': size === 'mini',
          'min-w-108': size !== 'mini',
          'rounded-sm': gap !== 0,
        }"
        :disabled="disabled"
        @click="changeHandler(item.id)"
      >
        {{ item.name }}
      </button>
    </div>
  </div>
</template>
