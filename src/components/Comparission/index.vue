<script setup lang="ts">
import { CaretBottom, CaretTop } from '@element-plus/icons-vue';
import { ElIcon } from 'element-plus';
import { computed } from 'vue';

interface IProps {
  /** 同比数据 */
  yoy?: number;
  /** 环比数据 */
  mom?: number;
  /** 是 */
  itemWidth?: string;
  /** 隐藏同比数据 */
  hideYoy?: boolean;
  /** 同一行展示 */
  inline?: boolean;
  /** 保留几位小数 */
  fractionDigits?: number;
}
defineOptions({
  name: 'Comparison',
});

const { yoy = 0, mom = 0, fractionDigits = 2 } = defineProps<IProps>();

const curYoy = computed(() => {
  return Number((yoy ?? 0).toFixed(fractionDigits));
});
const curMom = computed(() => {
  return Number((mom ?? 0).toFixed(fractionDigits));
});
</script>

<template>
  <div
    class="flex flex-nowrap items-center text-xs text-[#939cae]"
    :class="[inline ? 'inline' : '']"
  >
    <div v-if="!hideYoy" class="item text-nowrap" :style="{ width: itemWidth }">
      同比
      <span :class="curYoy >= 0 ? 'green' : 'red'">
        <span class="value">{{ Math.abs(curYoy) }}%</span>
        <ElIcon size="16">
          <CaretTop v-if="curYoy >= 0" />
          <CaretBottom v-else />
        </ElIcon>
      </span>
    </div>
    <div class="item text-nowrap" :style="{ width: itemWidth }">
      环比
      <span :class="curMom >= 0 ? 'green' : 'red'">
        <span class="value">{{ Math.abs(curMom) }}%</span>
        <ElIcon size="16">
          <CaretTop v-if="curMom >= 0" />
          <CaretBottom v-else />
        </ElIcon>
      </span>
    </div>
  </div>
</template>

<style scoped lang="less">
.inline {
  .item {
    width: 150px;
  }
}
.green {
  color: #2fb324;
  display: inline-flex;
  align-items: center;
}
.red {
  color: #e63746;
  display: inline-flex;
  align-items: center;
}
.value {
  margin: 0;
}
</style>
