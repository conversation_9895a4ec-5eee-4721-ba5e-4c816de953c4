<template>
  <div class="label-container" :class="{ 'is-dark': isDark }">
    <div class="label-icon" v-if="icon">
      <el-icon><component :is="icon" /></el-icon>
    </div>
    <div class="label-content">
      <div class="label-text">{{ text }}</div>
      <div class="label-value" v-if="value || percentage">
        {{ value }}
        <span v-if="percentage" class="label-percentage">{{ percentage }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  text: string;
  value?: string | number;
  percentage?: string;
  icon?: string;
  isDark?: boolean;
}>();
</script>

<style scoped lang="less">
.label-container {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background: linear-gradient(
    90deg,
    rgba(46, 107, 230, 0.1) 0%,
    rgba(46, 107, 230, 0.05) 100%
  );
  border-radius: 4px;
  border: 1px solid rgba(46, 107, 230, 0.2);
  gap: 8px;
  color: #15233f;

  &.is-dark {
    background: linear-gradient(
      90deg,
      rgba(21, 35, 63, 0.1) 0%,
      rgba(21, 35, 63, 0.05) 100%
    );
    border-color: rgba(21, 35, 63, 0.2);
  }

  .label-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #2e6be6;

    .is-dark & {
      color: #15233f;
    }
  }

  .label-content {
    display: flex;
    align-items: center;
    gap: 8px;

    .label-text {
      font-size: 16px;
      font-weight: bold;
    }

    .label-value {
      font-size: 20px;
      font-weight: bold;
      color: #2e6be6;

      .is-dark & {
        color: #15233f;
      }

      .label-percentage {
        font-size: 14px;
        margin-left: 2px;
      }
    }
  }
}
</style>
