<script setup lang="ts">
import type { EChartsOption } from 'echarts';
import { <PERSON><PERSON><PERSON>, LineChart } from 'echarts/charts';
import {
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { isEmpty } from 'lodash-es';
import { computed, useAttrs } from 'vue';
import VChart from 'vue-echarts';
import Empty from '../Empty/index.vue';

export interface BasicLineProps {
  xAxisData?: EChartsOption['xAxis'];
  seriesData?: number[];
  smooth?: boolean;
  symbol?: string;
  lineColor?: string;
  tooltip?: EChartsOption['tooltip'];
}

const {
  xAxisData = [],
  seriesData = [],
  smooth = false,
  lineColor = '#2E6BE6',
  symbol = 'none',
  tooltip,
} = defineProps<BasicLineProps>();

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  GridComponent,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
]);

const attrs = useAttrs();

const options = computed<EChartsOption>(() => ({
  grid: {
    left: 40,
    top: 30,
    right: 30,
    bottom: 30,
  },
  tooltip,
  xAxis: {
    axisLine: {
      lineStyle: {
        color: '#ECEEF2',
      },
    },
    axisLabel: {
      color: '#7A8599',
    },
    axisTick: {
      show: false,
    },
    ...xAxisData,
  },
  yAxis: {
    type: 'value',
    splitLine: {
      show: true, // 确保显示网格线
      lineStyle: {
        type: 'dashed', // 设置为虚线
        color: '#ccc', // 虚线颜色（可选）
        width: 1, // 虚线宽度（可选）
        dashOffset: 2, // 虚线偏移量（可选，一般无需修改）
      },
    },
    axisLabel: {
      color: '#7A8599',
    },
  },
  series: [
    {
      data: seriesData,
      type: 'line',
      smooth,
      symbol,
      lineStyle: {
        color: lineColor,
      },
    },
  ],
}));
</script>

<template>
  <Empty v-if="isEmpty(options.xAxis?.data)" />
  <VChart v-else :option="options" class="w-full" :class="[attrs.class]" :style="attrs.style" autoresize />
</template>
