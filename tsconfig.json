{"compilerOptions": {"typeRoots": ["node_modules/@types", "src/types"], "target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "noImplicitAny": false, "lib": ["esnext", "dom"], "baseUrl": "./", "paths": {"@": ["src"], "@/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "docs/**/*.vue", "./auto-imports.d.ts"]}