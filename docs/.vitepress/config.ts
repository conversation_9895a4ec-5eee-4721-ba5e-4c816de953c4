import autoprefixer from 'autoprefixer';
import path from 'path';
import tailwindcss from 'tailwindcss';
import { defineConfig } from 'vitepress';
import { vitepressDemoPlugin } from 'vitepress-demo-plugin';

export default defineConfig({
  title: 'Data Vista',
  description: '数据可视化平台文档',
  lang: 'zh-CN',
  markdown: {
    config(md) {
      md.use(vitepressDemoPlugin, {
        demoDir: path.resolve(__dirname, '../demos'),
        lightTheme: 'github-light',
        darkTheme: 'github-dark',
      });
    },
  },
  themeConfig: {
    outline: {
      label: '页面导航',
    },
    returnToTopLabel: '返回顶部',
    darkModeSwitchLabel: '主题',
    nav: [
      { text: '首页', link: '/' },
      { text: '指南', link: '/guide/getting-started' },
      { text: '组件', link: '/components/charts' },
    ],
    sidebar: {
      '/guide/': [
        {
          text: '介绍',
          items: [
            { text: '快速开始', link: '/guide/getting-started' },
            { text: '项目结构', link: '/guide/project-structure' },
            { text: '开发规范', link: '/guide/development-guide' },
          ],
        },
      ],
      '/components/': [
        {
          text: '基础组件',
          items: [
            { text: '图表组件', link: '/components/charts' },
            { text: '数据表格', link: '/components/table' },
            { text: '表单组件', link: '/components/form' },
            { text: '统计数据', link: '/components/statistic-text' },
            { text: '同比/环比', link: '/components/comparison' },
            { text: 'RadioGroup', link: '/components/radio-group' },
          ],
        },
      ],
    },
    socialLinks: [
      {
        icon: 'gitlab',
        link: 'https://gitlab.hrttest.cn/frontend/web/data-vista',
      },
    ],
    footer: {
      message: '基于 MIT 许可发布',
      copyright: 'Copyright © 2024-present Data Vista',
    },
  },
  vite: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, '../../src'),
      },
    },
    css: {
      postcss: {
        plugins: [tailwindcss, autoprefixer],
      },
    },
  },
});
