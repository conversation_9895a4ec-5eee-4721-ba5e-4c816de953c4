# 开发规范

## 开发环境

### 环境要求

- Node.js >= 16.14.0
- pnpm >= 8.6.10
- VS Code（推荐）

### 推荐 VS Code 插件

- Volar (Vue 3)
- TypeScript Vue Plugin
- ESLint
- Prettier
- Tailwind CSS IntelliSense

## 开发流程

### 1. 克隆项目

```bash
git clone https://github.com/your-org/data-vista.git
cd data-vista
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 启动开发服务器

```bash
pnpm dev
```

### 4. 代码提交

```bash
# 创建新分支
git checkout -b feature/your-feature

# 提交代码
git add .
git commit -m "feat: your feature description"

# 推送到远程
git push origin feature/your-feature
```

## 代码规范

### Vue 组件规范

1. 组件命名
   - 使用 PascalCase
   - 以功能命名，如 `DataChart.vue`
   - 基础组件以 Base 开头，如 `BaseButton.vue`

2. 组件结构
   ```vue
   <template>
     <div class="component-name">
       <!-- 模板内容 -->
     </div>
   </template>

   <script setup lang="ts">
   // 导入
   import { ref, onMounted } from 'vue'

   // 类型定义
   interface Props {
     title: string
     data: ChartData[]
   }

   // Props 定义
   const props = defineProps<Props>()

   // 响应式数据
   const loading = ref(false)

   // 方法定义
   const handleClick = () => {
     // 处理逻辑
   }

   // 生命周期
   onMounted(() => {
     // 初始化逻辑
   })
   </script>

   <style scoped lang="less">
   .component-name {
     // 样式定义
   }
   </style>
   ```

### TypeScript 规范

1. 类型定义
   - 使用 interface 定义对象类型
   - 使用 type 定义联合类型
   - 使用 enum 定义枚举类型

2. 类型导入
   ```typescript
   // 推荐
   import type { ChartData } from '@/types'

   // 不推荐
   import { ChartData } from '@/types'
   ```

### 样式规范

1. 使用 Less 预处理器
2. 使用 BEM 命名规范
3. 优先使用 Tailwind CSS 类名
4. 组件样式使用 scoped

### Git 提交规范

提交信息格式：
```
<type>(<scope>): <subject>

<body>

<footer>
```

type 类型：
- feat: 新功能
- fix: 修复
- docs: 文档
- style: 格式
- refactor: 重构
- test: 测试
- chore: 构建

示例：
```
feat(chart): add line chart component

- Add basic line chart implementation
- Support data animation
- Add tooltip interaction

Closes #123
```

## 测试规范

### 单元测试

- 使用 Vitest 进行单元测试
- 测试文件以 `.test.ts` 或 `.spec.ts` 结尾
- 测试覆盖率要求 > 80%

### E2E 测试

- 使用 Cypress 进行 E2E 测试
- 测试文件放在 `cypress/e2e` 目录下
- 关键流程必须进行 E2E 测试

## 文档规范

### 组件文档

- 使用 VitePress 编写文档
- 包含组件描述、属性说明、事件说明、示例代码
- 提供在线演示

### 注释规范

- 使用 JSDoc 格式注释
- 复杂逻辑必须添加注释
- 公共 API 必须添加注释

## 发布规范

### 版本号规范

遵循语义化版本（Semantic Versioning）：
- 主版本号：不兼容的 API 修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 发布流程

1. 更新版本号
2. 更新 CHANGELOG.md
3. 提交代码
4. 创建 Git Tag
5. 构建发布包
6. 发布到 npm 