# 快速开始

## 安装

使用 pnpm 安装 Data Vista：

```bash
pnpm add data-vista
```

## 使用

在你的 Vue 项目中引入并使用：

```vue
<template>
  <div>
    <DataChart :data="chartData" />
  </div>
</template>

<script setup>
import { DataChart } from 'data-vista'

const chartData = {
  // 你的数据
}
</script>
```

## 配置

在 `vite.config.ts` 中添加配置：

```ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  // 其他配置...
})
```

## 下一步

- 查看[项目结构](/guide/project-structure)了解项目组织
- 阅读[开发规范](/guide/development-guide)开始开发
- 浏览[组件文档](/components/)了解更多组件 