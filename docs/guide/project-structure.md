# 项目结构

## 目录结构

```
data-vista/
├── docs/                # 文档目录
│   ├── .vitepress/     # VitePress 配置
│   ├── guide/          # 指南文档
│   └── components/     # 组件文档
├── src/                # 源代码目录
│   ├── assets/        # 静态资源
│   ├── components/    # 公共组件
│   ├── composables/   # 组合式函数
│   ├── config/        # 配置文件
│   ├── constant/      # 常量定义
│   ├── hooks/         # 自定义钩子
│   ├── interface/     # 类型定义
│   ├── router/        # 路由配置
│   ├── store/         # 状态管理
│   ├── styles/        # 样式文件
│   ├── utils/         # 工具函数
│   ├── views/         # 页面组件
│   ├── App.vue        # 根组件
│   └── main.ts        # 入口文件
├── public/            # 公共资源
├── .vscode/          # VS Code 配置
├── .husky/           # Git Hooks
├── .env.*           # 环境变量
├── package.json     # 项目配置
├── tsconfig.json    # TypeScript 配置
├── vite.config.ts   # Vite 配置
└── tailwind.config.js # Tailwind 配置
```

## 主要目录说明

### src 目录

- `assets/`: 存放项目资源文件，如图片、字体等
- `components/`: 存放可复用的 Vue 组件
- `composables/`: 存放可复用的组合式函数
- `config/`: 存放项目配置文件
- `constant/`: 存放常量定义
- `hooks/`: 存放自定义的 React Hooks
- `interface/`: 存放 TypeScript 类型定义
- `router/`: 存放路由配置
- `store/`: 存放 Pinia 状态管理
- `styles/`: 存放全局样式文件
- `utils/`: 存放工具函数
- `views/`: 存放页面级组件

### 配置文件

- `package.json`: 项目依赖和脚本配置
- `tsconfig.json`: TypeScript 编译配置
- `vite.config.ts`: Vite 构建工具配置
- `tailwind.config.js`: Tailwind CSS 配置
- `.env.*`: 不同环境的变量配置

## 开发规范

### 命名规范

- 组件名：使用 PascalCase（如 `DataChart.vue`）
- 文件名：使用 kebab-case（如 `data-chart.ts`）
- 变量名：使用 camelCase（如 `chartData`）
- 常量名：使用 UPPER_SNAKE_CASE（如 `MAX_DATA_POINTS`）

### 目录规范

- 组件目录：使用 index.vue 作为主文件
- 工具函数：按功能模块组织
- 类型定义：集中管理在 interface 目录

### 代码规范

- 使用 TypeScript 进行类型检查
- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 遵循 Vue 3 组合式 API 风格 