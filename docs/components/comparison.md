# Comparison 同比环比组件

用于展示数据的同比和环比变化情况。

## 基础用法

使用 `Comparison` 组件展示同比和环比数据。

```vue
<template>
  <Comparison :yoy="12.5" :mom="-5.2" />
</template>
```

## 基础用法

<demo vue="comparison/base.vue" scope="vue"></demo>

## 全部为正数

当同比和环比都为正数时的展示效果。

<demo vue="comparison/positive.vue" scope="vue"></demo>

## 全部为负数

当同比和环比都为负数时的展示效果。

<demo vue="comparison/negative.vue" scope="vue"></demo>

## API

### Props

| 参数           | 说明             | 类型    | 默认值 |
| -------------- | ---------------- | ------- | ------ |
| yoy            | 同比数据         | number  | -      |
| mom            | 环比数据         | number  | -      |
| itemWidth      | 每项的宽度       | string  | -      |
| hideYoy        | 是否隐藏同比数据 | boolean | false  |
| inline         | 是否同一行展示   | boolean | false  |
| fractionDigits | 保留几位小数     | number  | 2      |

### 样式说明

- 当数值为正数时，显示绿色（#2FB324）
- 当数值为负数时，显示红色（#E63746）
- 增长使用向上箭头图标
- 下降使用向下箭头图标
