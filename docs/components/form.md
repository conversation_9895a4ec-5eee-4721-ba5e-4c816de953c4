# 表单组件

## 基础表单

### 基本用法

```vue
<template>
  <DataForm
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="100px"
  >
    <DataFormItem label="用户名" prop="username">
      <el-input v-model="formData.username" />
    </DataFormItem>
    <DataFormItem label="密码" prop="password">
      <el-input v-model="formData.password" type="password" />
    </DataFormItem>
    <DataFormItem>
      <el-button type="primary" @click="handleSubmit">提交</el-button>
      <el-button @click="handleReset">重置</el-button>
    </DataFormItem>
  </DataForm>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DataForm, DataFormItem } from 'data-vista'

const formRef = ref()
const formData = ref({
  username: '',
  password: ''
})

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate()
  console.log('表单数据：', formData.value)
}

const handleReset = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
</script>
```

### 动态表单

```vue
<template>
  <DataForm
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="100px"
  >
    <DataFormItem label="姓名" prop="name">
      <el-input v-model="formData.name" />
    </DataFormItem>
    <DataFormItem label="爱好" prop="hobbies">
      <el-select v-model="formData.hobbies" multiple>
        <el-option
          v-for="item in hobbyOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </DataFormItem>
    <DataFormItem label="备注" prop="remarks">
      <el-input
        v-model="formData.remarks"
        type="textarea"
        :rows="3"
      />
    </DataFormItem>
  </DataForm>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DataForm, DataFormItem } from 'data-vista'

const formRef = ref()
const formData = ref({
  name: '',
  hobbies: [],
  remarks: ''
})

const hobbyOptions = [
  { label: '阅读', value: 'reading' },
  { label: '运动', value: 'sports' },
  { label: '音乐', value: 'music' },
  { label: '旅行', value: 'travel' }
]

const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  hobbies: [
    { type: 'array', required: true, message: '请选择爱好', trigger: 'change' }
  ]
}
</script>
```

### 表单验证

```vue
<template>
  <DataForm
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="100px"
  >
    <DataFormItem label="邮箱" prop="email">
      <el-input v-model="formData.email" />
    </DataFormItem>
    <DataFormItem label="手机号" prop="phone">
      <el-input v-model="formData.phone" />
    </DataFormItem>
    <DataFormItem label="年龄" prop="age">
      <el-input-number v-model="formData.age" :min="0" :max="100" />
    </DataFormItem>
  </DataForm>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DataForm, DataFormItem } from 'data-vista'

const formRef = ref()
const formData = ref({
  email: '',
  phone: '',
  age: 18
})

const validateEmail = (rule: any, value: string, callback: Function) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(value)) {
    callback(new Error('请输入正确的邮箱地址'))
  } else {
    callback()
  }
}

const validatePhone = (rule: any, value: string, callback: Function) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(value)) {
    callback(new Error('请输入正确的手机号码'))
  } else {
    callback()
  }
}

const rules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { validator: validateEmail, trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' }
  ],
  age: [
    { required: true, message: '请输入年龄', trigger: 'blur' },
    { type: 'number', message: '年龄必须为数字', trigger: 'blur' }
  ]
}
</script>
```

## 高级功能

### 动态表单项

```vue
<template>
  <DataForm
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="100px"
  >
    <DataFormItem
      v-for="(field, index) in formFields"
      :key="index"
      :label="field.label"
      :prop="field.prop"
    >
      <component
        :is="field.component"
        v-model="formData[field.prop]"
        v-bind="field.props"
      />
    </DataFormItem>
  </DataForm>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DataForm, DataFormItem } from 'data-vista'

const formRef = ref()
const formData = ref({
  name: '',
  age: 18,
  gender: '',
  hobbies: []
})

const formFields = [
  {
    label: '姓名',
    prop: 'name',
    component: 'el-input'
  },
  {
    label: '年龄',
    prop: 'age',
    component: 'el-input-number',
    props: { min: 0, max: 100 }
  },
  {
    label: '性别',
    prop: 'gender',
    component: 'el-radio-group',
    props: {
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' }
      ]
    }
  },
  {
    label: '爱好',
    prop: 'hobbies',
    component: 'el-select',
    props: {
      multiple: true,
      options: [
        { label: '阅读', value: 'reading' },
        { label: '运动', value: 'sports' },
        { label: '音乐', value: 'music' }
      ]
    }
  }
]

const rules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }]
}
</script>
```

### 表单联动

```vue
<template>
  <DataForm
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="100px"
  >
    <DataFormItem label="省份" prop="province">
      <el-select v-model="formData.province" @change="handleProvinceChange">
        <el-option
          v-for="item in provinces"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </DataFormItem>
    <DataFormItem label="城市" prop="city">
      <el-select v-model="formData.city">
        <el-option
          v-for="item in cities"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </DataFormItem>
  </DataForm>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { DataForm, DataFormItem } from 'data-vista'

const formRef = ref()
const formData = ref({
  province: '',
  city: ''
})

const provinces = [
  { label: '北京', value: 'beijing' },
  { label: '上海', value: 'shanghai' },
  { label: '广东', value: 'guangdong' }
]

const cityMap = {
  beijing: [
    { label: '朝阳区', value: 'chaoyang' },
    { label: '海淀区', value: 'haidian' }
  ],
  shanghai: [
    { label: '浦东新区', value: 'pudong' },
    { label: '黄浦区', value: 'huangpu' }
  ],
  guangdong: [
    { label: '广州', value: 'guangzhou' },
    { label: '深圳', value: 'shenzhen' }
  ]
}

const cities = computed(() => {
  return cityMap[formData.value.province] || []
})

const handleProvinceChange = () => {
  formData.value.city = ''
}

const rules = {
  province: [{ required: true, message: '请选择省份', trigger: 'change' }],
  city: [{ required: true, message: '请选择城市', trigger: 'change' }]
}
</script>
```

## 组件属性

### DataForm 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| model | `object` | - | 表单数据对象 |
| rules | `object` | - | 表单验证规则 |
| labelWidth | `string` | '100px' | 标签宽度 |
| inline | `boolean` | false | 是否行内表单 |
| disabled | `boolean` | false | 是否禁用表单 |

### DataFormItem 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| label | `string` | - | 标签文本 |
| prop | `string` | - | 表单字段名 |
| rules | `object[]` | - | 表单项验证规则 |
| required | `boolean` | false | 是否必填 |
| disabled | `boolean` | false | 是否禁用 |

## 方法

### DataForm 方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| validate | `(callback?: Function) => Promise<boolean>` | 表单验证 |
| resetFields | `() => void` | 重置表单 |
| clearValidate | `(props?: string[]) => void` | 清除验证信息 |

### DataFormItem 方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| validate | `(callback?: Function) => Promise<boolean>` | 表单项验证 |
| resetField | `() => void` | 重置表单项 |
| clearValidate | `() => void` | 清除验证信息 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| validate | `(prop: string, valid: boolean, message: string) => void` | 表单项验证结果 |
| submit | `(formData: object) => void` | 表单提交 |

## 插槽

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| default | - | 表单项内容 |
| label | - | 标签内容 |
| error | `{ message: string }` | 错误提示内容 |

## 最佳实践

1. 表单验证
   - 合理使用验证规则
   - 自定义验证方法
   - 异步验证处理

2. 性能优化
   - 避免频繁验证
   - 合理使用计算属性
   - 优化大数据量表单

3. 用户体验
   - 提供清晰的错误提示
   - 优化表单布局
   - 添加表单说明

4. 代码组织
   - 抽离验证规则
   - 复用表单配置
   - 统一错误处理 