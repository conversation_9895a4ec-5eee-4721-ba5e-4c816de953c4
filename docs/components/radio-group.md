# RadioGroup 单选按钮组

用于在多个选项中选择一个选项。

<demo vue="radio-group/base.vue" scope="vue"></demo>

## 功能特性

- 支持自定义标签文本和样式
- 支持禁用状态
- 支持迷你尺寸
- 支持分割线
- 支持自定义数据源

## Props

| 属性名   | 类型                              | 默认值     | 说明               |
| -------- | --------------------------------- | ---------- | ------------------ |
| label    | string                            | -          | 标签文本           |
| data     | Array<{id: string, name: string}> | INDICATORS | 选项数据源         |
| value    | string                            | -          | 选中值             |
| color    | string                            | '#3A4762'  | 标签文本颜色       |
| bold     | boolean                           | false      | 标签文本是否加粗   |
| disabled | boolean                           | false      | 是否禁用           |
| hasLine  | boolean                           | false      | 是否显示底部分割线 |
| size     | 'mini' \| 'default'               | 'default'  | 按钮尺寸           |

## Events

| 事件名   | 说明             | 回调参数        |
| -------- | ---------------- | --------------- |
| onChange | 选中值变化时触发 | (value: string) |
