# 图表组件

## 基础图表

### 折线图

用于展示数据随时间或类别的变化趋势。

```vue
<template>
  <LineChart
    :data="lineData"
    :options="lineOptions"
    height="300px"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { LineChart } from 'data-vista'

const lineData = ref([
  { date: '2024-01', value: 150 },
  { date: '2024-02', value: 230 },
  { date: '2024-03', value: 180 },
  { date: '2024-04', value: 280 },
])

const lineOptions = {
  xAxis: {
    type: 'category',
    data: ['2024-01', '2024-02', '2024-03', '2024-04']
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    data: [150, 230, 180, 280],
    type: 'line',
    smooth: true
  }]
}
</script>
```

### 柱状图

用于展示不同类别的数据对比。

```vue
<template>
  <BarChart
    :data="barData"
    :options="barOptions"
    height="300px"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { BarChart } from 'data-vista'

const barData = ref([
  { category: 'A', value: 120 },
  { category: 'B', value: 200 },
  { category: 'C', value: 150 },
  { category: 'D', value: 80 },
])

const barOptions = {
  xAxis: {
    type: 'category',
    data: ['A', 'B', 'C', 'D']
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    data: [120, 200, 150, 80],
    type: 'bar'
  }]
}
</script>
```

### 饼图

用于展示数据占比。

```vue
<template>
  <PieChart
    :data="pieData"
    :options="pieOptions"
    height="300px"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { PieChart } from 'data-vista'

const pieData = ref([
  { name: 'A', value: 335 },
  { name: 'B', value: 310 },
  { name: 'C', value: 234 },
  { name: 'D', value: 135 },
])

const pieOptions = {
  series: [{
    type: 'pie',
    radius: '50%',
    data: [
      { value: 335, name: 'A' },
      { value: 310, name: 'B' },
      { value: 234, name: 'C' },
      { value: 135, name: 'D' }
    ]
  }]
}
</script>
```

## 高级图表

### 散点图

用于展示两个变量之间的关系。

```vue
<template>
  <ScatterChart
    :data="scatterData"
    :options="scatterOptions"
    height="300px"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ScatterChart } from 'data-vista'

const scatterData = ref([
  { x: 10, y: 20 },
  { x: 20, y: 30 },
  { x: 30, y: 40 },
  { x: 40, y: 50 },
])

const scatterOptions = {
  xAxis: {
    type: 'value'
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    type: 'scatter',
    data: [
      [10, 20],
      [20, 30],
      [30, 40],
      [40, 50]
    ]
  }]
}
</script>
```

### 雷达图

用于展示多维度的数据对比。

```vue
<template>
  <RadarChart
    :data="radarData"
    :options="radarOptions"
    height="300px"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { RadarChart } from 'data-vista'

const radarData = ref([
  { name: 'A', value: 80 },
  { name: 'B', value: 90 },
  { name: 'C', value: 70 },
  { name: 'D', value: 85 },
])

const radarOptions = {
  radar: {
    indicator: [
      { name: 'A', max: 100 },
      { name: 'B', max: 100 },
      { name: 'C', max: 100 },
      { name: 'D', max: 100 }
    ]
  },
  series: [{
    type: 'radar',
    data: [{
      value: [80, 90, 70, 85]
    }]
  }]
}
</script>
```

## 组件属性

### 通用属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| data | `object[]` | - | 图表数据 |
| options | `object` | - | 图表配置项 |
| height | `string` | '300px' | 图表高度 |
| width | `string` | '100%' | 图表宽度 |
| loading | `boolean` | false | 是否显示加载状态 |
| theme | `string` | 'light' | 主题，可选 'light' 或 'dark' |

### 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | `(params: object) => void` | 点击图表元素时触发 |
| hover | `(params: object) => void` | 鼠标悬停在图表元素上时触发 |
| legendselectchanged | `(params: object) => void` | 图例选择状态改变时触发 |

## 最佳实践

1. 数据格式
   - 确保数据格式统一
   - 处理空值和异常值
   - 合理使用数据转换

2. 性能优化
   - 大数据量时使用分页或虚拟滚动
   - 合理设置更新频率
   - 避免不必要的重绘

3. 交互设计
   - 添加适当的提示信息
   - 实现缩放和拖拽功能
   - 支持数据筛选和排序

4. 样式定制
   - 使用主题配置
   - 自定义图表样式
   - 保持视觉一致性 