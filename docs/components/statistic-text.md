# StatisticText 统计组件

显示统计数据。

## 基础用法

用于突出某个或某组数字时，如统计数值、金额、排名等。支持数值的变化动效。

<demo vue="statistic-text/base.vue" scope="vue"></demo>

## API

### 属性

| 属性      | 描述             | 类型      | 默认值 |
| --------- | ---------------- | --------- | ------ |
| value     | 数字内容         | `number`  | 0      |
| title     | 数字标题         | `string`  | --     |
| animation | 是否开启动画效果 | `boolean` | false  |

### 插槽

| 插槽名 | 详情       |
| ------ | ---------- |
| prefix | 数字区之前 |
| suffix | 数字区之后 |
