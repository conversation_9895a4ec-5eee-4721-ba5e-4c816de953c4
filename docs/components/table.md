# 数据表格

## 基础表格

### 基本用法

```vue
<template>
  <DataTable
    :columns="columns"
    :data="tableData"
    :loading="loading"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DataTable } from 'data-vista'

const loading = ref(false)
const columns = ref([
  { prop: 'name', label: '姓名' },
  { prop: 'age', label: '年龄' },
  { prop: 'address', label: '地址' },
  { prop: 'date', label: '日期' }
])

const tableData = ref([
  {
    name: '张三',
    age: 18,
    address: '北京市朝阳区',
    date: '2024-03-31'
  },
  {
    name: '李四',
    age: 20,
    address: '上海市浦东新区',
    date: '2024-03-31'
  }
])
</script>
```

### 带分页的表格

```vue
<template>
  <DataTable
    :columns="columns"
    :data="tableData"
    :pagination="pagination"
    @page-change="handlePageChange"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DataTable } from 'data-vista'

const columns = ref([
  { prop: 'name', label: '姓名' },
  { prop: 'age', label: '年龄' },
  { prop: 'address', label: '地址' }
])

const tableData = ref([
  {
    name: '张三',
    age: 18,
    address: '北京市朝阳区'
  },
  // ... 更多数据
])

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 100
})

const handlePageChange = (page: number) => {
  pagination.value.current = page
  // 加载对应页的数据
}
</script>
```

### 可排序表格

```vue
<template>
  <DataTable
    :columns="columns"
    :data="tableData"
    :sort-config="sortConfig"
    @sort-change="handleSortChange"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DataTable } from 'data-vista'

const columns = ref([
  { prop: 'name', label: '姓名', sortable: true },
  { prop: 'age', label: '年龄', sortable: true },
  { prop: 'score', label: '分数', sortable: true }
])

const sortConfig = ref({
  prop: 'age',
  order: 'ascending'
})

const handleSortChange = (sort: { prop: string; order: string }) => {
  sortConfig.value = sort
  // 根据排序条件重新加载数据
}
</script>
```

### 可筛选表格

```vue
<template>
  <DataTable
    :columns="columns"
    :data="tableData"
    :filter-config="filterConfig"
    @filter-change="handleFilterChange"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DataTable } from 'data-vista'

const columns = ref([
  { prop: 'name', label: '姓名', filterable: true },
  { prop: 'age', label: '年龄', filterable: true },
  { prop: 'status', label: '状态', filterable: true }
])

const filterConfig = ref({
  name: '',
  age: '',
  status: ''
})

const handleFilterChange = (filters: Record<string, string>) => {
  filterConfig.value = filters
  // 根据筛选条件重新加载数据
}
</script>
```

## 高级功能

### 自定义列模板

```vue
<template>
  <DataTable :columns="columns" :data="tableData">
    <template #action="{ row }">
      <el-button type="primary" @click="handleEdit(row)">编辑</el-button>
      <el-button type="danger" @click="handleDelete(row)">删除</el-button>
    </template>
  </DataTable>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DataTable } from 'data-vista'

const columns = ref([
  { prop: 'name', label: '姓名' },
  { prop: 'age', label: '年龄' },
  { prop: 'action', label: '操作', slot: 'action' }
])

const handleEdit = (row: any) => {
  console.log('编辑', row)
}

const handleDelete = (row: any) => {
  console.log('删除', row)
}
</script>
```

### 树形数据

```vue
<template>
  <DataTable
    :columns="columns"
    :data="treeData"
    row-key="id"
    :tree-props="{ children: 'children' }"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DataTable } from 'data-vista'

const columns = ref([
  { prop: 'name', label: '名称' },
  { prop: 'type', label: '类型' },
  { prop: 'size', label: '大小' }
])

const treeData = ref([
  {
    id: 1,
    name: '文件夹1',
    type: 'folder',
    children: [
      {
        id: 2,
        name: '文件1',
        type: 'file',
        size: '1MB'
      }
    ]
  }
])
</script>
```

## 组件属性

### 基础属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| columns | `object[]` | - | 表格列配置 |
| data | `object[]` | - | 表格数据 |
| loading | `boolean` | false | 是否显示加载状态 |
| border | `boolean` | false | 是否显示边框 |
| stripe | `boolean` | false | 是否显示斑马纹 |
| height | `string` | - | 表格高度 |

### 分页属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| pagination | `object` | - | 分页配置 |
| pagination.current | `number` | 1 | 当前页码 |
| pagination.pageSize | `number` | 10 | 每页条数 |
| pagination.total | `number` | 0 | 总条数 |

### 排序属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| sortConfig | `object` | - | 排序配置 |
| sortConfig.prop | `string` | - | 排序字段 |
| sortConfig.order | `string` | - | 排序方向 |

### 筛选属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| filterConfig | `object` | - | 筛选配置 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| page-change | `(page: number) => void` | 页码改变时触发 |
| sort-change | `(sort: object) => void` | 排序改变时触发 |
| filter-change | `(filters: object) => void` | 筛选条件改变时触发 |
| selection-change | `(selection: object[]) => void` | 选择项改变时触发 |

## 插槽

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| default | `{ row, column, index }` | 自定义单元格内容 |
| header | `{ column }` | 自定义表头内容 |
| empty | - | 空数据时的内容 |

## 最佳实践

1. 数据加载
   - 使用 loading 状态
   - 实现数据缓存
   - 优化大数据量渲染

2. 性能优化
   - 使用虚拟滚动
   - 合理设置分页大小
   - 避免频繁更新

3. 交互体验
   - 添加操作反馈
   - 优化加载状态
   - 提供数据导出

4. 样式定制
   - 使用主题配置
   - 自定义表格样式
   - 保持视觉一致性 