<template>
  <div class="space-y-8">
    <!-- 基础用法 -->
    <div class="demo-block">
      <h3>基础用法</h3>
      <RadioGroup label="基础单选" @on-change="val => console.log(val)" />
    </div>

    <!-- 自定义数据 -->
    <div class="demo-block">
      <h3>自定义数据</h3>
      <RadioGroup
        label="自定义数据"
        :data="customData"
        @on-change="val => console.log(val)"
      />
    </div>

    <!-- 迷你尺寸 -->
    <div class="demo-block">
      <h3>迷你尺寸</h3>
      <RadioGroup
        label="迷你按钮"
        size="mini"
        @on-change="val => console.log(val)"
      />
    </div>

    <!-- 带分割线 -->
    <div class="demo-block">
      <h3>带分割线</h3>
      <RadioGroup
        label="带分割线"
        has-line
        @on-change="val => console.log(val)"
      />
    </div>

    <!-- 禁用状态 -->
    <div class="demo-block">
      <h3>禁用状态</h3>
      <RadioGroup
        label="禁用状态"
        disabled
        @on-change="val => console.log(val)"
      />
    </div>

    <!-- 自定义样式 -->
    <div class="demo-block">
      <h3>自定义样式</h3>
      <RadioGroup
        label="自定义样式"
        color="#1890ff"
        bold
        @on-change="val => console.log(val)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import RadioGroup from '@/components/RadioGroup/index.vue';

const customData = [
  { id: '1', name: '选项一' },
  { id: '2', name: '选项二' },
  { id: '3', name: '选项三' },
];
</script>

<style scoped>
.demo-block {
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 4px;
}

h3 {
  margin: 0 0 16px;
  font-size: 16px;
  font-weight: 500;
}
</style>
