# workstation-web

> Vite + Vue3 + TypeScript + Pinia + Vue-router + vueuse + tailwindcss + element-plus + echarts + lodash-es

## 介绍

数据面板

## 安装

```bash
$ pnpm i      # 推荐使用pnpm，不推荐npm
$ pnpm dev    # 本地运行
$ pnpm build  # 打包
```

## 目录

- src
  - @types // 全局类型
  - api // api 接口
  - assets // 静态资源
  - components // 全局组件
  - constant // 全局常量
  - interface // 全局接口类型
  - network // 请求封装
  - pages // 路由页面
  - router // 路由配置
  - store // 全局 store
  - styles // 基础样式配置0
  - utils // 工具函数
  - App.vue // 应用入口容器
  - main.ts // 应用初始入口

## 自动 import

```js
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';

// 配置了 unplugin-auto-import  unplugin-vue-components 插件，上面的不用 import 引入，直接使用就行、
```

## icon 使用方式

```js
// 官方的建议, 使用<el-icon>包裹, 否则在某些 element 组件嵌套使用时，样式可能会有错
<el-icon>
  <i-ep-plus />
</el-icon>
```

## 约定

- event bus 只作为通信手段，不传递数据， 数据流统一在 pinia 里管理
- 定义在 store/module 里的 store 都建议以 use 开头
- 统一在 store/index.ts 里引入， 对外 export

## 特殊点

- 获取环境变量方式 import.meta.env
