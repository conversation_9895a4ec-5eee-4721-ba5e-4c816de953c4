{"name": "data-vista", "type": "module", "version": "1.0.0", "private": true, "packageManager": "pnpm@10.12.1", "engines": {"node": ">=22.16.0", "pnpm": ">=10.12.1", "npm": ">=10.9.2"}, "scripts": {"dev": "vite --host", "build": "vite build", "build-test": "vite build --mode test", "build-uat": "vite build --mode uat", "preview": "vite preview", "check": "eslint ./src", "ts-check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "interface": "node ./src/interface/generate.js & eslint --fix ./src/interface/type.d.ts", "docs:dev": "vitepress dev docs --host", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tanstack/vue-query": "^5.74.7", "@types/lodash-es": "^4.17.12", "@vueuse/components": "^13.3.0", "@vueuse/core": "^13.1.0", "@vueuse/router": "^13.1.0", "axios": "^1.9.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.6.0", "element-plus": "^2.9.10", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "vue": "^3.5.14", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1", "vue3-tree-org": "^4.2.2", "zod": "^4.0.0-beta.20250420T053007"}, "devDependencies": {"@antfu/eslint-config": "^4.13.2", "@iconify-json/ep": "^1.1.14", "@tanstack/vue-query-devtools": "^5.74.3", "@types/crypto-js": "^4.2.2", "@types/lodash": "4.17.17", "@types/node": "^20.10.4", "@vitejs/plugin-vue": "^4.5.0", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.16", "eslint": "9.27.0", "eslint-import-resolver-node": "^0.3.9", "eslint-plugin-format": "^1.0.1", "eslint-plugin-tailwindcss": "^3.18.0", "husky": "^9.1.7", "less": "^4.3.0", "lint-staged": "^15.2.0", "mitt": "^3.0.1", "postcss": "^8.4.32", "postcss-nesting": "^13.0.1", "rollup": "^4.41.0", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "unplugin-auto-import": "^19.1.2", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.4.1", "vite": "5.4.11", "vite-plugin-vue-devtools": "^7.7.2", "vitepress": "^1.6.3", "vitepress-demo-plugin": "^1.4.0", "vue-tsc": "^2.2.10"}, "lint-staged": {"src/**/*.{json,ts,vue}": ["eslint --fix"]}}