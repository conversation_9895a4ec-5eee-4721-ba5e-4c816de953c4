{"name": "data-vista", "type": "module", "version": "1.0.0", "private": true, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39", "engines": {"node": ">=16.14.0", "pnpm": ">=8.6.10"}, "scripts": {"dev": "vite --host", "build": "vite build", "build-test": "vite build --mode test", "build-uat": "vite build --mode uat", "preview": "vite preview", "check": "eslint ./src", "ts-check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "interface": "node ./src/interface/generate.js & eslint --fix ./src/interface/type.d.ts"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tanstack/vue-query": "^5.74.3", "@types/lodash-es": "^4.17.12", "@vueuse/core": "^13.1.0", "@vueuse/router": "^13.1.0", "axios": "^1.6.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "2.7.6", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-echarts": "^7.0.3", "vue-router": "^4.2.5"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@iconify-json/ep": "^1.1.14", "@tanstack/vue-query-devtools": "^5.74.3", "@types/node": "^20.10.4", "@vitejs/plugin-vue": "^4.5.0", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.16", "eslint": "9.26.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-tailwindcss": "^3.18.0", "husky": "^9.1.7", "less": "^4.2.0", "lint-staged": "^15.2.0", "mitt": "^3.0.1", "postcss": "^8.4.32", "postcss-nesting": "^12.0.1", "rollup": "^4.8.0", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "unplugin-auto-import": "^0.17.2", "unplugin-icons": "^0.18.1", "unplugin-vue-components": "^0.26.0", "vite": "5.4.11", "vue-tsc": "^2.1.10"}, "lint-staged": {"src/**/*.{json,ts,vue}": ["eslint"]}}